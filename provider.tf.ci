terraform {
  required_version = ">= 1.0.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = ">= 2.0.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = ">= 2.0.0"
    }
    null = {
      source  = "hashicorp/null"
      version = ">= 3.0.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = ">= 2.0.0"
    }
    random = {
      source  = "hashicorp/random"
      version = ">= 3.0.0"
    }
    tls = {
      source  = "hashicorp/tls"
      version = ">= 3.0.0"
    }
    time = {
      source  = "hashicorp/time"
      version = ">= 0.9.0"
    }
    http = {
      source  = "hashicorp/http"
      version = ">= 3.0.0"
    }
    external = {
      source  = "hashicorp/external"
      version = ">= 2.0.0"
    }
    kubectl = {
      source  = "gavinbunney/kubectl"
      version = ">= 1.14.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
  default_tags {
    tags = {
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Provider for disaster recovery region
provider "aws" {
  alias  = "dr_region"
  region = "eu-west-1" # Ireland region for disaster recovery
  default_tags {
    tags = {
      Environment = var.environment
      ManagedBy   = "Terraform"
      Purpose     = "DisasterRecovery"
    }
  }
}

# Alias for disaster recovery provider (used by disaster_recovery module)
provider "aws" {
  alias  = "dr"
  region = "eu-west-1" # Ireland region for disaster recovery
  default_tags {
    tags = {
      Environment = var.environment
      ManagedBy   = "Terraform"
      Purpose     = "DisasterRecovery"
    }
  }
}

# CI-specific: Skip Kubernetes provider configuration to avoid connection issues
# The Kubernetes resources will be skipped in CI using skip_kubernetes_resources = true

# Placeholder Kubernetes provider for CI - will not be used due to skip flags
provider "kubernetes" {
  # Use a placeholder configuration that won't cause connection errors
  config_path = "/dev/null"
  
  # Alternative: Use exec plugin for authentication (commented out for CI)
  # exec {
  #   api_version = "client.authentication.k8s.io/v1beta1"
  #   command     = "aws"
  #   args        = ["eks", "get-token", "--cluster-name", var.cluster_name]
  # }
}

# Add the safe alias for Kubernetes provider
provider "kubernetes" {
  alias = "safe"
  # Use a placeholder configuration that won't cause connection errors
  config_path = "/dev/null"
}

# Placeholder Helm provider for CI - will not be used due to skip flags
provider "helm" {
  kubernetes {
    config_path = "/dev/null"
  }
}

# Add the safe alias for Helm provider
provider "helm" {
  alias = "safe"
  kubernetes {
    config_path = "/dev/null"
  }
}

# Keep your availability_zones variable here since it's already referenced
variable "availability_zones" {
  description = "List of availability zones to use in the region"
  type        = list(string)
  default     = ["eu-central-1a", "eu-central-1b", "eu-central-1c"]
}
