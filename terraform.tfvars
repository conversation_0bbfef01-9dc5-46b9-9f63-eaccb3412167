# Environment setting
environment = "production"

# EKS Cluster Configuration
cluster_name = "production-wks"

# Override variables for Kubernetes connectivity - cluster exists
skip_k8s_connection       = true # Set to true to skip Kubernetes connectivity
skip_kubernetes_resources = true # Set to true to skip Kubernetes resources
skip_tenant_resources     = true # Set to false after CRDs are deployed
check_if_cluster_exists   = true
eks_cluster_exists        = true
create_eks                = false # Cluster already exists
create_iam_role           = false # IAM role already exists
create_eks_log_group      = false # Log group already exists
create_security_groups    = false # Security groups already exist
create_node_groups        = false # Node groups already exist
create_iam_policies       = false # IAM policies already exist

# Enable using existing resources
import_existing_resources = false
skip_config_creation      = false
skip_problematic_modules  = false

# Enable core infrastructure components
enable_cloudtrail            = false
enable_inspector             = false
enable_bastion_ssh           = true
enable_vpc_endpoints         = true
enable_cloudwatch_encryption = false
enable_container_insights    = false

# RDS Configuration
rds_instance_class         = "db.t3.medium"
rds_engine_version         = "8.0"
db_backup_retention_period = 7
db_multi_az                = true
db_deletion_protection     = false

# EKS Node Configuration
desired_size = 2
min_size     = 1
max_size     = 4

# Tenant Configuration for Customer Dashboards
tenants = {
  tenant1 = {
    id                    = "tenant1"
    namespace_prefix      = "tenant"
    display_name          = "Tenant 1"
    description           = "First tenant for testing"
    rate_limit            = 2000
    security_threshold    = 80
    db_role               = "tenant1_role"
    db_password           = "tenant1_password"
    rds_arn               = "arn:aws:rds:eu-central-1:************:db:tenant1-db"
    efs_arn               = "arn:aws:elasticfilesystem:eu-central-1:************:file-system/fs-12345678"
    backup_retention_days = 30
    dr_team_role_arn      = "arn:aws:iam::************:role/tenant1-dr-team"
    dr_test_schedule      = "cron(0 0 ? * SUN *)"
    backup_vault_arn      = "arn:aws:backup:eu-central-1:************:backup-vault/tenant1-vault"
    rds_instance_arn      = "arn:aws:rds:eu-central-1:************:db:tenant1-db"
    ec2_instance_id       = "i-1234567890abcdef0"
    rds_instance_id       = "tenant1-db"
    efs_id                = "fs-12345678"
    ec2_cpu_threshold     = 80
    rds_cpu_threshold     = 80
    alb_arn_suffix        = "tenant1-alb"
    db_cpu_threshold      = 80
    db_instance           = "tenant1-db"
    waf_blocked_threshold = 100
    compliance_rules = [
      "AWS-GR_RESTRICTED_INCOMING_TRAFFIC",
      "AWS-GR_RESTRICTED_OUTGOING_TRAFFIC",
      "AWS-GR_RESTRICTED_SSH"
    ]
    # Add contact information
    contact = {
      name  = "Tenant 1 Admin"
      email = "<EMAIL>"
      phone = "******-0123"
    }
    # Add resource quota configuration
    resource_quota = {
      cpu      = "4"
      memory   = "8Gi"
      storage  = "100Gi"
      pods     = "50"
      services = "10"
    }
    # Add network policy configuration
    network_policy = {
      enabled            = true
      allowed_namespaces = ["kube-system", "monitoring"]
    }
    # Add database configuration
    database = {
      enabled      = true
      type         = "mysql"
      name         = "tenant1_db"
      storage_size = "20Gi"
      importFromS3 = false
      s3Bucket     = "architravetestdb"
      s3Key        = "architrave_1.45.2.sql"
    }
    # Add storage configuration
    storage = {
      enabled    = true
      type       = "s3"
      size       = "100Gi"
      bucketName = "tenant-tenant1-assets"
      encryption = true
    }
    # Add monitoring configuration
    monitoring = {
      enabled    = true
      grafana    = true
      prometheus = true
      alerting   = true
      loki       = true
      jaeger     = true
    }
    # Add ingress configuration
    ingress = {
      enabled      = true
      domain       = "tenant1.architrave-assets.com"
      tls          = true
      istioGateway = "tenant-gateway"
      mtls         = true
    }

    api_config = {
      rate_limit_per_minute = 1500
      burst_limit          = 3000
      quota_per_day        = 150000
      enable_caching       = true
      cache_ttl_seconds    = 300
      enable_compression   = true
      cors_enabled         = true
      cors_origins         = ["https://tenant1.architrave-assets.com", "https://app.tenant1.architrave-assets.com"]
    }

    service_mesh = {
      enable_injection = true
      traffic_policy = {
        load_balancer = "ROUND_ROBIN"
        connection_pool = {
          tcp = {
            max_connections = 20
            connect_timeout = "10s"
          }
          http = {
            http1_max_pending_requests = 20
            http2_max_requests        = 200
            max_requests_per_connection = 2
            max_retries               = 3
          }
        }
        outlier_detection = {
          consecutive_errors  = 5
          interval           = "30s"
          base_ejection_time = "30s"
          max_ejection_percent = 50
        }
      }
    }
  }
}

db_name     = "tenant_db"
db_host     = "tenant-db.cluster-123456789012.us-west-2.rds.amazonaws.com"
db_username = "admin"
db_password = "admin_password"

tags = {
  Environment = "production"
  Project     = "tenant-management"
  ManagedBy   = "terraform"
}

# Service Mesh Configuration
enable_service_mesh = true

# Autoscaling Configuration
enable_auto_scaling = true

# Advanced Monitoring Configuration
enable_monitoring = true

# Tenant Management Configuration
enable_tenant_management = true

# Ensure no deletion protection is enabled for testing

# Bastion Host Configuration
enable_bastion        = true
bastion_instance_type = "t3.small"
bastion_allowed_cidrs = ["10.0.0.0/16"] # Using VPC CIDR for security

# EKS Public Access Configuration
endpoint_public_access = true
public_access_cidrs    = [] # Will be populated with bastion host IP

aws_region = "eu-central-1"
region = "eu-central-1"
account_id = "************"
name = "tenant-management"

# EKS cluster endpoint from the existing cluster
eks_cluster_endpoint = "https://3DCD914B17E38238CC9BF848AA48F5C2.gr7.eu-central-1.eks.amazonaws.com"

# Additional required variables
domain_name = "architrave-assets.com"
vpc_cidr = "10.0.0.0/16"
public_subnets = ["********/24", "********/24", "********/24"]
private_subnets = ["*********/24", "*********/24", "*********/24"]

# Tenant operator configuration
operator_namespace = "tenant-operator"
create_operator_namespace = true
operator_image_repository = "tenant-operator"
operator_image_tag = "latest"
operator_replicas = 1
operator_log_level = "info"
operator_role_arn = ""
tenant_kms_key_arns = {}
database_secret_arn = ""
s3_bucket_name = ""
default_resource_quota_cpu = "2"
default_resource_quota_memory = "4Gi"
default_resource_quota_storage = "10Gi"
default_resource_quota_pods = "10"
prometheus_namespace = "monitoring"
grafana_namespace = "monitoring"

# Additional configuration
owner = "DevOps Team"
project = "Infrastructure"
cost_center = "Engineering"
data_classification = "Internal"
compliance_requirements = "SOC2"
enable_access_logs = false
allowed_cidr_blocks = ["10.0.0.0/16"]
bastion_public_ip = ""

# Operator resources
operator_resources = {
  requests = {
    cpu = "100m"
    memory = "128Mi"
  }
  limits = {
    cpu = "500m"
    memory = "512Mi"
  }
}

# Istio Service Mesh Configuration
istio_version = "1.20.1"
mesh_id = "production-mesh"
cluster_network = "production-network"
trace_sampling = 1.0
enable_istio_ingress_gateway = true
enable_istio_egress_gateway = false
enable_istio_mtls = true
istio_mtls_mode = "STRICT"

istio_pilot_resources = {
  requests = {
    cpu    = "500m"
    memory = "2Gi"
  }
  limits = {
    cpu    = "1000m"
    memory = "4Gi"
  }
}

istio_circuit_breaker = {
  max_connections                = 100
  connect_timeout               = "30s"
  http1_max_pending_requests    = 100
  http2_max_requests           = 100
  max_requests_per_connection  = 2
  max_retries                  = 3
  consecutive_gateway_errors   = 5
  interval                     = "30s"
  base_ejection_time          = "30s"
  max_ejection_percent        = 50
  min_health_percent          = 50
}

# API Management Configuration
api_gateway_type = "istio"

api_rate_limiting = {
  enabled               = true
  requests_per_minute   = 1000
  burst_limit          = 2000
  per_tenant_override  = true
}

api_versions = {
  strategy           = "path"
  default_version    = "v1"
  supported_versions = ["v1", "v2"]
  deprecation_policy = {
    notice_period_days = 90
    sunset_period_days = 180
  }
}

api_latency_threshold = 1000
api_error_rate_threshold = 10
