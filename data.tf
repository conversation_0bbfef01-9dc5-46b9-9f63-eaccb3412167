# This data source is now defined in main.tf

# Local variable to control whether to look up resources
locals {
  # Always look up resources
  lookup_resources = true

  # Check if EKS cluster name is provided
  eks_cluster_name_provided = var.cluster_name != null && var.cluster_name != ""

  # Get AWS account ID from caller identity
  aws_account_id = data.aws_caller_identity.current.account_id

  # Bastion instance details with improved lookups
  bastion_public_ip   = try(data.aws_instance.bastion_instance[0].public_ip, "")
  bastion_private_ip  = try(data.aws_instance.bastion_instance[0].private_ip, "")
  bastion_instance_id = try(data.aws_instance.bastion_instance[0].id, "")

  # EKS cluster details with improved lookups
  eks_cluster_endpoint = local.eks_cluster_name_provided ? try(data.aws_eks_cluster.eks_cluster[0].endpoint, "") : ""
  eks_cluster_id       = local.eks_cluster_name_provided ? try(data.aws_eks_cluster.eks_cluster[0].id, "") : ""
  eks_cluster_ca_cert  = local.eks_cluster_name_provided ? try(data.aws_eks_cluster.eks_cluster[0].certificate_authority[0].data, "") : ""

  # Certificate details with the actual certificate ARN as fallback
  domain_certificate_arn = try(data.aws_acm_certificate.domain_certificate[0].arn, "arn:aws:acm:eu-central-1:************:certificate/8d306c2f-62e2-42f4-a941-562ce65cda32")
}

# Improved bastion instance lookup
data "aws_instances" "bastion" {
  filter {
    name   = "tag:Name"
    values = ["${var.environment}-eks-bastion"]
  }

  filter {
    name   = "instance-state-name"
    values = ["running"]
  }
}

data "aws_instance" "bastion_instance" {
  count       = length(data.aws_instances.bastion.ids) > 0 ? 1 : 0
  instance_id = data.aws_instances.bastion.ids[0]
}

# EKS cluster lookup - only if cluster name is provided and exists
# We'll use the check_if_cluster_exists variable to control whether to look up the cluster
data "aws_eks_cluster" "eks_cluster" {
  count = local.eks_cluster_name_provided && var.eks_cluster_exists ? 1 : 0
  name  = var.cluster_name
}

data "aws_eks_cluster_auth" "cluster_auth" {
  count = local.eks_cluster_name_provided && var.eks_cluster_exists ? 1 : 0
  name  = var.cluster_name
}

# ACM certificate lookup
data "aws_acm_certificate" "domain_certificate" {
  count       = var.domain_name != "" ? 1 : 0
  domain      = "*.${var.domain_name}"
  statuses    = ["ISSUED"]
  most_recent = true
}

# Note: We're using the certificate ARN from the locals block if the lookup fails

# Output for bastion details
output "bastion_private_ip" {
  value       = local.bastion_private_ip
  description = "Private IP of the bastion host"
}

output "bastion_instance_id" {
  value       = local.bastion_instance_id
  description = "Instance ID of the bastion host"
}

output "cluster_endpoint" {
  value       = local.eks_cluster_endpoint
  description = "Endpoint for the EKS cluster"
}

output "cluster_id" {
  value       = local.eks_cluster_id
  description = "ID of the EKS cluster"
}

output "domain_certificate_arn" {
  value       = local.domain_certificate_arn
  description = "ARN of the domain certificate"
}

output "eks_cluster_endpoint" {
  value       = local.eks_cluster_endpoint
  description = "Endpoint for the EKS cluster"
}

output "eks_cluster_ca_cert" {
  value       = local.eks_cluster_ca_cert
  description = "Certificate authority data for the EKS cluster"
  sensitive   = true
}

output "eks_cluster_auth_token" {
  value       = local.eks_cluster_name_provided && length(data.aws_eks_cluster_auth.cluster_auth) > 0 ? data.aws_eks_cluster_auth.cluster_auth[0].token : ""
  description = "Authentication token for the EKS cluster"
  sensitive   = true
}
