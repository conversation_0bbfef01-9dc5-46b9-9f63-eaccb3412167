-- MySQL dump 10.13  Distrib 5.6.41, for Linux (x86_64)
--
-- Host: localhost    Database: architrave
-- ------------------------------------------------------
-- Server version	5.6.41

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

USE architrave;

--
-- Table structure for table `asset_downloads`
--
DROP TABLE IF EXISTS `asset_downloads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `asset_downloads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `filename` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `filesize` bigint(20) unsigned DEFAULT NULL,
  `filepath` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `type` smallint(6) NOT NULL,
  `status` smallint(6) NOT NULL,
  `expiry` datetime NOT NULL,
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_A6494C8F5DA1941` (`asset_id`),
  KEY `IDX_A6494C8FA76ED395` (`user_id`),
  CONSTRAINT `FK_A6494C8F5DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_A6494C8FA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `asset_downloads`
--

LOCK TABLES `asset_downloads` WRITE;
/*!40000 ALTER TABLE `asset_downloads` DISABLE KEYS */;
/*!40000 ALTER TABLE `asset_downloads` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `asset_meta_info`
--

DROP TABLE IF EXISTS `asset_meta_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `asset_meta_info` (
  `asset_id` int(11) NOT NULL,
  `rental_area` int(11) DEFAULT NULL,
  `vacant_area` int(11) DEFAULT NULL,
  `occupancy_rate` smallint(6) DEFAULT NULL,
  `rent_per_year` int(11) DEFAULT NULL,
  `number_of_tenants` smallint(6) DEFAULT NULL,
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`asset_id`),
  CONSTRAINT `FK_B515AA2E5DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `asset_meta_info`
--

LOCK TABLES `asset_meta_info` WRITE;
/*!40000 ALTER TABLE `asset_meta_info` DISABLE KEYS */;
/*!40000 ALTER TABLE `asset_meta_info` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `assets`
--

DROP TABLE IF EXISTS `assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_id` int(11) DEFAULT NULL,
  `state_id` int(11) NOT NULL,
  `identifier` varchar(31) COLLATE utf8_unicode_ci NOT NULL,
  `postcode` varchar(15) COLLATE utf8_unicode_ci NOT NULL,
  `street` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `city` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `country` varchar(2) COLLATE utf8_unicode_ci NOT NULL,
  `description` longtext COLLATE utf8_unicode_ci,
  `icon_file` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `content_updated` datetime NOT NULL,
  `in_operation` smallint(6) NOT NULL DEFAULT '0',
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  `transaction_ready_level` smallint(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `identifier_unique` (`identifier`),
  KEY `IDX_79D17D8E84337261` (`index_id`),
  CONSTRAINT `FK_79D17D8E84337261` FOREIGN KEY (`index_id`) REFERENCES `indices` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `assets`
--

LOCK TABLES `assets` WRITE;
/*!40000 ALTER TABLE `assets` DISABLE KEYS */;
/*!40000 ALTER TABLE `assets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `assets_dqa_workflows`
--

DROP TABLE IF EXISTS `assets_dqa_workflows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assets_dqa_workflows` (
  `asset_id` int(11) NOT NULL,
  `dqa_workflow_uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  PRIMARY KEY (`asset_id`,`dqa_workflow_uuid`),
  KEY `IDX_F634F7C35DA1941` (`asset_id`),
  KEY `IDX_F634F7C3A9C2448B` (`dqa_workflow_uuid`),
  CONSTRAINT `FK_F634F7C35DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_F634F7C3A9C2448B` FOREIGN KEY (`dqa_workflow_uuid`) REFERENCES `dqa_workflows` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `assets_dqa_workflows`
--

LOCK TABLES `assets_dqa_workflows` WRITE;
/*!40000 ALTER TABLE `assets_dqa_workflows` DISABLE KEYS */;
/*!40000 ALTER TABLE `assets_dqa_workflows` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `assets_responsible_users`
--

DROP TABLE IF EXISTS `assets_responsible_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `assets_responsible_users` (
  `asset_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`asset_id`,`user_id`),
  KEY `IDX_21CE96345DA1941` (`asset_id`),
  KEY `IDX_21CE9634A76ED395` (`user_id`),
  CONSTRAINT `FK_21CE96345DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_21CE9634A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `assets_responsible_users`
--

LOCK TABLES `assets_responsible_users` WRITE;
/*!40000 ALTER TABLE `assets_responsible_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `assets_responsible_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `disclaimer`
--

DROP TABLE IF EXISTS `disclaimer`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `disclaimer` (
  `id` int(11) NOT NULL,
  `content` longtext COLLATE utf8_unicode_ci NOT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `disclaimer`
--

LOCK TABLES `disclaimer` WRITE;
/*!40000 ALTER TABLE `disclaimer` DISABLE KEYS */;
/*!40000 ALTER TABLE `disclaimer` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `documents`
--

DROP TABLE IF EXISTS `documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `folder_id` int(11) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `locked_by_user` int(11) DEFAULT NULL,
  `last_edit_by` int(11) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `size` bigint(20) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_editable` tinyint(1) NOT NULL DEFAULT '0',
  `is_transaction_relevant` tinyint(1) NOT NULL DEFAULT '0',
  `preview_status` smallint(6) NOT NULL,
  `internet_media_type` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `page_count` smallint(6) DEFAULT NULL,
  `checksum` varchar(40) COLLATE utf8_unicode_ci DEFAULT NULL,
  `in_operation` smallint(6) NOT NULL DEFAULT '0',
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  `trashed` int(11) NOT NULL,
  `locked_date` datetime DEFAULT NULL,
  `last_edit_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `folder_id_name_unique` (`folder_id`,`name`,`trashed`),
  KEY `IDX_A2B07288162CB942` (`folder_id`),
  KEY `IDX_A2B07288A76ED395` (`user_id`),
  KEY `IDX_A2B07288EEFE6950` (`locked_by_user`),
  KEY `IDX_A2B07288D88646FF` (`last_edit_by`),
  KEY `folder_trashed_created_active` (`folder_id`,`trashed`,`created`,`is_active`),
  CONSTRAINT `FK_A2B07288162CB942` FOREIGN KEY (`folder_id`) REFERENCES `folders` (`id`),
  CONSTRAINT `FK_A2B07288A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_A2B07288D88646FF` FOREIGN KEY (`last_edit_by`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_A2B07288EEFE6950` FOREIGN KEY (`locked_by_user`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `documents`
--

LOCK TABLES `documents` WRITE;
/*!40000 ALTER TABLE `documents` DISABLE KEYS */;
/*!40000 ALTER TABLE `documents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_activities`
--

DROP TABLE IF EXISTS `dqa_activities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_activities` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `dqa_ticket_uuid` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `dqa_transition_instance_uuid` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `dqa_group_uuid` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `user_id` int(11) DEFAULT NULL,
  `dqa_primary_text_content_uuid` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `dqa_secondary_text_content_uuid` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `state` smallint(6) NOT NULL,
  `type` smallint(6) NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`),
  KEY `IDX_2D6212E27D584380` (`dqa_ticket_uuid`),
  KEY `IDX_2D6212E245A9387` (`dqa_transition_instance_uuid`),
  KEY `IDX_2D6212E260B6B6D1` (`dqa_group_uuid`),
  KEY `IDX_2D6212E2A76ED395` (`user_id`),
  KEY `IDX_2D6212E235F620B9` (`dqa_primary_text_content_uuid`),
  KEY `IDX_2D6212E2CD158219` (`dqa_secondary_text_content_uuid`),
  CONSTRAINT `FK_2D6212E235F620B9` FOREIGN KEY (`dqa_primary_text_content_uuid`) REFERENCES `dqa_text_contents` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `FK_2D6212E245A9387` FOREIGN KEY (`dqa_transition_instance_uuid`) REFERENCES `dqa_transition_instances` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `FK_2D6212E260B6B6D1` FOREIGN KEY (`dqa_group_uuid`) REFERENCES `dqa_groups` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `FK_2D6212E27D584380` FOREIGN KEY (`dqa_ticket_uuid`) REFERENCES `dqa_tickets` (`uuid`) ON DELETE CASCADE,
  CONSTRAINT `FK_2D6212E2A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FK_2D6212E2CD158219` FOREIGN KEY (`dqa_secondary_text_content_uuid`) REFERENCES `dqa_text_contents` (`uuid`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_activities`
--

LOCK TABLES `dqa_activities` WRITE;
/*!40000 ALTER TABLE `dqa_activities` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_activities` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_groups`
--

DROP TABLE IF EXISTS `dqa_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_groups` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `description` longtext COLLATE utf8_unicode_ci,
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_groups`
--

LOCK TABLES `dqa_groups` WRITE;
/*!40000 ALTER TABLE `dqa_groups` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_groups_users`
--

DROP TABLE IF EXISTS `dqa_groups_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_groups_users` (
  `dqa_group_uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `user_id` int(11) NOT NULL,
  PRIMARY KEY (`dqa_group_uuid`,`user_id`),
  KEY `IDX_BFB1032860B6B6D1` (`dqa_group_uuid`),
  KEY `IDX_BFB10328A76ED395` (`user_id`),
  CONSTRAINT `FK_BFB1032860B6B6D1` FOREIGN KEY (`dqa_group_uuid`) REFERENCES `dqa_groups` (`uuid`) ON DELETE CASCADE,
  CONSTRAINT `FK_BFB10328A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_groups_users`
--

LOCK TABLES `dqa_groups_users` WRITE;
/*!40000 ALTER TABLE `dqa_groups_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_groups_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_processes`
--

DROP TABLE IF EXISTS `dqa_processes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_processes` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `description` longtext COLLATE utf8_unicode_ci,
  `begin` datetime NOT NULL,
  `end` datetime NOT NULL,
  `can_receive_new_questions` tinyint(1) NOT NULL,
  `ticket_delay_medium` int(11) NOT NULL,
  `ticket_delay_high` int(11) NOT NULL,
  `start_tasks_satisfied` tinyint(1) NOT NULL DEFAULT '0',
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_processes`
--

LOCK TABLES `dqa_processes` WRITE;
/*!40000 ALTER TABLE `dqa_processes` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_processes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_stages`
--

DROP TABLE IF EXISTS `dqa_stages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_stages` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `dqa_workflow_template_uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `description` longtext COLLATE utf8_unicode_ci,
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`),
  KEY `IDX_8BE2E956292D8122` (`dqa_workflow_template_uuid`),
  CONSTRAINT `FK_8BE2E956292D8122` FOREIGN KEY (`dqa_workflow_template_uuid`) REFERENCES `dqa_workflow_templates` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_stages`
--

LOCK TABLES `dqa_stages` WRITE;
/*!40000 ALTER TABLE `dqa_stages` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_stages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_text_contents`
--

DROP TABLE IF EXISTS `dqa_text_contents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_text_contents` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `text` longtext COLLATE utf8_unicode_ci NOT NULL,
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_text_contents`
--

LOCK TABLES `dqa_text_contents` WRITE;
/*!40000 ALTER TABLE `dqa_text_contents` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_text_contents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_tickets`
--

DROP TABLE IF EXISTS `dqa_tickets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_tickets` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `dqa_process` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `dqa_workflow` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `current_dqa_stage` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `user_id` int(11) DEFAULT NULL,
  `dqa_group_uuid` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `question_dqa_activity` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `latest_answer_dqa_activity` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `approved_answer_dqa_activity` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `related_folder_id` int(11) DEFAULT NULL,
  `related_document_id` int(11) DEFAULT NULL,
  `related_dqa_ticket_uuid` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `locked_by` int(11) DEFAULT NULL,
  `state` smallint(6) NOT NULL,
  `locked_until` datetime DEFAULT NULL,
  `delay_medium_end` datetime DEFAULT NULL,
  `delay_high_end` datetime DEFAULT NULL,
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`),
  UNIQUE KEY `UNIQ_9C358CF7A65FD0B3` (`question_dqa_activity`),
  UNIQUE KEY `UNIQ_9C358CF76B61E2BE` (`latest_answer_dqa_activity`),
  UNIQUE KEY `UNIQ_9C358CF7F4AE42A2` (`approved_answer_dqa_activity`),
  KEY `IDX_9C358CF74E6E0995` (`dqa_process`),
  KEY `IDX_9C358CF7FC04BABD` (`dqa_workflow`),
  KEY `IDX_9C358CF7881D7DA3` (`current_dqa_stage`),
  KEY `IDX_9C358CF7A76ED395` (`user_id`),
  KEY `IDX_9C358CF760B6B6D1` (`dqa_group_uuid`),
  KEY `IDX_9C358CF7BEEDE559` (`related_folder_id`),
  KEY `IDX_9C358CF78D406B3` (`related_document_id`),
  KEY `IDX_9C358CF7F03016A9` (`related_dqa_ticket_uuid`),
  KEY `IDX_9C358CF7719C4CB1` (`locked_by`),
  CONSTRAINT `FK_9C358CF74E6E0995` FOREIGN KEY (`dqa_process`) REFERENCES `dqa_processes` (`uuid`) ON DELETE CASCADE,
  CONSTRAINT `FK_9C358CF760B6B6D1` FOREIGN KEY (`dqa_group_uuid`) REFERENCES `dqa_groups` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `FK_9C358CF76B61E2BE` FOREIGN KEY (`latest_answer_dqa_activity`) REFERENCES `dqa_activities` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `FK_9C358CF7719C4CB1` FOREIGN KEY (`locked_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FK_9C358CF7881D7DA3` FOREIGN KEY (`current_dqa_stage`) REFERENCES `dqa_stages` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `FK_9C358CF78D406B3` FOREIGN KEY (`related_document_id`) REFERENCES `documents` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FK_9C358CF7A65FD0B3` FOREIGN KEY (`question_dqa_activity`) REFERENCES `dqa_activities` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `FK_9C358CF7A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FK_9C358CF7BEEDE559` FOREIGN KEY (`related_folder_id`) REFERENCES `folders` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FK_9C358CF7F03016A9` FOREIGN KEY (`related_dqa_ticket_uuid`) REFERENCES `dqa_tickets` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `FK_9C358CF7F4AE42A2` FOREIGN KEY (`approved_answer_dqa_activity`) REFERENCES `dqa_activities` (`uuid`) ON DELETE SET NULL,
  CONSTRAINT `FK_9C358CF7FC04BABD` FOREIGN KEY (`dqa_workflow`) REFERENCES `dqa_workflows` (`uuid`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_tickets`
--

LOCK TABLES `dqa_tickets` WRITE;
/*!40000 ALTER TABLE `dqa_tickets` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_tickets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_transition_definitions`
--

DROP TABLE IF EXISTS `dqa_transition_definitions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_transition_definitions` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `parent_dqa_stage_uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `child_dqa_stage_uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `dqa_workflow_template_uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `behavior_question_update` tinyint(1) NOT NULL DEFAULT '0',
  `behavior_ticket_approve` tinyint(1) NOT NULL DEFAULT '0',
  `behavior_ticket_reject` tinyint(1) NOT NULL DEFAULT '0',
  `behavior_primary_text_keep` tinyint(1) NOT NULL DEFAULT '0',
  `is_transition_from_root` tinyint(1) NOT NULL DEFAULT '0',
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`),
  KEY `IDX_4B9FE7A295E4963F` (`parent_dqa_stage_uuid`),
  KEY `IDX_4B9FE7A27983335D` (`child_dqa_stage_uuid`),
  KEY `IDX_4B9FE7A2292D8122` (`dqa_workflow_template_uuid`),
  CONSTRAINT `FK_4B9FE7A2292D8122` FOREIGN KEY (`dqa_workflow_template_uuid`) REFERENCES `dqa_workflow_templates` (`uuid`) ON DELETE CASCADE,
  CONSTRAINT `FK_4B9FE7A27983335D` FOREIGN KEY (`child_dqa_stage_uuid`) REFERENCES `dqa_stages` (`uuid`) ON DELETE CASCADE,
  CONSTRAINT `FK_4B9FE7A295E4963F` FOREIGN KEY (`parent_dqa_stage_uuid`) REFERENCES `dqa_stages` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_transition_definitions`
--

LOCK TABLES `dqa_transition_definitions` WRITE;
/*!40000 ALTER TABLE `dqa_transition_definitions` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_transition_definitions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_transition_instances`
--

DROP TABLE IF EXISTS `dqa_transition_instances`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_transition_instances` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `dqa_transition_definition_uuid` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `dqa_workflow_uuid` char(36) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '(DC2Type:uuid)',
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`),
  KEY `IDX_AA15D43289568BC5` (`dqa_transition_definition_uuid`),
  KEY `IDX_AA15D432A9C2448B` (`dqa_workflow_uuid`),
  CONSTRAINT `FK_AA15D43289568BC5` FOREIGN KEY (`dqa_transition_definition_uuid`) REFERENCES `dqa_transition_definitions` (`uuid`) ON DELETE CASCADE,
  CONSTRAINT `FK_AA15D432A9C2448B` FOREIGN KEY (`dqa_workflow_uuid`) REFERENCES `dqa_workflows` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_transition_instances`
--

LOCK TABLES `dqa_transition_instances` WRITE;
/*!40000 ALTER TABLE `dqa_transition_instances` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_transition_instances` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_transition_instances_groups`
--

DROP TABLE IF EXISTS `dqa_transition_instances_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_transition_instances_groups` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `dqa_transition_instance_uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `dqa_group_uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `can_read_user_name` tinyint(1) NOT NULL DEFAULT '0',
  `can_create_primary_text` tinyint(1) NOT NULL DEFAULT '0',
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`),
  KEY `IDX_35DBD52245A9387` (`dqa_transition_instance_uuid`),
  KEY `IDX_35DBD52260B6B6D1` (`dqa_group_uuid`),
  CONSTRAINT `FK_35DBD52245A9387` FOREIGN KEY (`dqa_transition_instance_uuid`) REFERENCES `dqa_transition_instances` (`uuid`) ON DELETE CASCADE,
  CONSTRAINT `FK_35DBD52260B6B6D1` FOREIGN KEY (`dqa_group_uuid`) REFERENCES `dqa_groups` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_transition_instances_groups`
--

LOCK TABLES `dqa_transition_instances_groups` WRITE;
/*!40000 ALTER TABLE `dqa_transition_instances_groups` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_transition_instances_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_workflow_templates`
--

DROP TABLE IF EXISTS `dqa_workflow_templates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_workflow_templates` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `user_id` int(11) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `description` longtext COLLATE utf8_unicode_ci,
  `is_public` tinyint(1) NOT NULL,
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`),
  KEY `IDX_A362C84EA76ED395` (`user_id`),
  CONSTRAINT `FK_A362C84EA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_workflow_templates`
--

LOCK TABLES `dqa_workflow_templates` WRITE;
/*!40000 ALTER TABLE `dqa_workflow_templates` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_workflow_templates` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `dqa_workflows`
--

DROP TABLE IF EXISTS `dqa_workflows`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `dqa_workflows` (
  `uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `dqa_process_uuid` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `user_id` int(11) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `description` longtext COLLATE utf8_unicode_ci,
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`uuid`),
  KEY `IDX_AE220480C40743F6` (`dqa_process_uuid`),
  KEY `IDX_AE220480A76ED395` (`user_id`),
  CONSTRAINT `FK_AE220480A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `FK_AE220480C40743F6` FOREIGN KEY (`dqa_process_uuid`) REFERENCES `dqa_processes` (`uuid`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `dqa_workflows`
--

LOCK TABLES `dqa_workflows` WRITE;
/*!40000 ALTER TABLE `dqa_workflows` DISABLE KEYS */;
/*!40000 ALTER TABLE `dqa_workflows` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `export_assets`
--

DROP TABLE IF EXISTS `export_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `export_assets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `export_assets`
--

LOCK TABLES `export_assets` WRITE;
/*!40000 ALTER TABLE `export_assets` DISABLE KEYS */;
/*!40000 ALTER TABLE `export_assets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `export_documents`
--

DROP TABLE IF EXISTS `export_documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `export_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `document_id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `source_path` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `target_path` varchar(511) COLLATE utf8_unicode_ci NOT NULL,
  `status` smallint(6) NOT NULL DEFAULT '0',
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_2FB63438C33F7837` (`document_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `export_documents`
--

LOCK TABLES `export_documents` WRITE;
/*!40000 ALTER TABLE `export_documents` DISABLE KEYS */;
/*!40000 ALTER TABLE `export_documents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `export_folder_mappings`
--

DROP TABLE IF EXISTS `export_folder_mappings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `export_folder_mappings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `source_folder_id` int(11) NOT NULL,
  `target_folder_id` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `export_folder_mappings`
--

LOCK TABLES `export_folder_mappings` WRITE;
/*!40000 ALTER TABLE `export_folder_mappings` DISABLE KEYS */;
/*!40000 ALTER TABLE `export_folder_mappings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `export_target_folders`
--

DROP TABLE IF EXISTS `export_target_folders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `export_target_folders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `number` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `path` varchar(511) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_1147FF2596901F54` (`number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `export_target_folders`
--

LOCK TABLES `export_target_folders` WRITE;
/*!40000 ALTER TABLE `export_target_folders` DISABLE KEYS */;
/*!40000 ALTER TABLE `export_target_folders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `feedback`
--

DROP TABLE IF EXISTS `feedback`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `context` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `text` longtext COLLATE utf8_unicode_ci NOT NULL,
  `stack_trace` longtext COLLATE utf8_unicode_ci NOT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_D2294458A76ED395` (`user_id`),
  CONSTRAINT `FK_D2294458A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `feedback`
--

LOCK TABLES `feedback` WRITE;
/*!40000 ALTER TABLE `feedback` DISABLE KEYS */;
/*!40000 ALTER TABLE `feedback` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `folder_notifications`
--

DROP TABLE IF EXISTS `folder_notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `folder_notifications` (
  `user_id` int(11) NOT NULL,
  `folder_id` int(11) NOT NULL,
  PRIMARY KEY (`user_id`,`folder_id`),
  KEY `IDX_841789BAA76ED395` (`user_id`),
  KEY `IDX_841789BA162CB942` (`folder_id`),
  CONSTRAINT `FK_841789BA162CB942` FOREIGN KEY (`folder_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_841789BAA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `folder_notifications`
--

LOCK TABLES `folder_notifications` WRITE;
/*!40000 ALTER TABLE `folder_notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `folder_notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `folder_operations`
--

DROP TABLE IF EXISTS `folder_operations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `folder_operations` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `source_folder_id` int(11) NOT NULL,
  `new_parent_folder_id` int(11) DEFAULT NULL,
  `new_pre_folder_id` int(11) DEFAULT NULL,
  `action` varchar(6) COLLATE utf8_unicode_ci NOT NULL,
  `status` smallint(6) NOT NULL,
  `operation_relations` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:json_array)',
  `logs` longtext COLLATE utf8_unicode_ci COMMENT '(DC2Type:json_array)',
  `notification_status` smallint(6) NOT NULL,
  `created` datetime NOT NULL,
  `finished` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_C35A73C9A76ED395` (`user_id`),
  KEY `IDX_C35A73C97EB22A8C` (`source_folder_id`),
  KEY `IDX_C35A73C9C320BBF8` (`new_parent_folder_id`),
  KEY `IDX_C35A73C9BFC88DD5` (`new_pre_folder_id`),
  CONSTRAINT `FK_C35A73C97EB22A8C` FOREIGN KEY (`source_folder_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_C35A73C9A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_C35A73C9BFC88DD5` FOREIGN KEY (`new_pre_folder_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_C35A73C9C320BBF8` FOREIGN KEY (`new_parent_folder_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `folder_operations`
--

LOCK TABLES `folder_operations` WRITE;
/*!40000 ALTER TABLE `folder_operations` DISABLE KEYS */;
/*!40000 ALTER TABLE `folder_operations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `folders`
--

DROP TABLE IF EXISTS `folders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `folders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT NULL,
  `asset_id` int(11) DEFAULT NULL,
  `index_folder_id` int(11) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `number` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `lft` int(11) NOT NULL,
  `lvl` int(11) NOT NULL,
  `rgt` int(11) NOT NULL,
  `root` int(11) DEFAULT NULL,
  `is_transaction_relevant` tinyint(1) NOT NULL DEFAULT '0',
  `is_approved` tinyint(1) NOT NULL DEFAULT '0',
  `is_numbered` tinyint(1) NOT NULL DEFAULT '0',
  `in_operation` smallint(6) NOT NULL DEFAULT '0',
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  `trashed` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `parent_id_name_unique` (`parent_id`,`name`,`trashed`),
  KEY `IDX_FE37D30F727ACA70` (`parent_id`),
  KEY `IDX_FE37D30F5DA1941` (`asset_id`),
  KEY `IDX_FE37D30F26B9E38D` (`index_folder_id`),
  KEY `asset_trashed_active` (`asset_id`,`trashed`,`is_active`),
  CONSTRAINT `FK_FE37D30F26B9E38D` FOREIGN KEY (`index_folder_id`) REFERENCES `index_folders` (`id`),
  CONSTRAINT `FK_FE37D30F5DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`),
  CONSTRAINT `FK_FE37D30F727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `folders`
--

LOCK TABLES `folders` WRITE;
/*!40000 ALTER TABLE `folders` DISABLE KEYS */;
/*!40000 ALTER TABLE `folders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `group_folder_rights`
--

DROP TABLE IF EXISTS `group_folder_rights`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `group_folder_rights` (
  `folder_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  `asset_id` int(11) NOT NULL,
  `state_id` smallint(6) NOT NULL,
  `folder_write` tinyint(1) NOT NULL DEFAULT '0',
  `folder_delete` tinyint(1) NOT NULL DEFAULT '0',
  `folder_read` tinyint(1) NOT NULL DEFAULT '0',
  `folder_mark` tinyint(1) NOT NULL DEFAULT '0',
  `folder_approve` tinyint(1) NOT NULL DEFAULT '0',
  `folder_hint_read` tinyint(1) NOT NULL DEFAULT '0',
  `document_write` tinyint(1) NOT NULL DEFAULT '0',
  `document_delete` tinyint(1) NOT NULL DEFAULT '0',
  `document_list` tinyint(1) NOT NULL DEFAULT '0',
  `preview_watermark` tinyint(1) NOT NULL DEFAULT '0',
  `preview_original` tinyint(1) NOT NULL DEFAULT '0',
  `download_watermark` tinyint(1) NOT NULL DEFAULT '0',
  `download_original` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`folder_id`,`group_id`,`asset_id`,`state_id`),
  KEY `IDX_BB316537162CB942` (`folder_id`),
  KEY `IDX_BB316537FE54D947` (`group_id`),
  KEY `IDX_BB3165375DA1941` (`asset_id`),
  KEY `group_id_folder_read` (`group_id`,`folder_read`),
  CONSTRAINT `FK_BB316537162CB942` FOREIGN KEY (`folder_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_BB3165375DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`),
  CONSTRAINT `FK_BB316537FE54D947` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `group_folder_rights`
--

LOCK TABLES `group_folder_rights` WRITE;
/*!40000 ALTER TABLE `group_folder_rights` DISABLE KEYS */;
/*!40000 ALTER TABLE `group_folder_rights` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `groups`
--

DROP TABLE IF EXISTS `groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_group_id` int(11) DEFAULT NULL,
  `name` varchar(127) COLLATE utf8_unicode_ci NOT NULL,
  `description` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_F06D39705E237E06` (`name`),
  KEY `IDX_F06D3970DEF1FFA8` (`index_group_id`),
  CONSTRAINT `FK_F06D3970DEF1FFA8` FOREIGN KEY (`index_group_id`) REFERENCES `index_groups` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `groups`
--

LOCK TABLES `groups` WRITE;
/*!40000 ALTER TABLE `groups` DISABLE KEYS */;
/*!40000 ALTER TABLE `groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `groups_users`
--

DROP TABLE IF EXISTS `groups_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `groups_users` (
  `user_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  PRIMARY KEY (`user_id`,`group_id`),
  KEY `IDX_4520C24DA76ED395` (`user_id`),
  KEY `IDX_4520C24DFE54D947` (`group_id`),
  CONSTRAINT `FK_4520C24DA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_4520C24DFE54D947` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `groups_users`
--

LOCK TABLES `groups_users` WRITE;
/*!40000 ALTER TABLE `groups_users` DISABLE KEYS */;
/*!40000 ALTER TABLE `groups_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `history`
--

DROP TABLE IF EXISTS `history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `asset_id` int(11) DEFAULT NULL,
  `action` smallint(6) NOT NULL,
  `content` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:json_array)',
  `timestamp` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_27BA704BA76ED395` (`user_id`),
  KEY `IDX_27BA704B5DA1941` (`asset_id`),
  CONSTRAINT `FK_27BA704B5DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`),
  CONSTRAINT `FK_27BA704BA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `history`
--

LOCK TABLES `history` WRITE;
/*!40000 ALTER TABLE `history` DISABLE KEYS */;
/*!40000 ALTER TABLE `history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `history2groups`
--

DROP TABLE IF EXISTS `history2groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `history2groups` (
  `history_id` int(11) NOT NULL,
  `group_id` int(11) NOT NULL,
  PRIMARY KEY (`history_id`,`group_id`),
  KEY `IDX_C98358A61E058452` (`history_id`),
  KEY `IDX_C98358A6FE54D947` (`group_id`),
  CONSTRAINT `FK_C98358A61E058452` FOREIGN KEY (`history_id`) REFERENCES `history` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_C98358A6FE54D947` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `history2groups`
--

LOCK TABLES `history2groups` WRITE;
/*!40000 ALTER TABLE `history2groups` DISABLE KEYS */;
/*!40000 ALTER TABLE `history2groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `import_documents`
--

DROP TABLE IF EXISTS `import_documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `import_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `document_id` int(11) DEFAULT NULL,
  `asset_id` int(11) DEFAULT NULL,
  `folder_id` int(11) DEFAULT NULL,
  `status` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `xml_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `ims_data` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:json_array)',
  `type` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `original_created` datetime DEFAULT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  `error_traces` longtext COLLATE utf8_unicode_ci COMMENT '(DC2Type:json_array)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_FF71C634C33F7837` (`document_id`),
  KEY `IDX_FF71C6345DA1941` (`asset_id`),
  KEY `IDX_FF71C634162CB942` (`folder_id`),
  KEY `xml_name_index` (`xml_name`),
  CONSTRAINT `FK_FF71C634162CB942` FOREIGN KEY (`folder_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_FF71C6345DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_FF71C634C33F7837` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `import_documents`
--

LOCK TABLES `import_documents` WRITE;
/*!40000 ALTER TABLE `import_documents` DISABLE KEYS */;
/*!40000 ALTER TABLE `import_documents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `index_folder_hints`
--

DROP TABLE IF EXISTS `index_folder_hints`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `index_folder_hints` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_folder_id` int(11) DEFAULT NULL,
  `title` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `content` longtext COLLATE utf8_unicode_ci,
  `position` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_folder_hints_index_folder_position` (`index_folder_id`,`position`),
  KEY `IDX_DE5E71D326B9E38D` (`index_folder_id`),
  CONSTRAINT `FK_DE5E71D326B9E38D` FOREIGN KEY (`index_folder_id`) REFERENCES `index_folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `index_folder_hints`
--

LOCK TABLES `index_folder_hints` WRITE;
/*!40000 ALTER TABLE `index_folder_hints` DISABLE KEYS */;
/*!40000 ALTER TABLE `index_folder_hints` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `index_folders`
--

DROP TABLE IF EXISTS `index_folders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `index_folders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT NULL,
  `index_id` int(11) DEFAULT NULL,
  `is_numbered` tinyint(1) NOT NULL DEFAULT '0',
  `is_transaction_relevant` tinyint(1) NOT NULL DEFAULT '0',
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `number` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `lft` int(11) NOT NULL,
  `lvl` int(11) NOT NULL,
  `rgt` int(11) NOT NULL,
  `root` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_parent_id_name_unique` (`parent_id`,`name`),
  KEY `IDX_34948F7A727ACA70` (`parent_id`),
  KEY `IDX_34948F7A84337261` (`index_id`),
  CONSTRAINT `FK_34948F7A727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `index_folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_34948F7A84337261` FOREIGN KEY (`index_id`) REFERENCES `indices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `index_folders`
--

LOCK TABLES `index_folders` WRITE;
/*!40000 ALTER TABLE `index_folders` DISABLE KEYS */;
/*!40000 ALTER TABLE `index_folders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `index_group_folder_rights`
--

DROP TABLE IF EXISTS `index_group_folder_rights`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `index_group_folder_rights` (
  `index_folder_id` int(11) NOT NULL,
  `index_group_id` int(11) NOT NULL,
  `state_id` smallint(6) NOT NULL,
  `folder_write` tinyint(1) NOT NULL,
  `folder_read` tinyint(1) NOT NULL,
  `folder_delete` tinyint(1) NOT NULL DEFAULT '0',
  `folder_mark` tinyint(1) NOT NULL,
  `folder_approve` tinyint(1) NOT NULL DEFAULT '0',
  `folder_hint_read` tinyint(1) NOT NULL DEFAULT '0',
  `document_write` tinyint(1) NOT NULL,
  `document_delete` tinyint(1) NOT NULL DEFAULT '0',
  `document_read` tinyint(1) NOT NULL,
  `preview_watermark` tinyint(1) NOT NULL,
  `preview_original` tinyint(1) NOT NULL,
  `download_watermark` tinyint(1) NOT NULL,
  `download_original` tinyint(1) NOT NULL,
  PRIMARY KEY (`index_folder_id`,`index_group_id`,`state_id`),
  KEY `IDX_1AFAE43426B9E38D` (`index_folder_id`),
  KEY `IDX_1AFAE434DEF1FFA8` (`index_group_id`),
  CONSTRAINT `FK_1AFAE43426B9E38D` FOREIGN KEY (`index_folder_id`) REFERENCES `index_folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_1AFAE434DEF1FFA8` FOREIGN KEY (`index_group_id`) REFERENCES `index_groups` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `index_group_folder_rights`
--

LOCK TABLES `index_group_folder_rights` WRITE;
/*!40000 ALTER TABLE `index_group_folder_rights` DISABLE KEYS */;
/*!40000 ALTER TABLE `index_group_folder_rights` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `index_groups`
--

DROP TABLE IF EXISTS `index_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `index_groups` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) DEFAULT NULL,
  `name` varchar(127) COLLATE utf8_unicode_ci NOT NULL,
  `description` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_groupname` (`name`),
  KEY `IDX_E9F3C681D60322AC` (`role_id`),
  CONSTRAINT `FK_E9F3C681D60322AC` FOREIGN KEY (`role_id`) REFERENCES `user_roles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `index_groups`
--

LOCK TABLES `index_groups` WRITE;
/*!40000 ALTER TABLE `index_groups` DISABLE KEYS */;
/*!40000 ALTER TABLE `index_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `indices`
--

DROP TABLE IF EXISTS `indices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `indices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `description` longtext COLLATE utf8_unicode_ci,
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_name_unique` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `indices`
--

LOCK TABLES `indices` WRITE;
/*!40000 ALTER TABLE `indices` DISABLE KEYS */;
/*!40000 ALTER TABLE `indices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `instance_feature_flags`
--

DROP TABLE IF EXISTS `instance_feature_flags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `instance_feature_flags` (
  `feature_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `state` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`feature_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `instance_feature_flags`
--

LOCK TABLES `instance_feature_flags` WRITE;
/*!40000 ALTER TABLE `instance_feature_flags` DISABLE KEYS */;
/*!40000 ALTER TABLE `instance_feature_flags` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `maintenance_index_recipe`
--

DROP TABLE IF EXISTS `maintenance_index_recipe`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `maintenance_index_recipe` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_id` int(11) DEFAULT NULL,
  `payload` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:json_array)',
  `status` smallint(6) NOT NULL,
  `queue_state` smallint(6) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_8BAE5F6484337261` (`index_id`),
  CONSTRAINT `FK_8BAE5F6484337261` FOREIGN KEY (`index_id`) REFERENCES `indices` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `maintenance_index_recipe`
--

LOCK TABLES `maintenance_index_recipe` WRITE;
/*!40000 ALTER TABLE `maintenance_index_recipe` DISABLE KEYS */;
/*!40000 ALTER TABLE `maintenance_index_recipe` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `maintenance_index_worker`
--

DROP TABLE IF EXISTS `maintenance_index_worker`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `maintenance_index_worker` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_id` int(11) DEFAULT NULL,
  `user_id` int(11) NOT NULL,
  `filepath` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `type` smallint(6) NOT NULL,
  `status` smallint(6) NOT NULL,
  `logs` longtext COLLATE utf8_unicode_ci COMMENT '(DC2Type:json_array)',
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_CE94513184337261` (`index_id`),
  KEY `IDX_CE945131A76ED395` (`user_id`),
  CONSTRAINT `FK_CE94513184337261` FOREIGN KEY (`index_id`) REFERENCES `indices` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_CE945131A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `maintenance_index_worker`
--

LOCK TABLES `maintenance_index_worker` WRITE;
/*!40000 ALTER TABLE `maintenance_index_worker` DISABLE KEYS */;
/*!40000 ALTER TABLE `maintenance_index_worker` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `maintenance_recipe_applicant`
--

DROP TABLE IF EXISTS `maintenance_recipe_applicant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `maintenance_recipe_applicant` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_recipe_id` int(11) DEFAULT NULL,
  `asset_id` int(11) DEFAULT NULL,
  `status` smallint(6) NOT NULL,
  `try_logs` longtext COLLATE utf8_unicode_ci COMMENT '(DC2Type:json_array)',
  `apply_logs` longtext COLLATE utf8_unicode_ci COMMENT '(DC2Type:json_array)',
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_73F1CB24694DF8DB` (`index_recipe_id`),
  KEY `IDX_73F1CB245DA1941` (`asset_id`),
  CONSTRAINT `FK_73F1CB245DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_73F1CB24694DF8DB` FOREIGN KEY (`index_recipe_id`) REFERENCES `maintenance_index_recipe` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `maintenance_recipe_applicant`
--

LOCK TABLES `maintenance_recipe_applicant` WRITE;
/*!40000 ALTER TABLE `maintenance_recipe_applicant` DISABLE KEYS */;
/*!40000 ALTER TABLE `maintenance_recipe_applicant` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `mfa_trust_tokens`
--

DROP TABLE IF EXISTS `mfa_trust_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `mfa_trust_tokens` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `token` char(36) COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:uuid)',
  `valid_until` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_3D0FCFEDA76ED395` (`user_id`),
  CONSTRAINT `FK_3D0FCFEDA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `mfa_trust_tokens`
--

LOCK TABLES `mfa_trust_tokens` WRITE;
/*!40000 ALTER TABLE `mfa_trust_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `mfa_trust_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migration_autofill_mapping`
--

DROP TABLE IF EXISTS `migration_autofill_mapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migration_autofill_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `target_index_folder_id` int(11) NOT NULL,
  `source_directory` varchar(512) COLLATE utf8_unicode_ci NOT NULL,
  `folder_to_create` varchar(512) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_A348D0D964B701EE` (`target_index_folder_id`),
  CONSTRAINT `FK_A348D0D964B701EE` FOREIGN KEY (`target_index_folder_id`) REFERENCES `index_folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migration_autofill_mapping`
--

LOCK TABLES `migration_autofill_mapping` WRITE;
/*!40000 ALTER TABLE `migration_autofill_mapping` DISABLE KEYS */;
/*!40000 ALTER TABLE `migration_autofill_mapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migration_folders_autofill_mapping`
--

DROP TABLE IF EXISTS `migration_folders_autofill_mapping`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migration_folders_autofill_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `target_index_folder_id` int(11) NOT NULL,
  `source_directory` varchar(512) COLLATE utf8_unicode_ci NOT NULL,
  `folder_to_create` varchar(512) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_22942AEF64B701EE` (`target_index_folder_id`),
  CONSTRAINT `FK_22942AEF64B701EE` FOREIGN KEY (`target_index_folder_id`) REFERENCES `index_folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migration_folders_autofill_mapping`
--

LOCK TABLES `migration_folders_autofill_mapping` WRITE;
/*!40000 ALTER TABLE `migration_folders_autofill_mapping` DISABLE KEYS */;
/*!40000 ALTER TABLE `migration_folders_autofill_mapping` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migration_mapped_assets`
--

DROP TABLE IF EXISTS `migration_mapped_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migration_mapped_assets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_id` int(11) NOT NULL,
  `source_path` varchar(512) COLLATE utf8_unicode_ci NOT NULL,
  `brainloop_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `last_sync` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_A57D29C05DA1941` (`asset_id`),
  CONSTRAINT `FK_A57D29C05DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migration_mapped_assets`
--

LOCK TABLES `migration_mapped_assets` WRITE;
/*!40000 ALTER TABLE `migration_mapped_assets` DISABLE KEYS */;
/*!40000 ALTER TABLE `migration_mapped_assets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migration_mapped_documents`
--

DROP TABLE IF EXISTS `migration_mapped_documents`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migration_mapped_documents` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `document_id` int(11) NOT NULL,
  `source_folder_path` varchar(512) COLLATE utf8_unicode_ci NOT NULL,
  `source_document_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `target_document_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `source_checksum` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_D903A50DC33F7837` (`document_id`),
  CONSTRAINT `FK_D903A50DC33F7837` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migration_mapped_documents`
--

LOCK TABLES `migration_mapped_documents` WRITE;
/*!40000 ALTER TABLE `migration_mapped_documents` DISABLE KEYS */;
/*!40000 ALTER TABLE `migration_mapped_documents` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migration_mapped_folders`
--

DROP TABLE IF EXISTS `migration_mapped_folders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migration_mapped_folders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `folder_id` int(11) NOT NULL,
  `source_path` varchar(512) COLLATE utf8_unicode_ci NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_6F8F13CC162CB942` (`folder_id`),
  CONSTRAINT `FK_6F8F13CC162CB942` FOREIGN KEY (`folder_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migration_mapped_folders`
--

LOCK TABLES `migration_mapped_folders` WRITE;
/*!40000 ALTER TABLE `migration_mapped_folders` DISABLE KEYS */;
/*!40000 ALTER TABLE `migration_mapped_folders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migration_mapped_indices`
--

DROP TABLE IF EXISTS `migration_mapped_indices`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migration_mapped_indices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `index_id` int(11) NOT NULL,
  `source_path` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_918FDBB884337261` (`index_id`),
  CONSTRAINT `FK_918FDBB884337261` FOREIGN KEY (`index_id`) REFERENCES `indices` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migration_mapped_indices`
--

LOCK TABLES `migration_mapped_indices` WRITE;
/*!40000 ALTER TABLE `migration_mapped_indices` DISABLE KEYS */;
/*!40000 ALTER TABLE `migration_mapped_indices` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notification_settings`
--

DROP TABLE IF EXISTS `notification_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notification_settings` (
  `user_id` int(11) NOT NULL,
  `qa_notification_frequency` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dqa_notification_frequency` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_asset_notification_frequency` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_document_notification_frequency` varchar(32) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`user_id`),
  CONSTRAINT `FK_B0559860A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notification_settings`
--

LOCK TABLES `notification_settings` WRITE;
/*!40000 ALTER TABLE `notification_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `notification_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `asset_id` int(11) DEFAULT NULL,
  `type` smallint(6) NOT NULL,
  `content` longtext COLLATE utf8_unicode_ci NOT NULL COMMENT '(DC2Type:json_array)',
  `link` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `is_sent` smallint(6) NOT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_6000B0D3A76ED395` (`user_id`),
  KEY `IDX_6000B0D35DA1941` (`asset_id`),
  CONSTRAINT `FK_6000B0D35DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`),
  CONSTRAINT `FK_6000B0D3A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `operation_locks`
--

DROP TABLE IF EXISTS `operation_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `operation_locks` (
  `operation_id` int(11) NOT NULL,
  `entity_hash` varchar(15) COLLATE utf8_unicode_ci NOT NULL,
  `status` smallint(6) NOT NULL,
  PRIMARY KEY (`operation_id`,`entity_hash`),
  UNIQUE KEY `operation_lock_unique` (`operation_id`,`entity_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `operation_locks`
--

LOCK TABLES `operation_locks` WRITE;
/*!40000 ALTER TABLE `operation_locks` DISABLE KEYS */;
/*!40000 ALTER TABLE `operation_locks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `portfolios`
--

DROP TABLE IF EXISTS `portfolios`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `portfolios` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  `description` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_B81B226F5E237E06` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `portfolios`
--

LOCK TABLES `portfolios` WRITE;
/*!40000 ALTER TABLE `portfolios` DISABLE KEYS */;
/*!40000 ALTER TABLE `portfolios` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `portfolios_assets`
--

DROP TABLE IF EXISTS `portfolios_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `portfolios_assets` (
  `portfolio_id` int(11) NOT NULL,
  `asset_id` int(11) NOT NULL,
  PRIMARY KEY (`portfolio_id`,`asset_id`),
  KEY `IDX_788EC2DAB96B5643` (`portfolio_id`),
  KEY `IDX_788EC2DA5DA1941` (`asset_id`),
  CONSTRAINT `FK_788EC2DA5DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_788EC2DAB96B5643` FOREIGN KEY (`portfolio_id`) REFERENCES `portfolios` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `portfolios_assets`
--

LOCK TABLES `portfolios_assets` WRITE;
/*!40000 ALTER TABLE `portfolios_assets` DISABLE KEYS */;
/*!40000 ALTER TABLE `portfolios_assets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_answers`
--

DROP TABLE IF EXISTS `qa_answers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_answers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `text` text COLLATE utf8_unicode_ci NOT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_answers`
--

LOCK TABLES `qa_answers` WRITE;
/*!40000 ALTER TABLE `qa_answers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_answers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_clearers`
--

DROP TABLE IF EXISTS `qa_clearers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_clearers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `process` int(11) DEFAULT NULL,
  `user` int(11) DEFAULT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_72D3C6C5861D1896` (`process`),
  KEY `IDX_72D3C6C58D93D649` (`user`),
  CONSTRAINT `FK_72D3C6C5861D1896` FOREIGN KEY (`process`) REFERENCES `qa_processes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_72D3C6C58D93D649` FOREIGN KEY (`user`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_clearers`
--

LOCK TABLES `qa_clearers` WRITE;
/*!40000 ALTER TABLE `qa_clearers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_clearers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_dispatch_clearers`
--

DROP TABLE IF EXISTS `qa_dispatch_clearers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_dispatch_clearers` (
  `question_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `manual` tinyint(1) NOT NULL DEFAULT '0',
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`question_id`,`user_id`),
  KEY `IDX_852401361E27F6BF` (`question_id`),
  KEY `IDX_85240136A76ED395` (`user_id`),
  CONSTRAINT `FK_852401361E27F6BF` FOREIGN KEY (`question_id`) REFERENCES `qa_questions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_85240136A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_dispatch_clearers`
--

LOCK TABLES `qa_dispatch_clearers` WRITE;
/*!40000 ALTER TABLE `qa_dispatch_clearers` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_dispatch_clearers` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_dispatch_experts`
--

DROP TABLE IF EXISTS `qa_dispatch_experts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_dispatch_experts` (
  `question_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `manual` tinyint(1) NOT NULL DEFAULT '0',
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`question_id`,`user_id`),
  KEY `IDX_B67077931E27F6BF` (`question_id`),
  KEY `IDX_B6707793A76ED395` (`user_id`),
  CONSTRAINT `FK_B67077931E27F6BF` FOREIGN KEY (`question_id`) REFERENCES `qa_questions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_B6707793A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_dispatch_experts`
--

LOCK TABLES `qa_dispatch_experts` WRITE;
/*!40000 ALTER TABLE `qa_dispatch_experts` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_dispatch_experts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_experts`
--

DROP TABLE IF EXISTS `qa_experts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_experts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `process` int(11) DEFAULT NULL,
  `asset` int(11) DEFAULT NULL,
  `folder` int(11) DEFAULT NULL,
  `user` int(11) DEFAULT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `process_asset_folder_user_unique` (`process`,`asset`,`folder`,`user`),
  KEY `IDX_23E0D907861D1896` (`process`),
  KEY `IDX_23E0D9072AF5A5C` (`asset`),
  KEY `IDX_23E0D907ECA209CD` (`folder`),
  KEY `IDX_23E0D9078D93D649` (`user`),
  CONSTRAINT `FK_23E0D9072AF5A5C` FOREIGN KEY (`asset`) REFERENCES `assets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_23E0D907861D1896` FOREIGN KEY (`process`) REFERENCES `qa_processes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_23E0D9078D93D649` FOREIGN KEY (`user`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_23E0D907ECA209CD` FOREIGN KEY (`folder`) REFERENCES `folders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_experts`
--

LOCK TABLES `qa_experts` WRITE;
/*!40000 ALTER TABLE `qa_experts` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_experts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_hints`
--

DROP TABLE IF EXISTS `qa_hints`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_hints` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `text` varchar(1023) COLLATE utf8_unicode_ci NOT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_hints`
--

LOCK TABLES `qa_hints` WRITE;
/*!40000 ALTER TABLE `qa_hints` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_hints` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_processes`
--

DROP TABLE IF EXISTS `qa_processes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_processes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `description` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `state` smallint(6) NOT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_processes`
--

LOCK TABLES `qa_processes` WRITE;
/*!40000 ALTER TABLE `qa_processes` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_processes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_processes_assets`
--

DROP TABLE IF EXISTS `qa_processes_assets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_processes_assets` (
  `process` int(11) NOT NULL,
  `asset` int(11) NOT NULL,
  PRIMARY KEY (`process`,`asset`),
  KEY `IDX_A0E0A1D861D1896` (`process`),
  KEY `IDX_A0E0A1D2AF5A5C` (`asset`),
  CONSTRAINT `FK_A0E0A1D2AF5A5C` FOREIGN KEY (`asset`) REFERENCES `assets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_A0E0A1D861D1896` FOREIGN KEY (`process`) REFERENCES `qa_processes` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_processes_assets`
--

LOCK TABLES `qa_processes_assets` WRITE;
/*!40000 ALTER TABLE `qa_processes_assets` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_processes_assets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_questioners`
--

DROP TABLE IF EXISTS `qa_questioners`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_questioners` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `process` int(11) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_4DE8841A861D1896` (`process`),
  KEY `IDX_4DE8841AFE54D947` (`group_id`),
  CONSTRAINT `FK_4DE8841A861D1896` FOREIGN KEY (`process`) REFERENCES `qa_processes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_4DE8841AFE54D947` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_questioners`
--

LOCK TABLES `qa_questioners` WRITE;
/*!40000 ALTER TABLE `qa_questioners` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_questioners` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_questions`
--

DROP TABLE IF EXISTS `qa_questions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `folder_id` int(11) DEFAULT NULL,
  `document_id` int(11) DEFAULT NULL,
  `asset_id` int(11) DEFAULT NULL,
  `process_id` int(11) DEFAULT NULL,
  `text` text COLLATE utf8_unicode_ci NOT NULL,
  `is_archived` tinyint(1) NOT NULL,
  `state` smallint(6) NOT NULL,
  `order_number` int(11) NOT NULL,
  `due_date` datetime DEFAULT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  `type` smallint(6) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_71AADFB2A76ED395` (`user_id`),
  KEY `IDX_71AADFB2FE54D947` (`group_id`),
  KEY `IDX_71AADFB2162CB942` (`folder_id`),
  KEY `IDX_71AADFB2C33F7837` (`document_id`),
  KEY `IDX_71AADFB25DA1941` (`asset_id`),
  KEY `IDX_71AADFB27EC2F574` (`process_id`),
  CONSTRAINT `FK_71AADFB2162CB942` FOREIGN KEY (`folder_id`) REFERENCES `folders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_71AADFB25DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_71AADFB27EC2F574` FOREIGN KEY (`process_id`) REFERENCES `qa_processes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_71AADFB2A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_71AADFB2C33F7837` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_71AADFB2FE54D947` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_questions`
--

LOCK TABLES `qa_questions` WRITE;
/*!40000 ALTER TABLE `qa_questions` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_questions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_sequences`
--

DROP TABLE IF EXISTS `qa_sequences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_sequences` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `question_id` int(11) NOT NULL,
  `hint_id` int(11) DEFAULT NULL,
  `answer_id` int(11) DEFAULT NULL,
  `action` smallint(6) NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_4C963BFBA76ED395` (`user_id`),
  KEY `IDX_4C963BFB1E27F6BF` (`question_id`),
  KEY `IDX_4C963BFB519161AB` (`hint_id`),
  KEY `IDX_4C963BFBAA334807` (`answer_id`),
  CONSTRAINT `FK_4C963BFB1E27F6BF` FOREIGN KEY (`question_id`) REFERENCES `qa_questions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_4C963BFB519161AB` FOREIGN KEY (`hint_id`) REFERENCES `qa_hints` (`id`),
  CONSTRAINT `FK_4C963BFBA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_4C963BFBAA334807` FOREIGN KEY (`answer_id`) REFERENCES `qa_answers` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_sequences`
--

LOCK TABLES `qa_sequences` WRITE;
/*!40000 ALTER TABLE `qa_sequences` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_sequences` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `qa_spectators`
--

DROP TABLE IF EXISTS `qa_spectators`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `qa_spectators` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `process` int(11) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_B81D7EE1861D1896` (`process`),
  KEY `IDX_B81D7EE1FE54D947` (`group_id`),
  CONSTRAINT `FK_B81D7EE1861D1896` FOREIGN KEY (`process`) REFERENCES `qa_processes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `FK_B81D7EE1FE54D947` FOREIGN KEY (`group_id`) REFERENCES `groups` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `qa_spectators`
--

LOCK TABLES `qa_spectators` WRITE;
/*!40000 ALTER TABLE `qa_spectators` DISABLE KEYS */;
/*!40000 ALTER TABLE `qa_spectators` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `report_downloads`
--

DROP TABLE IF EXISTS `report_downloads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `report_downloads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `filename` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `filesize` int(11) DEFAULT NULL,
  `filepath` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `status` smallint(6) NOT NULL,
  `expiry` datetime NOT NULL,
  `updated` datetime NOT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_65C7C9ADA76ED395` (`user_id`),
  CONSTRAINT `FK_65C7C9ADA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `report_downloads`
--

LOCK TABLES `report_downloads` WRITE;
/*!40000 ALTER TABLE `report_downloads` DISABLE KEYS */;
/*!40000 ALTER TABLE `report_downloads` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tenants`
--

DROP TABLE IF EXISTS `tenants`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tenants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_id` int(11) DEFAULT NULL,
  `property` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `t_scode` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `tenant` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `lease_from` datetime DEFAULT NULL,
  `lease_to` datetime DEFAULT NULL,
  `units` varchar(2048) COLLATE utf8_unicode_ci DEFAULT NULL,
  `break_date` datetime DEFAULT NULL,
  `contract` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `lettable` double DEFAULT NULL,
  `rent` double DEFAULT NULL,
  `erv` double DEFAULT NULL,
  `service_charge` double DEFAULT NULL,
  `office` double DEFAULT NULL,
  `warehouse` double DEFAULT NULL,
  `land` double DEFAULT NULL,
  `retail` double DEFAULT NULL,
  `residential` double DEFAULT NULL,
  `other` double DEFAULT NULL,
  `parking` double DEFAULT NULL,
  `timestamp` date NOT NULL,
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `IDX_B8FC96BB5DA1941` (`asset_id`),
  CONSTRAINT `FK_B8FC96BB5DA1941` FOREIGN KEY (`asset_id`) REFERENCES `assets` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tenants`
--

LOCK TABLES `tenants` WRITE;
/*!40000 ALTER TABLE `tenants` DISABLE KEYS */;
/*!40000 ALTER TABLE `tenants` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user2current_role`
--

DROP TABLE IF EXISTS `user2current_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user2current_role` (
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  PRIMARY KEY (`user_id`,`role_id`),
  KEY `IDX_51C9252EA76ED395` (`user_id`),
  KEY `IDX_51C9252ED60322AC` (`role_id`),
  CONSTRAINT `FK_51C9252EA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_51C9252ED60322AC` FOREIGN KEY (`role_id`) REFERENCES `user_roles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user2current_role`
--

LOCK TABLES `user2current_role` WRITE;
/*!40000 ALTER TABLE `user2current_role` DISABLE KEYS */;
/*!40000 ALTER TABLE `user2current_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user2role`
--

DROP TABLE IF EXISTS `user2role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user2role` (
  `user_id` int(11) NOT NULL,
  `role_id` int(11) NOT NULL,
  PRIMARY KEY (`user_id`,`role_id`),
  KEY `IDX_4C4A755FA76ED395` (`user_id`),
  KEY `IDX_4C4A755FD60322AC` (`role_id`),
  CONSTRAINT `FK_4C4A755FA76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `FK_4C4A755FD60322AC` FOREIGN KEY (`role_id`) REFERENCES `user_roles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user2role`
--

LOCK TABLES `user2role` WRITE;
/*!40000 ALTER TABLE `user2role` DISABLE KEYS */;
/*!40000 ALTER TABLE `user2role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_multi_factor_auth`
--

DROP TABLE IF EXISTS `user_multi_factor_auth`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_multi_factor_auth` (
  `user_id` int(11) NOT NULL,
  `secret` varchar(128) COLLATE utf8_unicode_ci NOT NULL,
  `method` varchar(5) COLLATE utf8_unicode_ci DEFAULT NULL,
  `phoneNumber` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `created` datetime NOT NULL,
  PRIMARY KEY (`user_id`),
  CONSTRAINT `FK_5568A3B0A76ED395` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_multi_factor_auth`
--

LOCK TABLES `user_multi_factor_auth` WRITE;
/*!40000 ALTER TABLE `user_multi_factor_auth` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_multi_factor_auth` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_roles`
--

DROP TABLE IF EXISTS `user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT NULL,
  `role_id` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `default` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `UNIQ_54FCD59FD60322AC` (`role_id`),
  KEY `IDX_54FCD59F727ACA70` (`parent_id`),
  CONSTRAINT `FK_54FCD59F727ACA70` FOREIGN KEY (`parent_id`) REFERENCES `user_roles` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_roles`
--

LOCK TABLES `user_roles` WRITE;
/*!40000 ALTER TABLE `user_roles` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `firstname` varchar(63) COLLATE utf8_unicode_ci NOT NULL,
  `lastname` varchar(63) COLLATE utf8_unicode_ci NOT NULL,
  `gender` varchar(1) COLLATE utf8_unicode_ci NOT NULL,
  `title` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `password` varchar(128) COLLATE utf8_unicode_ci NOT NULL,
  `confirmation_hash` varchar(128) COLLATE utf8_unicode_ci DEFAULT NULL,
  `lang` varchar(5) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'de-DE',
  `login_date` datetime DEFAULT NULL,
  `last_login_date` datetime DEFAULT NULL,
  `features` longtext COLLATE utf8_unicode_ci COMMENT '(DC2Type:json_array)',
  `sso` tinyint(1) NOT NULL DEFAULT '0',
  `mfa` tinyint(1) NOT NULL DEFAULT '0',
  `created` datetime NOT NULL,
  `updated` datetime NOT NULL,
  `state` smallint(6) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_idx` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2018-08-24 13:41:02
