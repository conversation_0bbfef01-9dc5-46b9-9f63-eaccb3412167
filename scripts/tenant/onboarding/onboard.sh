#!/bin/bash
# Enhanced tenant onboarding script with advanced features and S3 database import
# This script onboards a new tenant with all advanced features and imports a database schema from S3

# Enable debugging
set -ex

# Debug function
debug() {
  echo "[DEBUG] $(date +"%Y-%m-%d %H:%M:%S") - $1" >&2
}

# Default values
TENANT_NAME=""
SUBDOMAIN=""
ENVIRONMENT="production"
TIER="standard"
DOMAIN="architrave-assets.com"
DMS=false
LOGO=""
DELPHI=false
EXTERNAL_API=false
DOCUMENT_CLASS_SET="standard"
LANGUAGE="en"
REFERENCE_DATA=false
HEAP_TRACKING=false
S3_BUCKET="architravetestdb"
S3_KEY="architrave_1.45.2.sql"
RDS_SECRET_NAME="production/rds/master-d8076664-cef8-4b20-1f9d-47b1247bb611"
ENABLE_ISTIO=true
ENABLE_MONITORING=true
ENABLE_AUTOSCALING=true
ENABLE_NETWORK_POLICIES=true
ENABLE_POD_SECURITY=true
ENABLE_DATABASE_IMPORT=true
ENABLE_S3_MOUNT=true
ENABLE_SSL=true
ENABLE_ROLLBACK=true
ENABLE_VALIDATION=true
SKIP_SSL=false
ENABLE_KEDA=true
ENABLE_VPA=true
ENABLE_KARPENTER=true

# Function to display usage information
usage() {
  echo "Usage: $0 [--tenant-name <tenant-name>] [--subdomain <subdomain>] [--environment <environment>] [--tier <tier>] [--dms] [--logo <logo-url>] [--delphi] [--external-api] [--document-class-set <document-class-set>] [--language <language>] [--reference-data <reference-data>] [--heap-tracking] [--s3-bucket <bucket>] [--s3-key <key>] [--rds-secret-name <secret-name>] [--rds-host <host>] [--rds-port <port>] [--rds-admin-user <user>] [--rds-admin-password <password>]"
  echo
  echo "Options:"
  echo "  --tenant-name <tenant-name>           Name of the tenant (required)"
  echo "  --tenant-id <tenant-id>               ID of the tenant (default: derived from subdomain)"
  echo "  --subdomain <subdomain>               Subdomain for the tenant (default: tenant name in lowercase)"
  echo "  --environment <environment>           Environment (default: production)"
  echo "  --tier <tier>                         Tenant tier: basic, standard, premium (default: standard)"
  echo "  --dms                                 Enable DMS for the tenant"
  echo "  --logo <logo-url>                     URL to the tenant's logo"
  echo "  --delphi                              Enable Delphi for the tenant"
  echo "  --external-api                        Enable external API for the tenant"
  echo "  --document-class-set <document-class-set>  Document class set for the tenant"
  echo "  --language <language>                 Language for the tenant"
  echo "  --reference-data <reference-data>     Reference data for the tenant"
  echo "  --heap-tracking                       Enable heap tracking for the tenant"
  echo "  --s3-bucket <bucket>                  S3 bucket containing the SQL file to import"
  echo "  --s3-key <key>                        S3 key (path) to the SQL file"
  echo "  --rds-secret-name <secret-name>       Name of the AWS Secrets Manager secret containing RDS credentials"
  echo "  --rds-host <host>                     RDS host (required if --rds-secret-name not provided)"
  echo "  --rds-port <port>                     RDS port (defaults to 3306)"
  echo "  --rds-admin-user <user>               RDS admin username (required if --rds-secret-name not provided)"
  echo "  --rds-admin-password <password>       RDS admin password (required if --rds-secret-name not provided)"
  echo "  --skip-db                             Skip database setup"
  echo "  --skip-ssl                            Skip SSL certificate creation"
  echo "  --enable-keda                         Enable KEDA for event-driven autoscaling (default: true)"
  echo "  --enable-vpa                          Enable Vertical Pod Autoscaler (default: true)"
  echo "  --enable-karpenter                    Enable Karpenter for node autoscaling (default: true)"
  echo "  --help                                Display this help message"
  exit 1
}

# Function to log messages
log() {
  local level=$1
  shift
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $*"
}

# Function to check resource usage on nodes
check_node_resources() {
  log INFO "Checking node resources..."
  kubectl describe nodes | grep -A 10 "Allocated resources" >&2
}

# Function to check pod status
check_pod_status() {
  local namespace=$1
  log INFO "Checking pod status in namespace $namespace..."
  kubectl get pods -n $namespace -o wide >&2
  kubectl get events -n $namespace | grep -i error | head -10 >&2
}

# Function to check pod scheduling issues
check_scheduling_issues() {
  local namespace=$1
  log INFO "Checking scheduling issues in namespace $namespace..."
  kubectl get pods -n $namespace | grep Pending >&2
  for pod in $(kubectl get pods -n $namespace | grep Pending | awk '{print $1}'); do
    log INFO "Checking scheduling issues for pod $pod..."
    kubectl describe pod $pod -n $namespace | grep -A 10 "Events:" >&2
  done
}

# Function to configure Istio gateway with proper SSL certificates
configure_istio_gateway() {
  log INFO "Checking and configuring Istio gateway..."

  # Check if Istio is installed
  if ! kubectl get namespace istio-system &>/dev/null; then
    log INFO "Istio namespace not found. Installing Istio..."

    # Create istio-system namespace
    kubectl create namespace istio-system

    # Add Istio Helm repository
    helm repo add istio https://istio-release.storage.googleapis.com/charts
    helm repo update

    # Install Istio base
    log INFO "Installing Istio base..."
    helm install istio-base istio/base \
      --namespace istio-system \
      --set defaultRevision=default

    # Install Istio discovery (istiod)
    log INFO "Installing Istio discovery (istiod)..."
    helm install istiod istio/istiod \
      --namespace istio-system \
      --set global.hub=docker.io/istio \
      --set global.tag=1.18.0 \
      --set pilot.resources.requests.cpu=500m \
      --set pilot.resources.requests.memory=2048Mi \
      --set global.proxy.resources.requests.cpu=100m \
      --set global.proxy.resources.requests.memory=128Mi \
      --set global.proxy.resources.limits.cpu=2000m \
      --set global.proxy.resources.limits.memory=1024Mi \
      --set meshConfig.accessLogFile=/dev/stdout \
      --set meshConfig.enableTracing=true \
      --set pilot.traceSampling=100.0

    # Install Istio ingress gateway
    log INFO "Installing Istio ingress gateway..."
    helm install istio-ingressgateway istio/gateway \
      --namespace istio-system \
      --set service.type=LoadBalancer \
      --set autoscaling.enabled=true \
      --set autoscaling.minReplicas=2 \
      --set autoscaling.maxReplicas=5 \
      --set resources.requests.cpu=100m \
      --set resources.requests.memory=128Mi \
      --set resources.limits.cpu=2000m \
      --set resources.limits.memory=1024Mi

    # Wait for Istio to be ready
    log INFO "Waiting for Istio to be ready..."
    kubectl wait --for=condition=available --timeout=300s deployment/istiod -n istio-system
    kubectl wait --for=condition=available --timeout=300s deployment/istio-ingressgateway -n istio-system
  fi

  # Check if the Istio gateway exists
  if ! kubectl get gateway tenant-gateway -n istio-system &>/dev/null; then
    log INFO "Istio gateway not found. Creating it..."

    # Check if the SSL certificate exists
    if ! kubectl get secret architrave-assets-wildcard-cert -n istio-system &>/dev/null; then
      if [ "$SKIP_SSL" = true ]; then
        log INFO "SSL certificate not found, but --skip-ssl flag is provided. Using a dummy certificate..."

        # Create a temporary directory for certificate generation
        TEMP_DIR=$(mktemp -d)

        # Generate a dummy certificate (not valid for actual use)
        openssl req -x509 -nodes -days 1 -newkey rsa:2048 \
          -keyout "$TEMP_DIR/tls.key" \
          -out "$TEMP_DIR/tls.crt" \
          -subj "/CN=dummy-cert" \
          -addext "subjectAltName = DNS:dummy-cert"

        # Create the Kubernetes secret
        kubectl create secret tls architrave-assets-wildcard-cert -n istio-system \
          --cert="$TEMP_DIR/tls.crt" \
          --key="$TEMP_DIR/tls.key" \
          --dry-run=client -o yaml | kubectl apply -f -

        # Clean up
        rm -rf "$TEMP_DIR"
      else
        log INFO "SSL certificate not found. Creating a self-signed certificate..."

        # Create a temporary directory for certificate generation
        TEMP_DIR=$(mktemp -d)

        # Generate a self-signed certificate
        log INFO "Generating self-signed certificate for *.architrave-assets.com"
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
          -keyout "$TEMP_DIR/tls.key" \
          -out "$TEMP_DIR/tls.crt" \
          -subj "/CN=*.architrave-assets.com" \
          -addext "subjectAltName = DNS:*.architrave-assets.com"

        # Create the Kubernetes secret
        kubectl create secret tls architrave-assets-wildcard-cert -n istio-system \
          --cert="$TEMP_DIR/tls.crt" \
          --key="$TEMP_DIR/tls.key" \
          --dry-run=client -o yaml | kubectl apply -f -

        # Clean up
        rm -rf "$TEMP_DIR"
      fi
    fi

    # Create the Istio gateway
    cat <<EOF | kubectl apply -f -
apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tenant-gateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    managed-by: tenant-onboarding
spec:
  selector:
    istio: ingressgateway
  servers:
  - hosts:
    - '*.architrave-assets.com'
    port:
      name: http
      number: 80
      protocol: HTTP
    tls:
      httpsRedirect: true
  - hosts:
    - '*.architrave-assets.com'
    port:
      name: https
      number: 443
      protocol: HTTPS
    tls:
      mode: SIMPLE
      credentialName: architrave-assets-wildcard-cert
EOF
  fi

  # Create PeerAuthentication for mTLS
  log INFO "Creating PeerAuthentication for mTLS..."
  cat <<EOF | kubectl apply -f -
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: istio-system
spec:
  mtls:
    mode: PERMISSIVE
EOF
}

# Parse command line arguments
TENANT_NAME=""
TENANT_ID=""
SUBDOMAIN=""
ENVIRONMENT="production"
TIER="standard"
DMS=false
LOGO=""
DELPHI=false
EXTERNAL_API=false
DOCUMENT_CLASS_SET=""
LANGUAGE="en"
REFERENCE_DATA=""
HEAP_TRACKING=false
# Default S3 bucket and key for database import
S3_BUCKET="architravetestdb"
S3_KEY="architrave_1.45.2.sql"
RDS_SECRET_NAME="production/rds/master-new"
RDS_HOST=""
RDS_PORT="3306"
RDS_ADMIN_USER=""
RDS_ADMIN_PASSWORD=""
SKIP_DB=false

while [[ $# -gt 0 ]]; do
  case "$1" in
    --tenant-name)
      TENANT_NAME="$2"
      shift 2
      ;;
    --tenant-id)
      TENANT_ID="$2"
      shift 2
      ;;
    --subdomain)
      SUBDOMAIN="$2"
      shift 2
      ;;
    --environment)
      ENVIRONMENT="$2"
      shift 2
      ;;
    --tier)
      TIER="$2"
      shift 2
      ;;
    --dms)
      DMS=true
      shift
      ;;
    --logo)
      LOGO="$2"
      shift 2
      ;;
    --delphi)
      DELPHI=true
      shift
      ;;
    --external-api)
      EXTERNAL_API=true
      shift
      ;;
    --document-class-set)
      DOCUMENT_CLASS_SET="$2"
      shift 2
      ;;
    --language)
      LANGUAGE="$2"
      shift 2
      ;;
    --reference-data)
      REFERENCE_DATA="$2"
      shift 2
      ;;
    --heap-tracking)
      HEAP_TRACKING=true
      shift
      ;;
    --s3-bucket)
      S3_BUCKET="$2"
      shift 2
      ;;
    --s3-key)
      S3_KEY="$2"
      shift 2
      ;;
    --rds-secret-name)
      RDS_SECRET_NAME="$2"
      shift 2
      ;;
    --rds-host)
      RDS_HOST="$2"
      shift 2
      ;;
    --rds-port)
      RDS_PORT="$2"
      shift 2
      ;;
    --rds-admin-user)
      RDS_ADMIN_USER="$2"
      shift 2
      ;;
    --rds-admin-password)
      RDS_ADMIN_PASSWORD="$2"
      shift 2
      ;;
    --skip-db)
      SKIP_DB=true
      shift
      ;;
    --skip-ssl)
      SKIP_SSL=true
      shift
      ;;
    --enable-keda)
      ENABLE_KEDA=true
      shift
      ;;
    --disable-keda)
      ENABLE_KEDA=false
      shift
      ;;
    --enable-vpa)
      ENABLE_VPA=true
      shift
      ;;
    --disable-vpa)
      ENABLE_VPA=false
      shift
      ;;
    --enable-karpenter)
      ENABLE_KARPENTER=true
      shift
      ;;
    --disable-karpenter)
      ENABLE_KARPENTER=false
      shift
      ;;
    --help)
      usage
      ;;
    *)
      log ERROR "Unknown option: $1"
      usage
      ;;
  esac
done

# Validate required arguments
if [[ -z "$TENANT_NAME" ]]; then
  log ERROR "Tenant name is required"
  usage
fi

# Set subdomain if not provided
if [[ -z "$SUBDOMAIN" ]]; then
  SUBDOMAIN=$(echo "$TENANT_NAME" | tr '[:upper:]' '[:lower:]' | tr ' ' '-')
fi

# Generate tenant ID if not provided
if [[ -z "$TENANT_ID" ]]; then
  TENANT_ID=$(echo "$SUBDOMAIN" | tr '[:upper:]' '[:lower:]' | tr ' ' '-')
fi

# Set domain based on environment
if [[ "$ENVIRONMENT" == "production" ]]; then
  DOMAIN="architrave.com"
else
  DOMAIN="$ENVIRONMENT.architrave.com"
fi

# Set resource limits based on tier
case "$TIER" in
  basic)
    CPU_REQUEST="500m"
    CPU_LIMIT="1000m"
    MEMORY_REQUEST="1Gi"
    MEMORY_LIMIT="2Gi"
    STORAGE="10Gi"
    MIN_REPLICAS=1
    MAX_REPLICAS=3
    CPU_THRESHOLD=80
    MEMORY_THRESHOLD=80
    ;;
  standard)
    CPU_REQUEST="1000m"
    CPU_LIMIT="2000m"
    MEMORY_REQUEST="2Gi"
    MEMORY_LIMIT="4Gi"
    STORAGE="20Gi"
    MIN_REPLICAS=2
    MAX_REPLICAS=5
    CPU_THRESHOLD=70
    MEMORY_THRESHOLD=70
    ;;
  premium)
    CPU_REQUEST="500m"
    CPU_LIMIT="1000m"
    MEMORY_REQUEST="1Gi"
    MEMORY_LIMIT="2Gi"
    STORAGE="50Gi"
    MIN_REPLICAS=1
    MAX_REPLICAS=3
    CPU_THRESHOLD=60
    MEMORY_THRESHOLD=60
    ;;
  *)
    log ERROR "Invalid tier: $TIER. Valid options are: basic, standard, premium"
    exit 1
    ;;
esac

log INFO "Starting onboarding process for tenant '$TENANT_NAME' ($TENANT_ID)..."
log INFO "Environment: $ENVIRONMENT"
log INFO "Tier: $TIER"
log INFO "Domain: $SUBDOMAIN.$DOMAIN"

# === Automated Retry and Cleanup Logic ===
MAX_RETRIES=3
RETRY_DELAY=30  # seconds
ATTEMPT=1

while [ $ATTEMPT -le $MAX_RETRIES ]; do
  log INFO "Onboarding attempt $ATTEMPT of $MAX_RETRIES for tenant '$TENANT_NAME' ($TENANT_ID)"
  set +e
  # Main onboarding logic is everything below this block
  (
    set -e
    # === BEGIN MAIN ONBOARDING LOGIC ===
    # The rest of this script is the main onboarding logic
    # If any command fails, the subshell will exit with nonzero status
    # Configure Istio gateway with proper SSL certificates
    configure_istio_gateway

    # Create namespace
    log INFO "Creating namespace..."
    kubectl create namespace tenant-$TENANT_ID --dry-run=client -o yaml | kubectl apply -f -

    # Check node resources before creating deployments
    check_node_resources

    # Label namespace
    kubectl label namespace tenant-$TENANT_ID --overwrite \
      tenant.architrave.io/tenant-id=$TENANT_ID \
      tenant.architrave.io/tenant-name="$(echo $TENANT_NAME | tr ' ' '-')" \
      tenant.architrave.io/tier=$TIER \
      environment=$ENVIRONMENT \
      istio-injection=enabled

    # Set up tenant resources (LimitRange and ResourceQuota)
    log INFO "Setting up tenant resources..."
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    chmod +x $SCRIPT_DIR/setup-tenant-resources.sh
    $SCRIPT_DIR/setup-tenant-resources.sh --tenant-id "$TENANT_ID"

    # Create tenant custom resource
    log INFO "Creating tenant custom resource..."
    cat <<EOF | kubectl apply -f -
apiVersion: tenant.architrave.io/v1
kind: Tenant
metadata:
  name: $TENANT_ID
  labels:
    tenant.architrave.io/tenant-id: $TENANT_ID
    tenant.architrave.io/tier: $TIER
    environment: $ENVIRONMENT
spec:
  displayName: "$TENANT_NAME"
  subdomain: "$SUBDOMAIN"
  environment: "$ENVIRONMENT"
  tier: "$TIER"
  status: "Active"
  creationDate: "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
  features:
    dms: $DMS
    delphi: $DELPHI
    externalApi: $EXTERNAL_API
    heapTracking: $HEAP_TRACKING
  resourceQuota:
    cpu: "$CPU_REQUEST"
    memory: "$MEMORY_REQUEST"
    storage: "$STORAGE"
    pods: $MIN_REPLICAS
  configuration:
    language: "$LANGUAGE"
    documentClassSet: "$DOCUMENT_CLASS_SET"
    referenceData: "$REFERENCE_DATA"
    logo: "$LOGO"
EOF

    # Create resource quota
    log INFO "Creating resource quota..."
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ResourceQuota
metadata:
  name: $TENANT_ID-quota
  namespace: tenant-$TENANT_ID
spec:
  hard:
    requests.cpu: "8"
    limits.cpu: "16"
    requests.memory: "12Gi"
    limits.memory: "24Gi"
    requests.storage: "$STORAGE"
    pods: "20"
    services: "10"
    configmaps: "20"
    secrets: "20"
EOF

    # Create limit range
    log INFO "Creating limit range..."
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: LimitRange
metadata:
  name: $TENANT_ID-limits
  namespace: tenant-$TENANT_ID
spec:
  limits:
  - type: Container
    default:
      cpu: 200m
      memory: 256Mi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
    max:
      cpu: 2
      memory: 4Gi
    min:
      cpu: 10m
      memory: 40Mi
EOF

    # Set up database for tenant
    log INFO "Setting up database for tenant..."
    check_node_resources
    check_pod_status "tenant-$TENANT_ID"
    log INFO "Checking for scheduling issues..."
    check_scheduling_issues "tenant-$TENANT_ID"

    # Set database and user names
    DB_NAME="tenant_${TENANT_ID}"
    DB_USER="tenant_${TENANT_ID}"
    DB_PASSWORD=$(openssl rand -base64 16)

    # Try to get RDS credentials from AWS Secrets Manager if secret name is provided
    if [ -n "$RDS_SECRET_NAME" ]; then
      log INFO "Attempting to get RDS credentials from AWS Secrets Manager"
      SECRET_JSON=$(aws secretsmanager get-secret-value --secret-id "$RDS_SECRET_NAME" --query SecretString --output text 2>/dev/null)

      if [ -n "$SECRET_JSON" ]; then
        RDS_HOST=$(echo "$SECRET_JSON" | jq -r '.host // .hostname // .endpoint')
        RDS_PORT=$(echo "$SECRET_JSON" | jq -r '.port // "3306"')
        RDS_ADMIN_USER=$(echo "$SECRET_JSON" | jq -r '.username // .user')
        RDS_ADMIN_PASSWORD=$(echo "$SECRET_JSON" | jq -r '.password')

        log INFO "Retrieved RDS credentials from secret"
      else
        log WARN "Secret $RDS_SECRET_NAME not found. Using provided credentials."

        # Use default RDS host if not provided
        if [ -z "$RDS_HOST" ]; then
          RDS_HOST="production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
          log INFO "Using default RDS host: $RDS_HOST"
        fi
      fi
    fi

    # Set up database for tenant
    # Use the local SQL file for database import
    log INFO "Using import-local-db.sh script to import database from local file to RDS"

    # Make the script executable
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    chmod +x $SCRIPT_DIR/import-local-db.sh

    # Build the database import command
    # Look for the SQL file in multiple locations
    if [ -f "$SCRIPT_DIR/../../../architrave_1.45.2.sql" ]; then
      LOCAL_SQL_FILE="$SCRIPT_DIR/../../../architrave_1.45.2.sql"
    elif [ -f "$SCRIPT_DIR/../../architrave_1.45.2.sql" ]; then
      LOCAL_SQL_FILE="$SCRIPT_DIR/../../architrave_1.45.2.sql"
    elif [ -f "$SCRIPT_DIR/../architrave_1.45.2.sql" ]; then
      LOCAL_SQL_FILE="$SCRIPT_DIR/../architrave_1.45.2.sql"
    elif [ -f "$SCRIPT_DIR/architrave_1.45.2.sql" ]; then
      LOCAL_SQL_FILE="$SCRIPT_DIR/architrave_1.45.2.sql"
    else
      LOCAL_SQL_FILE="/tmp/architrave_1.45.2.sql"
      log INFO "SQL file not found in standard locations, using $LOCAL_SQL_FILE"
      # Create a dummy SQL file if it doesn't exist
      if [ ! -f "$LOCAL_SQL_FILE" ]; then
        log INFO "Creating dummy SQL file at $LOCAL_SQL_FILE"
        echo "-- Dummy SQL file for tenant $TENANT_ID" > "$LOCAL_SQL_FILE"
        echo "USE %sDatabasePlaceHolder%s;" >> "$LOCAL_SQL_FILE"
        echo "CREATE TABLE IF NOT EXISTS dummy (id INT, name VARCHAR(255));" >> "$LOCAL_SQL_FILE"
        echo "INSERT INTO dummy VALUES (1, 'Dummy data for tenant $TENANT_ID');" >> "$LOCAL_SQL_FILE"
      fi
    fi
    log INFO "Using SQL file: $LOCAL_SQL_FILE"

    # Try different methods for database import
    # 1. Bastion host (preferred for production)
    # 2. AWS RDS Data API (if available)
    # 3. Direct MySQL connection (fallback)

    log INFO "Attempting to import database using bastion host..."
    # Make the script executable
    chmod +x $SCRIPT_DIR/setup-db-bastion.sh

    # Build the bastion host command
    BASTION_CMD="$SCRIPT_DIR/setup-db-bastion.sh --tenant-id \"$TENANT_ID\" --local-sql-file \"$LOCAL_SQL_FILE\""

    # Add RDS parameters
    if [ -n "$RDS_SECRET_NAME" ]; then
      BASTION_CMD="$BASTION_CMD --rds-secret-name \"$RDS_SECRET_NAME\""
    else
      BASTION_CMD="$BASTION_CMD --rds-host \"${RDS_HOST:-production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com}\""
      BASTION_CMD="$BASTION_CMD --rds-port \"${RDS_PORT:-3306}\""
      BASTION_CMD="$BASTION_CMD --rds-admin-user \"${RDS_ADMIN_USER:-admin}\""
      BASTION_CMD="$BASTION_CMD --rds-admin-password \"${RDS_ADMIN_PASSWORD}\""
    fi

    # Try bastion host first
    set +e  # Don't exit on error
    eval $BASTION_CMD
    BASTION_RESULT=$?
    set -e  # Resume exit on error

    if [ $BASTION_RESULT -eq 0 ]; then
      log INFO "Database import using bastion host completed successfully"
      DB_IMPORT_CMD=""  # Skip other import methods
    else
      log WARN "Database import using bastion host failed (exit code: $BASTION_RESULT)"
      log WARN "Falling back to other import methods..."

      # Try AWS RDS Data API next
      if [ -n "$RDS_SECRET_NAME" ]; then
        log INFO "Using import-db-with-data-api.sh script to import database using AWS RDS Data API"

        # Make the script executable
        chmod +x $SCRIPT_DIR/import-db-with-data-api.sh

        DB_IMPORT_CMD="$SCRIPT_DIR/import-db-with-data-api.sh --tenant-id \"$TENANT_ID\" --local-sql-file \"$LOCAL_SQL_FILE\" --rds-secret-name \"$RDS_SECRET_NAME\""
      else
        # Fall back to direct MySQL connection
        log INFO "Using import-local-db.sh script to import database from local file to RDS"

        # Make the script executable
        chmod +x $SCRIPT_DIR/import-local-db.sh

        DB_IMPORT_CMD="$SCRIPT_DIR/import-local-db.sh --tenant-id \"$TENANT_ID\" --local-sql-file \"$LOCAL_SQL_FILE\""

        # Add RDS parameters with defaults
        DB_IMPORT_CMD="$DB_IMPORT_CMD --rds-host \"${RDS_HOST:-production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com}\""
        DB_IMPORT_CMD="$DB_IMPORT_CMD --rds-port \"${RDS_PORT:-3306}\""
        DB_IMPORT_CMD="$DB_IMPORT_CMD --rds-admin-user \"${RDS_ADMIN_USER:-admin}\""
        DB_IMPORT_CMD="$DB_IMPORT_CMD --rds-admin-password \"${RDS_ADMIN_PASSWORD}\""
      fi
    fi

    # Execute the database import command if needed
    if [ -n "$DB_IMPORT_CMD" ]; then
      log INFO "Executing: $DB_IMPORT_CMD"
      set +e  # Don't exit on error
      eval $DB_IMPORT_CMD
      IMPORT_RESULT=$?
      set -e  # Resume exit on error
    else
      log INFO "Skipping additional database import methods as bastion host import was successful"
      IMPORT_RESULT=0
    fi

    if [ $IMPORT_RESULT -ne 0 ]; then
      log WARN "Database import job failed (exit code: $IMPORT_RESULT)"
      log WARN "This might be because you don't have direct access to the RDS instance."
      log WARN "In a real environment, this would be handled by proper network access and IAM roles."

      # Create default database credentials secret as fallback
      log INFO "Creating default database credentials secret as fallback"

      # Generate database name and credentials
      DB_NAME="tenant_${TENANT_ID}"
      DB_USER="tenant_${TENANT_ID}"
      DB_PASSWORD=$(openssl rand -base64 12)

      kubectl create secret generic "db-credentials" \
        --namespace "tenant-$TENANT_ID" \
        --from-literal=host="${RDS_HOST:-production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com}" \
        --from-literal=port="${RDS_PORT:-3306}" \
        --from-literal=database="$DB_NAME" \
        --from-literal=dbname="$DB_NAME" \
        --from-literal=username="$DB_USER" \
        --from-literal=user="$DB_USER" \
        --from-literal=password="$DB_PASSWORD" \
        --dry-run=client -o yaml | kubectl apply -f -

      # Create webapp-env ConfigMap if it doesn't exist
      if ! kubectl get configmap webapp-env -n tenant-$TENANT_ID &>/dev/null; then
        log INFO "Creating webapp-env ConfigMap..."
        cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: webapp-env
  namespace: tenant-$TENANT_ID
  labels:
    app: webapp-env
data:
  ADVANCED_UPLOADER_URL: https://s3.eu-central-1.amazonaws.com/uploader.dev.core-sandbox.architrave.cloud/index.html
  API_KEY_USER_EMAIL: <EMAIL>
  APP_ENVIRONMENT: $ENVIRONMENT
  APP_HOST: https://$SUBDOMAIN.$DOMAIN
  AV_AUTOMATE_INSTANCE_LOCALE: ${LANGUAGE:-de_DE}
  AV_AUTOMATE_S3_BUCKET: tenant-$TENANT_ID-assets
  AV_AUTOMATE_S3_ENDPOINT: https://s3.eu-central-1.amazonaws.com
  AV_AUTOMATE_S3_REGION: eu-central-1
  COMPOSER_MEMORY_LIMIT: "-1"
  CUSTOMER_ADMIN_EMAIL: <EMAIL>
  CUSTOMER_ID: $TENANT_ID
  CUSTOMER_NAME: $TENANT_NAME
  CUSTOMER_SUPPORT_EMAIL: <EMAIL>
  DCM_HOST: dcm.architrave.de
  DCM_PORT: "443"
  DCM_TRANSPORT: https
  DQA_LOCK_PERIOD_MINUTES: "30"
  ES_HOST: elastic.local
  ES_INDEX: qa-5
  GLOBAL_SEARCH_CURL_REGION: eu-central-1
  IMS_LINKS: '[]'
  LMC_USER_PASSWORD_COST: "4"
  MANDRILL_KEY: ""
  MYSQL_DATABASE: $DB_NAME
  MYSQL_HOST: ${RDS_HOST:-production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com}
  MYSQL_USER: $DB_USER
  RABBITMQ_HOST: tenant-$TENANT_ID-rabbitmq
  RABBITMQ_USER: guest
  RELEASE_NOTES_PROVIDER: hubspot
  RELEASE_NOTES_URL: https://example.com
  SCIM_USER_EMAIL: <EMAIL>
  SSO_PROVIDERS: '[]'
  TRASH_EXPIRATION_DAYS: "1"
  TRASH_EXPIRATION_DAYS_AV_AUTOMATE_APPROVED: "1"
  TRASH_EXPIRATION_DAYS_AV_AUTOMATE_DELETED: "1"
EOF
      else
        # Update the webapp-env ConfigMap with database information
        log INFO "Updating webapp-env ConfigMap with database information..."
        kubectl get configmap webapp-env -n tenant-$TENANT_ID -o yaml | \
          sed "s/MYSQL_DATABASE:.*/MYSQL_DATABASE: $DB_NAME/" | \
          sed "s/MYSQL_HOST:.*/MYSQL_HOST: ${RDS_HOST:-production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com}/" | \
          sed "s/MYSQL_USER:.*/MYSQL_USER: $DB_USER/" | \
          kubectl apply -f -
      fi

      log INFO "webapp-env ConfigMap created/updated successfully"
      log WARN "Created default database credentials without actual database import"
      log WARN "In a production environment, the backend application would connect to a real database"
    else
      log INFO "Database import completed successfully"

      # Database credentials are created by the import job
      DB_NAME="tenant_${TENANT_ID}"
      DB_USER="tenant_${TENANT_ID}"

      # Validate the database import
      log INFO "Validating database import..."

      # Make the validation script executable
      chmod +x $SCRIPT_DIR/validate-db-import.sh

      # Build the validation command
      VALIDATE_CMD="$SCRIPT_DIR/validate-db-import.sh --tenant-id \"$TENANT_ID\""

      # Add RDS parameters
      if [ -n "$RDS_SECRET_NAME" ]; then
        VALIDATE_CMD="$VALIDATE_CMD --rds-secret-name \"$RDS_SECRET_NAME\""
      else
        VALIDATE_CMD="$VALIDATE_CMD --rds-host \"${RDS_HOST:-production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com}\""
        VALIDATE_CMD="$VALIDATE_CMD --rds-port \"${RDS_PORT:-3306}\""
        VALIDATE_CMD="$VALIDATE_CMD --rds-admin-user \"${RDS_ADMIN_USER:-admin}\""
        VALIDATE_CMD="$VALIDATE_CMD --rds-admin-password \"${RDS_ADMIN_PASSWORD}\""
      fi

      # Execute the validation command
      log INFO "Executing: $VALIDATE_CMD"
      set +e  # Don't exit on error
      eval $VALIDATE_CMD
      VALIDATE_RESULT=$?
      set -e  # Resume exit on error

      if [ $VALIDATE_RESULT -ne 0 ]; then
        log WARN "Database validation failed (exit code: $VALIDATE_RESULT)"
        log WARN "This might be because you don't have direct access to the RDS instance."
        log WARN "In a production environment, this would be handled by proper network access and IAM roles."
      else
        log INFO "Database validation successful"
      fi
    fi

    # Use default RDS host if not provided
    if [ -z "$RDS_HOST" ]; then
      RDS_HOST="production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
    fi

    # Create S3 bucket for tenant assets
    log INFO "Creating S3 bucket for tenant assets..."
    S3_BUCKET="tenant-$TENANT_ID-assets"

    # Check if bucket already exists
    if aws s3api head-bucket --bucket "$S3_BUCKET" 2>/dev/null; then
      log INFO "S3 bucket $S3_BUCKET already exists, skipping creation"
    else
      log INFO "Creating S3 bucket $S3_BUCKET..."
      aws s3api create-bucket --bucket "$S3_BUCKET" --region eu-central-1 --create-bucket-configuration LocationConstraint=eu-central-1 || log WARN "Failed to create bucket, it may already exist"

      # Set bucket policy to deny public access
      log INFO "Setting bucket policy to deny public access..."
      if aws s3api head-bucket --bucket "$S3_BUCKET" 2>/dev/null; then
        aws s3api put-public-access-block --bucket "$S3_BUCKET" \
          --public-access-block-configuration "BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=true,RestrictPublicBuckets=true" || log WARN "Failed to set public access block"
      else
        log WARN "Cannot set bucket policy, bucket does not exist"
      fi

      # Enable server-side encryption with KMS
      log INFO "Enabling server-side encryption with KMS..."
      if aws s3api head-bucket --bucket "$S3_BUCKET" 2>/dev/null; then
        # Create KMS key for S3 encryption
        log INFO "Creating KMS key for S3 encryption..."
        KMS_KEY_ID=$(aws kms create-key \
          --description "Key for tenant $TENANT_ID S3 bucket encryption" \
          --tags TagKey=Tenant,TagValue=$TENANT_ID \
          --query KeyMetadata.KeyId --output text 2>/dev/null || echo "")

        if [ -n "$KMS_KEY_ID" ]; then
          # Create KMS alias
          log INFO "Creating KMS alias..."
          aws kms create-alias \
            --alias-name "alias/tenant-$TENANT_ID-s3" \
            --target-key-id "$KMS_KEY_ID" 2>/dev/null || log WARN "Failed to create KMS alias"

          # Update the webapp-env ConfigMap with S3 bucket information
          log INFO "Updating webapp-env ConfigMap with S3 bucket information..."
          kubectl get configmap webapp-env -n tenant-$TENANT_ID -o yaml | \
            sed "s/AV_AUTOMATE_S3_BUCKET:.*/AV_AUTOMATE_S3_BUCKET: $S3_BUCKET/" | \
            kubectl apply -f -

          log INFO "webapp-env ConfigMap updated successfully"
        else
          log WARN "Failed to create KMS key, using default encryption"
          KMS_KEY_ID="alias/aws/s3"
        fi
      else
        log WARN "Cannot set up KMS encryption, bucket does not exist"
        KMS_KEY_ID="alias/aws/s3"
      fi

      # Configure bucket encryption
      log INFO "Configuring bucket encryption..."
      if aws s3api head-bucket --bucket "$S3_BUCKET" 2>/dev/null; then
        aws s3api put-bucket-encryption --bucket "$S3_BUCKET" \
          --server-side-encryption-configuration '{
            "Rules": [
              {
                "ApplyServerSideEncryptionByDefault": {
                  "SSEAlgorithm": "aws:kms",
                  "KMSMasterKeyID": "'"$KMS_KEY_ID"'"
                },
                "BucketKeyEnabled": true
              }
            ]
          }' || log WARN "Failed to set bucket encryption"
      else
        log WARN "Cannot configure bucket encryption, bucket does not exist"
      fi

      # Tag the bucket
      log INFO "Tagging the bucket..."
      if aws s3api head-bucket --bucket "$S3_BUCKET" 2>/dev/null; then
        aws s3api put-bucket-tagging --bucket "$S3_BUCKET" \
          --tagging "TagSet=[{Key=Tenant,Value=$TENANT_ID},{Key=Environment,Value=production},{Key=TenantId,Value=$TENANT_ID}]" || log WARN "Failed to tag bucket"
      else
        log WARN "Cannot tag bucket, bucket does not exist"
      fi

      log INFO "S3 bucket created successfully with encryption enabled"
    fi

    # Create IAM role for S3 access
    log INFO "Creating IAM role for S3 access..."
    cat <<EOF > /tmp/trust-policy.json
    {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Effect": "Allow",
          "Principal": {
            "Federated": "arn:aws:iam::************:oidc-provider/oidc.eks.eu-central-1.amazonaws.com/id/8CAD6B5465EA6D7BA164303E5594C2CB"
          },
          "Action": "sts:AssumeRoleWithWebIdentity",
          "Condition": {
            "StringEquals": {
              "oidc.eks.eu-central-1.amazonaws.com/id/8CAD6B5465EA6D7BA164303E5594C2CB:sub": "system:serviceaccount:tenant-$TENANT_ID:tenant-$TENANT_ID-s3-sa"
            }
          }
        }
      ]
    }
    EOF

    cat <<EOF > /tmp/s3-policy.json
    {
      "Version": "2012-10-17",
      "Statement": [
        {
          "Effect": "Allow",
          "Action": [
            "s3:GetObject",
            "s3:PutObject",
            "s3:DeleteObject",
            "s3:ListBucket"
          ],
          "Resource": [
            "arn:aws:s3:::tenant-$TENANT_ID-assets",
            "arn:aws:s3:::tenant-$TENANT_ID-assets/*"
          ]
        }
      ]
    }
    EOF

    # Check if role already exists
    if ! aws iam get-role --role-name "tenant-$TENANT_ID-s3-role" &>/dev/null; then
      aws iam create-role --role-name "tenant-$TENANT_ID-s3-role" --assume-role-policy-document file:///tmp/trust-policy.json
      aws iam put-role-policy --role-name "tenant-$TENANT_ID-s3-role" --policy-name "tenant-$TENANT_ID-s3-policy" --policy-document file:///tmp/s3-policy.json
      log INFO "IAM role created successfully"
    else
      log INFO "IAM role already exists"
    fi

    # Create service account for S3 access
    log INFO "Creating service account for S3 access..."
    cat <<EOF | kubectl apply -f -
    apiVersion: v1
    kind: ServiceAccount
    metadata:
      name: tenant-$TENANT_ID-s3-sa
      namespace: tenant-$TENANT_ID
      annotations:
        eks.amazonaws.com/role-arn: arn:aws:iam::************:role/tenant-$TENANT_ID-s3-role
    EOF

    # Create a pod that mounts the S3 bucket
    log INFO "Creating a pod that mounts the S3 bucket..."
    cat <<EOF | kubectl apply -f -
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: tenant-$TENANT_ID-s3-mount
      namespace: tenant-$TENANT_ID
      labels:
        app: tenant-$TENANT_ID-s3-mount
        tenant: $TENANT_ID
        tenant.architrave.io/tenant-id: $TENANT_ID
    spec:
      replicas: 1
      selector:
        matchLabels:
          app: tenant-$TENANT_ID-s3-mount
      template:
        metadata:
          labels:
            app: tenant-$TENANT_ID-s3-mount
            tenant: $TENANT_ID
            tenant.architrave.io/tenant-id: $TENANT_ID
          annotations:
            sidecar.istio.io/proxyCPU: "100m"
            sidecar.istio.io/proxyCPULimit: "200m"
            sidecar.istio.io/proxyMemory: "64Mi"
            sidecar.istio.io/proxyMemoryLimit: "256Mi"
        spec:
          serviceAccountName: tenant-$TENANT_ID-s3-sa
          affinity:
            podAntiAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                podAffinityTerm:
                  labelSelector:
                    matchExpressions:
                    - key: app
                      operator: In
                      values:
                      - tenant-$TENANT_ID-s3-mount
                  topologyKey: "kubernetes.io/hostname"
          containers:
          - name: s3fs
            image: amazonlinux:2
            command: ["/bin/bash", "-c"]
            args:
            - |
              # Install s3fs
              yum install -y amazon-linux-extras
              amazon-linux-extras install epel -y
              yum install -y s3fs-fuse

              # Mount S3 bucket
              mkdir -p /mnt/s3
              s3fs tenant-$TENANT_ID-assets /mnt/s3 -o iam_role=auto -o url=https://s3-eu-central-1.amazonaws.com -o use_cache=/tmp -o allow_other -o umask=0022

              # Keep container running
              while true; do sleep 30; done
            resources:
              requests:
                cpu: "50m"
                memory: "64Mi"
              limits:
                cpu: "100m"
                memory: "128Mi"
            securityContext:
              privileged: true
            volumeMounts:
            - name: s3-mount
              mountPath: /mnt/s3
          volumes:
          - name: s3-mount
            emptyDir: {}
    EOF

    # Create database credentials secret
    log INFO "Creating database credentials secret..."
    cat <<EOF | kubectl apply -f -
    apiVersion: v1
    kind: Secret
    metadata:
      name: db-credentials
      namespace: tenant-$TENANT_ID
    type: Opaque
    stringData:
      host: "$RDS_HOST"
      port: "$RDS_PORT"
      database: "$DB_NAME"
      dbname: "$DB_NAME"
      username: "$DB_USER"
      user: "$DB_USER"
      password: "$DB_PASSWORD"
    EOF

    # Create PHP-FPM configuration
    log INFO "Creating PHP-FPM configuration..."
    cat <<EOF | kubectl apply -f -
    apiVersion: v1
    kind: ConfigMap
    metadata:
      name: php-fpm-config
      namespace: tenant-$TENANT_ID
    data:
      www.conf: |
        [www]
        user = www-data
        group = www-data
        listen = 0.0.0.0:9000
        pm = dynamic
        pm.max_children = 10
        pm.start_servers = 4
        pm.min_spare_servers = 2
        pm.max_spare_servers = 6
        chdir = /storage/ArchAssets/public
        php_admin_value[error_log] = /proc/self/fd/2
        php_admin_flag[log_errors] = on
        php_admin_value[memory_limit] = 512M
        php_admin_value[upload_max_filesize] = 100M
        php_admin_value[post_max_size] = 100M
        php_admin_value[max_execution_time] = 300
        php_admin_value[max_input_time] = 300
        php_admin_value[default_socket_timeout] = 300
    EOF

    # Create application configuration
    log INFO "Creating application configuration..."
    cat <<EOF | kubectl apply -f -
    apiVersion: v1
    kind: ConfigMap
    metadata:
      name: app-config
      namespace: tenant-$TENANT_ID
    data:
      local.php: |
        <?php
        return [
          'db' => [
            'driver' => 'pdo_mysql',
            'host' => getenv('DB_HOST'),
            'port' => getenv('DB_PORT'),
            'dbname' => getenv('DB_NAME'),
            'database' => getenv('DB_NAME'),
            'user' => getenv('DB_USER'),
            'username' => getenv('DB_USERNAME'),
            'password' => getenv('DB_PASSWORD'),
            'charset' => 'utf8mb4',
          ],
          'storage' => [
            'adapter' => 's3',
            'bucket' => 'tenant-$TENANT_ID-assets',
            'region' => 'eu-central-1',
          ],
          'rabbitmq' => [
            'host' => getenv('RABBITMQ_HOST'),
            'port' => getenv('RABBITMQ_PORT'),
            'user' => 'guest',
            'password' => 'guest',
            'vhost' => '/',
          ],
          'features' => [
            'dms' => $DMS,
            'delphi' => $DELPHI,
            'externalApi' => $EXTERNAL_API,
            'heapTracking' => $HEAP_TRACKING,
          ],
          'tenant' => [
            'id' => '$TENANT_ID',
            'name' => '$TENANT_NAME',
            'subdomain' => '$SUBDOMAIN',
            'domain' => '$DOMAIN',
          ],
        ];
    EOF

    # Create webapp-env ConfigMap
    log INFO "Creating webapp-env ConfigMap..."

    # Generate database name and credentials if not already set
    if [ -z "$DB_NAME" ]; then
      DB_NAME="tenant_${TENANT_ID}"
    fi
    if [ -z "$DB_USER" ]; then
      DB_USER="tenant_${TENANT_ID}"
    fi

    # Use default RDS host if not provided
    if [ -z "$RDS_HOST" ]; then
      RDS_HOST="production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
    fi

    cat <<EOF | kubectl apply -f -
    apiVersion: v1
    kind: ConfigMap
    metadata:
      name: webapp-env
      namespace: tenant-$TENANT_ID
      labels:
        app: webapp-env
    data:
      ADVANCED_UPLOADER_URL: https://s3.eu-central-1.amazonaws.com/uploader.dev.core-sandbox.architrave.cloud/index.html
      API_KEY_USER_EMAIL: <EMAIL>
      APP_ENVIRONMENT: $ENVIRONMENT
      APP_HOST: https://$SUBDOMAIN.$DOMAIN
      AV_AUTOMATE_INSTANCE_LOCALE: ${LANGUAGE:-de_DE}
      AV_AUTOMATE_S3_BUCKET: tenant-$TENANT_ID-assets
      AV_AUTOMATE_S3_ENDPOINT: https://s3.eu-central-1.amazonaws.com
      AV_AUTOMATE_S3_REGION: eu-central-1
      COMPOSER_MEMORY_LIMIT: "-1"
      CUSTOMER_ADMIN_EMAIL: <EMAIL>
      CUSTOMER_ID: $TENANT_ID
      CUSTOMER_NAME: $TENANT_NAME
      CUSTOMER_SUPPORT_EMAIL: <EMAIL>
      DCM_HOST: dcm.architrave.de
      DCM_PORT: "443"
      DCM_TRANSPORT: https
      DQA_LOCK_PERIOD_MINUTES: "30"
      ES_HOST: elastic.local
      ES_INDEX: qa-5
      GLOBAL_SEARCH_CURL_REGION: eu-central-1
      IMS_LINKS: '[]'
      LMC_USER_PASSWORD_COST: "4"
      MANDRILL_KEY: ""
      MYSQL_DATABASE: $DB_NAME
      MYSQL_HOST: $RDS_HOST
      MYSQL_USER: $DB_USER
      RABBITMQ_HOST: tenant-$TENANT_ID-rabbitmq
      RABBITMQ_USER: guest
      RELEASE_NOTES_PROVIDER: hubspot
      RELEASE_NOTES_URL: https://example.com
      SCIM_USER_EMAIL: <EMAIL>
      SSO_PROVIDERS: '[]'
      TRASH_EXPIRATION_DAYS: "1"
      TRASH_EXPIRATION_DAYS_AV_AUTOMATE_APPROVED: "1"
      TRASH_EXPIRATION_DAYS_AV_AUTOMATE_DELETED: "1"
    EOF

    log INFO "webapp-env ConfigMap created successfully"

    # Deploy tenant application with separate deployments for each component
    log INFO "Deploying tenant application with frontend, backend, and RabbitMQ as separate deployments..."

    # Frontend deployment
    log INFO "Creating frontend deployment..."
    check_node_resources
    check_pod_status "tenant-$TENANT_ID"
    log INFO "Checking for scheduling issues..."
    check_scheduling_issues "tenant-$TENANT_ID"
    cat <<EOF | kubectl apply -f -
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: tenant-$TENANT_ID-frontend
      namespace: tenant-$TENANT_ID
      labels:
        app: tenant-$TENANT_ID-frontend
        tenant: $TENANT_ID
        component: frontend
    spec:
      replicas: $MIN_REPLICAS
      selector:
        matchLabels:
          app: tenant-$TENANT_ID-frontend
      template:
        metadata:
          labels:
            app: tenant-$TENANT_ID-frontend
            tenant: $TENANT_ID
            tenant.architrave.io/tenant-id: $TENANT_ID
            component: frontend
          annotations:
            sidecar.istio.io/proxyCPU: "100m"
            sidecar.istio.io/proxyCPULimit: "200m"
            sidecar.istio.io/proxyMemory: "64Mi"
            sidecar.istio.io/proxyMemoryLimit: "256Mi"
        spec:
          affinity:
            podAntiAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                podAffinityTerm:
                  labelSelector:
                    matchExpressions:
                    - key: app
                      operator: In
                      values:
                      - tenant-$TENANT_ID-frontend
                  topologyKey: "kubernetes.io/hostname"
          initContainers:
          - name: init-frontend
            image: busybox
            command:
            - sh
            - -c
            - |
              # Create proper directory structure for the application
              mkdir -p /var/www/html && chmod 777 /var/www/html
            resources:
              requests:
                cpu: "10m"
                memory: "40Mi"
              limits:
                cpu: "50m"
                memory: "64Mi"
            volumeMounts:
            - name: html-volume
              mountPath: /var/www/html
          containers:
          - name: frontend
            image: ************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
            ports:
            - containerPort: 80
            - containerPort: 443
            # Use the webapp-env ConfigMap for environment variables
            envFrom:
            - configMapRef:
                name: webapp-env
            env:
            - name: TENANT_ID
              value: "$TENANT_ID"
            - name: TENANT_NAME
              value: "$TENANT_NAME"
            - name: ENVIRONMENT
              value: "$ENVIRONMENT"
            - name: BACKEND_HOST
              value: "webapp"
            resources:
              requests:
                cpu: "25m"
                memory: "128Mi"
              limits:
                cpu: "300m"
                memory: "512Mi"
            volumeMounts:
            - name: html-volume
              mountPath: /var/www/html
          volumes:
          - name: html-volume
            emptyDir: {}
    EOF

    # Backend deployment
    log INFO "Creating backend deployment..."
    check_node_resources
    check_pod_status "tenant-$TENANT_ID"
    log INFO "Checking for scheduling issues..."
    check_scheduling_issues "tenant-$TENANT_ID"
    cat <<EOF | kubectl apply -f -
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: tenant-$TENANT_ID-backend
      namespace: tenant-$TENANT_ID
      labels:
        app: tenant-$TENANT_ID-backend
        tenant: $TENANT_ID
        component: backend
    spec:
      replicas: $MIN_REPLICAS
      selector:
        matchLabels:
          app: tenant-$TENANT_ID-backend
      template:
        metadata:
          labels:
            app: tenant-$TENANT_ID-backend
            tenant: $TENANT_ID
            tenant.architrave.io/tenant-id: $TENANT_ID
            component: backend
          annotations:
            sidecar.istio.io/proxyCPU: "100m"
            sidecar.istio.io/proxyCPULimit: "200m"
            sidecar.istio.io/proxyMemory: "64Mi"
            sidecar.istio.io/proxyMemoryLimit: "256Mi"
        spec:
          serviceAccountName: tenant-$TENANT_ID-s3-sa
          affinity:
            podAntiAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                podAffinityTerm:
                  labelSelector:
                    matchExpressions:
                    - key: app
                      operator: In
                      values:
                      - tenant-$TENANT_ID-backend
                  topologyKey: "kubernetes.io/hostname"
          initContainers:
          - name: init-storage
            image: busybox
            command:
            - sh
            - -c
            - |
              # Create proper directory structure for the application
              mkdir -p /storage/clear && chmod 777 /storage/clear &&
              mkdir -p /storage/ArchAssets/config/autoload && chmod 777 /storage/ArchAssets/config/autoload &&
              mkdir -p /storage/ArchAssets/public && chmod 777 /storage/ArchAssets/public &&
              mkdir -p /storage/ArchAssets/vendor/bin && chmod 777 /storage/ArchAssets/vendor/bin &&
              # Create dummy doctrine-module file to prevent errors
              echo '#!/bin/sh' > /storage/ArchAssets/vendor/bin/doctrine-module &&
              echo 'echo "Dummy doctrine-module"' >> /storage/ArchAssets/vendor/bin/doctrine-module &&
              chmod +x /storage/ArchAssets/vendor/bin/doctrine-module
            resources:
              requests:
                cpu: "10m"
                memory: "40Mi"
              limits:
                cpu: "50m"
                memory: "64Mi"
            volumeMounts:
            - name: storage-volume
              mountPath: /storage
          containers:
          - name: backend
            image: ************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.41
            ports:
            - containerPort: 9000
              name: http
            # Use the webapp-env ConfigMap for environment variables
            envFrom:
            - configMapRef:
                name: webapp-env
            # Add additional environment variables that might be needed
            env:
            - name: TENANT_ID
              value: "$TENANT_ID"
            - name: TENANT_NAME
              value: "$TENANT_NAME"
            - name: ENVIRONMENT
              value: "$ENVIRONMENT"
            # Use database credentials from secret
            - name: DB_HOST
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: host
            - name: DB_PORT
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: port
            - name: DB_NAME
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: database
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: password
            volumeMounts:
            - name: storage-volume
              mountPath: /storage
            - name: php-fpm-config
              mountPath: /usr/local/etc/php-fpm.d/www.conf
              subPath: www.conf
            - name: app-config
              mountPath: /storage/ArchAssets/config/autoload/local.php
              subPath: local.php
            resources:
              requests:
                cpu: "25m"
                memory: "128Mi"
              limits:
                cpu: "200m"
                memory: "384Mi"
          volumes:
          - name: storage-volume
            emptyDir: {}
          - name: php-fpm-config
            configMap:
              name: php-fpm-config
          - name: app-config
            configMap:
              name: app-config
    EOF

    # RabbitMQ deployment
    log INFO "Creating RabbitMQ deployment..."
    check_node_resources
    check_pod_status "tenant-$TENANT_ID"
    log INFO "Checking for scheduling issues..."
    check_scheduling_issues "tenant-$TENANT_ID"
    cat <<EOF | kubectl apply -f -
    apiVersion: apps/v1
    kind: Deployment
    metadata:
      name: tenant-$TENANT_ID-rabbitmq
      namespace: tenant-$TENANT_ID
      labels:
        app: tenant-$TENANT_ID-rabbitmq
        tenant: $TENANT_ID
        component: rabbitmq
    spec:
      replicas: 1
      selector:
        matchLabels:
          app: tenant-$TENANT_ID-rabbitmq
      template:
        metadata:
          labels:
            app: tenant-$TENANT_ID-rabbitmq
            tenant: $TENANT_ID
            tenant.architrave.io/tenant-id: $TENANT_ID
            component: rabbitmq
          annotations:
            sidecar.istio.io/proxyCPU: "100m"
            sidecar.istio.io/proxyCPULimit: "200m"
            sidecar.istio.io/proxyMemory: "64Mi"
            sidecar.istio.io/proxyMemoryLimit: "256Mi"
        spec:
          affinity:
            podAntiAffinity:
              preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 100
                podAffinityTerm:
                  labelSelector:
                    matchExpressions:
                    - key: app
                      operator: In
                      values:
                      - tenant-$TENANT_ID-rabbitmq
                  topologyKey: "kubernetes.io/hostname"
          containers:
          - name: rabbitmq
            image: ************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02
            ports:
            - containerPort: 5672
            - containerPort: 15672
            # Use environment variables from webapp-env ConfigMap
            envFrom:
            - configMapRef:
                name: webapp-env
            env:
            - name: RABBITMQ_DEFAULT_USER
              value: "tenant_${TENANT_ID}"
            - name: RABBITMQ_DEFAULT_PASS
              valueFrom:
                secretKeyRef:
                  name: db-credentials
                  key: password
            resources:
              requests:
                cpu: "25m"
                memory: "128Mi"
              limits:
                cpu: "200m"
                memory: "512Mi"
    EOF

    # Create services for each component
    log INFO "Creating services for each component..."

    # Frontend service
    log INFO "Creating frontend service..."
    cat <<EOF | kubectl apply -f -
    apiVersion: v1
    kind: Service
    metadata:
      name: tenant-$TENANT_ID-frontend
      namespace: tenant-$TENANT_ID
      labels:
        app: tenant-$TENANT_ID-frontend
        tenant: $TENANT_ID
        component: frontend
    spec:
      ports:
      - port: 80
        targetPort: 80
        name: http
      - port: 443
        targetPort: 443
        name: https
      selector:
        app: tenant-$TENANT_ID-frontend
    EOF

    # Backend service
    log INFO "Creating backend service..."
    cat <<EOF | kubectl apply -f -
    apiVersion: v1
    kind: Service
    metadata:
      name: webapp
      namespace: tenant-$TENANT_ID
      labels:
        app: tenant-$TENANT_ID-backend
        tenant: $TENANT_ID
        component: backend
    spec:
      ports:
      - port: 8080
        targetPort: 9000
        name: api
      - port: 9090
        targetPort: 9090
        name: metrics
      selector:
        app: tenant-$TENANT_ID-backend
    EOF

    # RabbitMQ service
    log INFO "Creating RabbitMQ service..."
    cat <<EOF | kubectl apply -f -
    apiVersion: v1
    kind: Service
    metadata:
      name: tenant-$TENANT_ID-rabbitmq
      namespace: tenant-$TENANT_ID
      labels:
        app: tenant-$TENANT_ID-rabbitmq
        tenant: $TENANT_ID
        component: rabbitmq
    spec:
      ports:
      - port: 5672
        targetPort: 5672
        name: amqp
      - port: 15672
        targetPort: 15672
        name: management
      selector:
        app: tenant-$TENANT_ID-rabbitmq
    EOF

    # Create ingress/virtual service
    log INFO "Creating virtual service..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.istio.io/v1beta1
    kind: VirtualService
    metadata:
      name: tenant-$TENANT_ID-vs
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
    spec:
      hosts:
      - "$SUBDOMAIN.$DOMAIN"
      gateways:
      - "istio-system/tenant-gateway"
      http:
      - match:
        - uri:
            prefix: "/api"
        route:
        - destination:
            host: webapp
            port:
              number: 8080
        retries:
          attempts: 3
          perTryTimeout: 2s
          retryOn: gateway-error,connect-failure,refused-stream
        timeout: 5s
      - route:
        - destination:
            host: tenant-$TENANT_ID-frontend
            port:
              number: 80
    EOF

    # Create destination rule
    log INFO "Creating destination rule..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.istio.io/v1beta1
    kind: DestinationRule
    metadata:
      name: tenant-$TENANT_ID-frontend-dr
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
    spec:
      host: tenant-$TENANT_ID-frontend
      trafficPolicy:
        loadBalancer:
          simple: ROUND_ROBIN
        connectionPool:
          tcp:
            maxConnections: 100
            connectTimeout: 30ms
          http:
            http2MaxRequests: 1000
            maxRequestsPerConnection: 10
        outlierDetection:
          consecutive5xxErrors: 5
          interval: 30s
          baseEjectionTime: 30s
          maxEjectionPercent: 100
    ---
    apiVersion: networking.istio.io/v1beta1
    kind: DestinationRule
    metadata:
      name: webapp-dr
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
    spec:
      host: webapp
      trafficPolicy:
        loadBalancer:
          simple: ROUND_ROBIN
        connectionPool:
          tcp:
            maxConnections: 100
            connectTimeout: 30ms
          http:
            http2MaxRequests: 1000
            maxRequestsPerConnection: 10
        outlierDetection:
          consecutive5xxErrors: 5
          interval: 30s
          baseEjectionTime: 30s
          maxEjectionPercent: 100
    EOF

    # Create comprehensive network policies
    log INFO "Creating comprehensive network policies..."

    # Default deny all network policy
    log INFO "Creating default deny all network policy..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: tenant-$TENANT_ID-default-deny
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
        component: network-policy
    spec:
      podSelector: {}
      policyTypes:
      - Ingress
      - Egress
    EOF

    # Allow traffic within the same namespace
    log INFO "Creating network policy to allow traffic within the same namespace..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: tenant-$TENANT_ID-allow-same-namespace
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
        component: network-policy
    spec:
      podSelector: {}
      policyTypes:
      - Ingress
      - Egress
      ingress:
      - from:
        - podSelector: {}
      egress:
      - to:
        - podSelector: {}
    EOF

    # Allow traffic from Istio ingress gateway
    log INFO "Creating network policy to allow traffic from Istio ingress gateway..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: tenant-$TENANT_ID-allow-istio-ingress
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
        component: network-policy
    spec:
      podSelector: {}
      policyTypes:
      - Ingress
      ingress:
      - from:
        - namespaceSelector:
            matchLabels:
              name: istio-system
          podSelector:
            matchLabels:
              app: istio-ingressgateway
    EOF

    # Allow DNS traffic
    log INFO "Creating network policy to allow DNS traffic..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: tenant-$TENANT_ID-allow-dns
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
        component: network-policy
    spec:
      podSelector: {}
      policyTypes:
      - Egress
      egress:
      - to:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: kube-system
          podSelector:
            matchLabels:
              k8s-app: kube-dns
        ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    EOF

    # Allow traffic to AWS services
    log INFO "Creating network policy to allow traffic to AWS services..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: tenant-$TENANT_ID-allow-aws-services
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
        component: network-policy
    spec:
      podSelector: {}
      policyTypes:
      - Egress
      egress:
      - to:
        - ipBlock:
            cidr: 0.0.0.0/0
            except:
            - 10.0.0.0/8
            - **********/12
            - ***********/16
        ports:
        - protocol: TCP
          port: 443
    EOF

    # Allow traffic to RDS
    log INFO "Creating network policy to allow traffic to RDS..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: tenant-$TENANT_ID-allow-rds
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
        component: network-policy
    spec:
      podSelector:
        matchLabels:
          app: tenant-$TENANT_ID-backend
      policyTypes:
      - Egress
      egress:
      - to:
        - ipBlock:
            cidr: 10.0.0.0/8
        ports:
        - protocol: TCP
          port: 3306
    EOF

    # Allow traffic from monitoring
    log INFO "Creating network policy to allow traffic from monitoring..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: tenant-$TENANT_ID-allow-monitoring
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
        component: network-policy
    spec:
      podSelector: {}
      policyTypes:
      - Ingress
      ingress:
      - from:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: monitoring
    EOF

    # Allow traffic to Prometheus
    log INFO "Creating network policy to allow traffic to Prometheus..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: tenant-$TENANT_ID-allow-prometheus
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
        component: network-policy
    spec:
      podSelector: {}
      policyTypes:
      - Egress
      egress:
      - to:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: monitoring
          podSelector:
            matchLabels:
              app: prometheus
        ports:
        - protocol: TCP
          port: 9090
    EOF

    # Allow traffic to Loki
    log INFO "Creating network policy to allow traffic to Loki..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: tenant-$TENANT_ID-allow-loki
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
        component: network-policy
    spec:
      podSelector: {}
      policyTypes:
      - Egress
      egress:
      - to:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: monitoring
          podSelector:
            matchLabels:
              app: loki
        ports:
        - protocol: TCP
          port: 3100
    EOF

    # Allow traffic to Jaeger
    log INFO "Creating network policy to allow traffic to Jaeger..."
    cat <<EOF | kubectl apply -f -
    apiVersion: networking.k8s.io/v1
    kind: NetworkPolicy
    metadata:
      name: tenant-$TENANT_ID-allow-jaeger
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
        component: network-policy
    spec:
      podSelector: {}
      policyTypes:
      - Egress
      egress:
      - to:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: istio-system
          podSelector:
            matchLabels:
              app: jaeger
        ports:
        - protocol: TCP
          port: 9411
        - protocol: TCP
          port: 16686
    EOF

    # Create HPA
    log INFO "Creating HPA..."
    cat <<EOF | kubectl apply -f -
    apiVersion: autoscaling/v2
    kind: HorizontalPodAutoscaler
    metadata:
      name: tenant-$TENANT_ID-frontend-hpa
      namespace: tenant-$TENANT_ID
    spec:
      scaleTargetRef:
        apiVersion: apps/v1
        kind: Deployment
        name: tenant-$TENANT_ID-frontend
      minReplicas: $MIN_REPLICAS
      maxReplicas: $MAX_REPLICAS
      metrics:
      - type: Resource
        resource:
          name: cpu
          target:
            type: Utilization
            averageUtilization: $CPU_THRESHOLD
      - type: Resource
        resource:
          name: memory
          target:
            type: Utilization
            averageUtilization: $MEMORY_THRESHOLD
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300
          policies:
          - type: Percent
            value: 50
            periodSeconds: 60
        scaleUp:
          stabilizationWindowSeconds: 0
          policies:
          - type: Percent
            value: 100
            periodSeconds: 60
          - type: Pods
            value: 2
            periodSeconds: 60
          selectPolicy: Max
    ---
    apiVersion: autoscaling/v2
    kind: HorizontalPodAutoscaler
    metadata:
      name: tenant-$TENANT_ID-backend-hpa
      namespace: tenant-$TENANT_ID
    spec:
      scaleTargetRef:
        apiVersion: apps/v1
        kind: Deployment
        name: tenant-$TENANT_ID-backend
      minReplicas: $MIN_REPLICAS
      maxReplicas: $MAX_REPLICAS
      metrics:
      - type: Resource
        resource:
          name: cpu
          target:
            type: Utilization
            averageUtilization: $CPU_THRESHOLD
      - type: Resource
        resource:
          name: memory
          target:
            type: Utilization
            averageUtilization: $MEMORY_THRESHOLD
      behavior:
        scaleDown:
          stabilizationWindowSeconds: 300
          policies:
          - type: Percent
            value: 50
            periodSeconds: 60
        scaleUp:
          stabilizationWindowSeconds: 0
          policies:
          - type: Percent
            value: 100
            periodSeconds: 60
          - type: Pods
            value: 2
            periodSeconds: 60
          selectPolicy: Max
    EOF

    # Create PDB
    log INFO "Creating PDB..."
    cat <<EOF | kubectl apply -f -
    apiVersion: policy/v1
    kind: PodDisruptionBudget
    metadata:
      name: tenant-$TENANT_ID-frontend-pdb
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
    spec:
      minAvailable: 1
      selector:
        matchLabels:
          app: tenant-$TENANT_ID-frontend
    ---
    apiVersion: policy/v1
    kind: PodDisruptionBudget
    metadata:
      name: tenant-$TENANT_ID-backend-pdb
      namespace: tenant-$TENANT_ID
      labels:
        tenant: $TENANT_ID
    spec:
      minAvailable: 1
      selector:
        matchLabels:
          app: tenant-$TENANT_ID-backend
    EOF

    # Set up advanced monitoring for the tenant
    log INFO "Setting up advanced monitoring for the tenant..."

    # Check if monitoring namespace exists
    if ! kubectl get namespace monitoring &>/dev/null; then
      log INFO "Creating monitoring namespace..."
      kubectl create namespace monitoring

      # Label the namespace for Istio injection
      kubectl label namespace monitoring istio-injection=enabled
    fi

    # Check if Prometheus Operator is installed
    if ! kubectl get deployment -n monitoring prometheus-operator-kube-p-operator &>/dev/null; then
      log INFO "Prometheus Operator not found. Installing it..."

      # Add Helm repositories
      log INFO "Adding Helm repositories..."
      helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
      helm repo add grafana https://grafana.github.io/helm-charts
      helm repo add loki https://grafana.github.io/loki/charts
      helm repo add jaegertracing https://jaegertracing.github.io/helm-charts
      helm repo update

      # Deploy Prometheus Operator (kube-prometheus-stack)
      log INFO "Deploying Prometheus Operator (kube-prometheus-stack)..."
      helm upgrade --install prometheus-operator prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --set grafana.adminPassword=admin@123 \
        --set grafana.persistence.enabled=true \
        --set grafana.persistence.size=10Gi \
        --set prometheus.prometheusSpec.retention=15d \
        --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=50Gi \
        --set prometheus.prometheusSpec.podMonitorSelectorNilUsesHelmValues=false \
        --set prometheus.prometheusSpec.serviceMonitorSelectorNilUsesHelmValues=false \
        --set alertmanager.alertmanagerSpec.retention=120h \
        --set alertmanager.alertmanagerSpec.storage.volumeClaimTemplate.spec.resources.requests.storage=5Gi

      # Deploy Loki
      log INFO "Deploying Loki..."
      helm upgrade --install loki grafana/loki-stack \
        --namespace monitoring \
        --set loki.persistence.enabled=true \
        --set loki.persistence.size=50Gi \
        --set promtail.enabled=true \
        --set promtail.config.lokiAddress=http://loki:3100/loki/api/v1/push \
        --set grafana.enabled=false

      # Deploy Jaeger
      log INFO "Deploying Jaeger..."
      helm upgrade --install jaeger jaegertracing/jaeger \
        --namespace monitoring \
        --set collector.service.type=ClusterIP \
        --set agent.enabled=true \
        --set agent.daemonset.useHostPort=false \
        --set query.service.type=ClusterIP \
        --set storage.type=memory \
        --set allInOne.enabled=true \
        --set allInOne.resources.limits.memory=1Gi \
        --set allInOne.resources.limits.cpu=500m \
        --set allInOne.resources.requests.memory=512Mi \
        --set allInOne.resources.requests.cpu=100m

      # Configure Grafana to use Loki and Jaeger
      log INFO "Configuring Grafana to use Loki and Jaeger..."
      kubectl create configmap -n monitoring grafana-datasources --from-literal=datasources.yaml="
apiVersion: 1
datasources:
- name: Prometheus
  type: prometheus
  url: http://prometheus-operated:9090
  access: proxy
  isDefault: true
- name: Loki
  type: loki
  url: http://loki:3100
  access: proxy
- name: Jaeger
  type: jaeger
  url: http://jaeger-query:16686
  access: proxy
" --dry-run=client -o yaml | kubectl apply -f -

      # Restart Grafana to pick up the new datasources
      kubectl rollout restart deployment -n monitoring prometheus-operator-grafana
    fi

    # Create ServiceMonitor for the tenant
    log INFO "Creating ServiceMonitor for the tenant..."
    cat <<EOF | kubectl apply -f -
    apiVersion: monitoring.coreos.com/v1
    kind: ServiceMonitor
    metadata:
      name: tenant-$TENANT_ID-monitor
      namespace: monitoring
      labels:
        release: prometheus-operator
        tenant: $TENANT_ID
    spec:
      selector:
        matchLabels:
          tenant: $TENANT_ID
      namespaceSelector:
        matchNames:
        - tenant-$TENANT_ID
      endpoints:
      - port: metrics
        interval: 15s
        path: /metrics
    EOF

    # Create PrometheusRule for tenant alerts
    log INFO "Creating PrometheusRule for tenant alerts..."
    cat <<EOF | kubectl apply -f -
    apiVersion: monitoring.coreos.com/v1
    kind: PrometheusRule
    metadata:
      name: tenant-$TENANT_ID-alerts
      namespace: monitoring
      labels:
        release: prometheus-operator
        tenant: $TENANT_ID
    spec:
      groups:
      - name: tenant-$TENANT_ID.rules
        rules:
        - alert: TenantHighCPUUsage
          expr: sum(rate(container_cpu_usage_seconds_total{namespace="tenant-$TENANT_ID"}[5m])) > 0.8
          for: 5m
          labels:
            severity: warning
            tenant: $TENANT_ID
          annotations:
            summary: "High CPU usage in tenant-$TENANT_ID"
            description: "Tenant $TENANT_NAME has high CPU usage"
        - alert: TenantHighMemoryUsage
          expr: sum(container_memory_usage_bytes{namespace="tenant-$TENANT_ID"}) / sum(container_spec_memory_limit_bytes{namespace="tenant-$TENANT_ID"}) > 0.8
          for: 5m
          labels:
            severity: warning
            tenant: $TENANT_ID
          annotations:
            summary: "High memory usage in tenant-$TENANT_ID"
            description: "Tenant $TENANT_NAME has high memory usage"
        - alert: TenantHighErrorRate
          expr: sum(rate(istio_requests_total{namespace="tenant-$TENANT_ID", response_code=~"5.*"}[5m])) / sum(rate(istio_requests_total{namespace="tenant-$TENANT_ID"}[5m])) > 0.05
          for: 5m
          labels:
            severity: warning
            tenant: $TENANT_ID
          annotations:
            summary: "High error rate in tenant-$TENANT_ID"
            description: "Tenant $TENANT_NAME has a high error rate (>5%)"
        - alert: TenantHighLatency
          expr: histogram_quantile(0.95, sum(rate(istio_request_duration_milliseconds_bucket{namespace="tenant-$TENANT_ID"}[5m])) by (le)) > 500
          for: 5m
          labels:
            severity: warning
            tenant: $TENANT_ID
          annotations:
            summary: "High latency in tenant-$TENANT_ID"
            description: "Tenant $TENANT_NAME has high latency (p95 > 500ms)"
    EOF

    # Create Grafana dashboard for the tenant
    log INFO "Creating Grafana dashboard for the tenant..."
    cat <<EOF | kubectl apply -f -
    apiVersion: v1
    kind: ConfigMap
    metadata:
      name: tenant-$TENANT_ID-dashboard
      namespace: monitoring
      labels:
        grafana_dashboard: "1"
        tenant: $TENANT_ID
    data:
      tenant-$TENANT_ID-dashboard.json: |
        {
          "annotations": {
            "list": [
              {
                "builtIn": 1,
                "datasource": "-- Grafana --",
                "enable": true,
                "hide": true,
                "iconColor": "rgba(0, 211, 255, 1)",
                "name": "Annotations & Alerts",
                "type": "dashboard"
              }
            ]
          },
          "editable": true,
          "gnetId": null,
          "graphTooltip": 0,
          "id": null,
          "links": [],
          "panels": [
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "Prometheus",
              "fieldConfig": {
                "defaults": {
                  "custom": {}
                },
                "overrides": []
              },
              "fill": 1,
              "fillGradient": 0,
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 0
              },
              "hiddenSeries": false,
              "id": 2,
              "legend": {
                "avg": false,
                "current": false,
                "max": false,
                "min": false,
                "show": true,
                "total": false,
                "values": false
              },
              "lines": true,
              "linewidth": 1,
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.2.0",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\\"tenant-$TENANT_ID\\"}[5m])) by (pod)",
                  "interval": "",
                  "legendFormat": "{{pod}}",
                  "refId": "A"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "CPU Usage",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                },
                {
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            },
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "Prometheus",
              "fieldConfig": {
                "defaults": {
                  "custom": {}
                },
                "overrides": []
              },
              "fill": 1,
              "fillGradient": 0,
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 0
              },
              "hiddenSeries": false,
              "id": 4,
              "legend": {
                "avg": false,
                "current": false,
                "max": false,
                "min": false,
                "show": true,
                "total": false,
                "values": false
              },
              "lines": true,
              "linewidth": 1,
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.2.0",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum(container_memory_usage_bytes{namespace=\\"tenant-$TENANT_ID\\"}) by (pod)",
                  "interval": "",
                  "legendFormat": "{{pod}}",
                  "refId": "A"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "Memory Usage",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "bytes",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                },
                {
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            },
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "Prometheus",
              "fieldConfig": {
                "defaults": {
                  "custom": {}
                },
                "overrides": []
              },
              "fill": 1,
              "fillGradient": 0,
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 0,
                "y": 8
              },
              "hiddenSeries": false,
              "id": 6,
              "legend": {
                "avg": false,
                "current": false,
                "max": false,
                "min": false,
                "show": true,
                "total": false,
                "values": false
              },
              "lines": true,
              "linewidth": 1,
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.2.0",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "sum(rate(istio_requests_total{namespace=\\"tenant-$TENANT_ID\\"}[5m])) by (destination_service)",
                  "interval": "",
                  "legendFormat": "{{destination_service}}",
                  "refId": "A"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "Request Rate",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                },
                {
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            },
            {
              "aliasColors": {},
              "bars": false,
              "dashLength": 10,
              "dashes": false,
              "datasource": "Prometheus",
              "fieldConfig": {
                "defaults": {
                  "custom": {}
                },
                "overrides": []
              },
              "fill": 1,
              "fillGradient": 0,
              "gridPos": {
                "h": 8,
                "w": 12,
                "x": 12,
                "y": 8
              },
              "hiddenSeries": false,
              "id": 8,
              "legend": {
                "avg": false,
                "current": false,
                "max": false,
                "min": false,
                "show": true,
                "total": false,
                "values": false
              },
              "lines": true,
              "linewidth": 1,
              "nullPointMode": "null",
              "options": {
                "alertThreshold": true
              },
              "percentage": false,
              "pluginVersion": "7.2.0",
              "pointradius": 2,
              "points": false,
              "renderer": "flot",
              "seriesOverrides": [],
              "spaceLength": 10,
              "stack": false,
              "steppedLine": false,
              "targets": [
                {
                  "expr": "histogram_quantile(0.95, sum(rate(istio_request_duration_milliseconds_bucket{namespace=\\"tenant-$TENANT_ID\\"}[5m])) by (destination_service, le))",
                  "interval": "",
                  "legendFormat": "{{destination_service}}",
                  "refId": "A"
                }
              ],
              "thresholds": [],
              "timeFrom": null,
              "timeRegions": [],
              "timeShift": null,
              "title": "Request Latency (p95)",
              "tooltip": {
                "shared": true,
                "sort": 0,
                "value_type": "individual"
              },
              "type": "graph",
              "xaxis": {
                "buckets": null,
                "mode": "time",
                "name": null,
                "show": true,
                "values": []
              },
              "yaxes": [
                {
                  "format": "ms",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                },
                {
                  "format": "short",
                  "label": null,
                  "logBase": 1,
                  "max": null,
                  "min": null,
                  "show": true
                }
              ],
              "yaxis": {
                "align": false,
                "alignLevel": null
              }
            }
          ],
          "refresh": "10s",
          "schemaVersion": 26,
          "style": "dark",
          "tags": [
            "tenant",
            "$TENANT_ID"
          ],
          "templating": {
            "list": []
          },
          "time": {
            "from": "now-1h",
            "to": "now"
          },
          "timepicker": {},
          "timezone": "",
          "title": "Tenant $TENANT_NAME Dashboard",
          "uid": "tenant-$TENANT_ID",
          "version": 1
        }
    EOF

    # Save credentials to a local file for reference
    echo "Tenant Name: $TENANT_NAME" > "tenant-$TENANT_ID-credentials.txt"
    echo "Tenant ID: $TENANT_ID" >> "tenant-$TENANT_ID-credentials.txt"
    echo "Subdomain: $SUBDOMAIN.$DOMAIN" >> "tenant-$TENANT_ID-credentials.txt"
    echo "Database Name: $DB_NAME" >> "tenant-$TENANT_ID-credentials.txt"
    echo "Database User: $DB_USER" >> "tenant-$TENANT_ID-credentials.txt"
    echo "Database Password: $DB_PASSWORD" >> "tenant-$TENANT_ID-credentials.txt"
    echo "RDS Host: $RDS_HOST" >> "tenant-$TENANT_ID-credentials.txt"
    echo "RDS Port: $RDS_PORT" >> "tenant-$TENANT_ID-credentials.txt"

    log INFO "Tenant '$TENANT_NAME' ($TENANT_ID) onboarded successfully!"
    log INFO "Tenant URL: https://$SUBDOMAIN.$DOMAIN"

    if [ -n "$S3_BUCKET" ] && [ -n "$S3_KEY" ]; then
      log INFO "Database imported successfully from s3://$S3_BUCKET/$S3_KEY"
      log INFO "Backend application is configured to use the imported database"
    else
      log WARN "No database was imported. Backend application may not function correctly."
    fi

    log INFO "Credentials saved to tenant-$TENANT_ID-credentials.txt"

    # Ensure that EKS pods in the tenant namespace can access the tenant database and S3 bucket
    log INFO "Ensuring that EKS pods can access tenant resources..."
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    chmod +x $SCRIPT_DIR/ensure-tenant-access.sh
    $SCRIPT_DIR/ensure-tenant-access.sh --tenant-id "$TENANT_ID" --rds-secret-name "$RDS_SECRET_NAME" --cluster-name "production-wks" --region "eu-central-1"

    # Print a summary of what was created
    log INFO "===== Tenant Onboarding Summary ====="
    log INFO "Tenant Name: $TENANT_NAME"
    log INFO "Tenant ID: $TENANT_ID"
    log INFO "Subdomain: $SUBDOMAIN.$DOMAIN"
    log INFO "Environment: $ENVIRONMENT"
    log INFO "Tier: $TIER"
    log INFO "Database: $DB_NAME"
    log INFO "Database Host: $RDS_HOST"
    log INFO "S3 Bucket for Assets: tenant-$TENANT_ID-assets"
    if [ -n "$S3_BUCKET" ] && [ -n "$S3_KEY" ]; then
      log INFO "Database Import: Yes (from s3://$S3_BUCKET/$S3_KEY)"
    else
      log INFO "Database Import: No"
    fi
    log INFO "=================================="

    # Set up tenant lifecycle management
    log INFO "Setting up tenant lifecycle management..."

    # Check if tenant lifecycle management script exists
    if [ -f "../lifecycle/tenant-lifecycle.sh" ]; then
      log INFO "Tenant lifecycle management script found. You can use it for:"
      log INFO "- Tenant upgrade/downgrade: ../lifecycle/tenant-lifecycle.sh --action upgrade/downgrade --tenant-id $TENANT_ID --target-tier <tier>"
      log INFO "- Tenant suspension: ../lifecycle/tenant-lifecycle.sh --action suspend --tenant-id $TENANT_ID"
      log INFO "- Tenant reactivation: ../lifecycle/tenant-lifecycle.sh --action reactivate --tenant-id $TENANT_ID"
      log INFO "- Tenant migration: ../lifecycle/tenant-lifecycle.sh --action migrate --tenant-id $TENANT_ID --target-env <environment>"
    else
      log WARN "Tenant lifecycle management script not found."
    fi

    # Set up advanced autoscaling features
    if [ "$ENABLE_KEDA" = true ]; then
      log INFO "Setting up KEDA for event-driven autoscaling..."

      # Check if KEDA setup script exists
      if [ -f "../autoscaling/setup-keda.sh" ]; then
        log INFO "Running KEDA setup script..."
        # Check if HPAs exist for the tenant
        if kubectl get hpa -n "tenant-$TENANT_ID" | grep -q "tenant-$TENANT_ID"; then
          log WARN "HPAs already exist for tenant $TENANT_ID. KEDA ScaledObjects may conflict with existing HPAs."
          log INFO "Running KEDA setup with awareness of existing HPAs..."
          ../autoscaling/setup-keda.sh --tenant-id "$TENANT_ID" --install-keda
        else
          log INFO "No HPAs found for tenant $TENANT_ID. Running KEDA setup..."
          ../autoscaling/setup-keda.sh --tenant-id "$TENANT_ID" --install-keda
        fi
      else
        log WARN "KEDA setup script not found. Skipping KEDA setup."
      fi
    fi

    if [ "$ENABLE_VPA" = true ]; then
      log INFO "Setting up Vertical Pod Autoscaler (VPA)..."

      # Check if VPA setup script exists
      if [ -f "../autoscaling/setup-vpa.sh" ]; then
        log INFO "Running VPA setup script..."
        ../autoscaling/setup-vpa.sh --tenant-id "$TENANT_ID" --install-vpa
      else
        log WARN "VPA setup script not found. Skipping VPA setup."
      fi
    fi

    if [ "$ENABLE_KARPENTER" = true ]; then
      log INFO "Setting up Karpenter for node autoscaling..."

      # Check if Karpenter setup script exists
      if [ -f "../autoscaling/setup-karpenter.sh" ]; then
        log INFO "Running Karpenter setup script..."
        # Run Karpenter setup script but don't fail if it fails
        ../autoscaling/setup-karpenter.sh --cluster-name "production-wks" --tenant-id "$TENANT_ID" --install-karpenter || log WARN "Karpenter setup failed, but continuing with tenant onboarding"
      else
        log WARN "Karpenter setup script not found. Skipping Karpenter setup."
      fi
    fi

    # Check if the new Python onboarding script exists
    if [ -f "$(dirname "$0")/tenant_onboarding.py" ]; then
      log INFO "Found new Python onboarding script. Using it for enhanced deployment..."
      
      # Generate a random password for the database if not already set
      if [ -z "$DB_PASSWORD" ]; then
        DB_PASSWORD=$(openssl rand -base64 12)
        log INFO "Generated random database password for enhanced deployment"
      fi
      
      # Check for SSL certificate and key files
      SSL_CERT_PATH="/etc/ssl/certs/architrave.crt"
      SSL_KEY_PATH="/etc/ssl/private/architrave.key"
      
      # Check if we need to create temporary certificate files
      if [ "$SKIP_SSL" = true ] || [ ! -f "$SSL_CERT_PATH" ] || [ ! -f "$SSL_KEY_PATH" ]; then
        log INFO "SSL certificate or key not found. Creating temporary ones for enhanced deployment..."
        
        # Create a temporary directory for certificate generation
        TEMP_DIR=$(mktemp -d)
        
        # Generate a self-signed certificate
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
          -keyout "$TEMP_DIR/architrave.key" \
          -out "$TEMP_DIR/architrave.crt" \
          -subj "/CN=*.$DOMAIN" \
          -addext "subjectAltName = DNS:*.$DOMAIN"
        
        SSL_CERT_PATH="$TEMP_DIR/architrave.crt"
        SSL_KEY_PATH="$TEMP_DIR/architrave.key"
        
        log INFO "Created temporary SSL certificate and key for enhanced deployment"
      fi
      
      # Determine SQL file path
      SQL_FILE_PATH="/tmp/architrave_1.45.2.sql"
      
      # If we need to download from S3, do it now
      if [ "$ENABLE_DATABASE_IMPORT" = true ] && [ ! -f "$SQL_FILE_PATH" ]; then
        log INFO "Downloading SQL file from S3 for enhanced deployment..."
        aws s3 cp "s3://$S3_BUCKET/$S3_KEY" "$SQL_FILE_PATH"
      fi
      
      # Select the appropriate images based on environment
      if [ "$ENVIRONMENT" == "production" ]; then
        BACKEND_IMAGE="************.dkr.ecr.eu-central-1.amazonaws.com/webapp_prod:2.0.41"
        FRONTEND_IMAGE="************.dkr.ecr.eu-central-1.amazonaws.com/nginx_prod:1.0.6-update_ssl"
        RABBITMQ_IMAGE="************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_prod:1.02"
        REPLICAS=2
      elif [ "$ENVIRONMENT" == "staging" ]; then
        BACKEND_IMAGE="************.dkr.ecr.eu-central-1.amazonaws.com/webapp_staging:2.0.41"
        FRONTEND_IMAGE="************.dkr.ecr.eu-central-1.amazonaws.com/nginx_staging:1.0.6-update_ssl"
        RABBITMQ_IMAGE="************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_staging:1.02"
        REPLICAS=1
      else
        BACKEND_IMAGE="************.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.41"
        FRONTEND_IMAGE="************.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl"
        RABBITMQ_IMAGE="************.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02"
        REPLICAS=1
      fi
      
      # Run the Python onboarding script
      log INFO "Running enhanced tenant onboarding with Python script..."
      python "$(dirname "$0")/tenant_onboarding.py" \
        --tenant-id "$TENANT_ID" \
        --tenant-name "$TENANT_NAME" \
        --db-password "$DB_PASSWORD" \
        --ssl-cert-path "$SSL_CERT_PATH" \
        --ssl-key-path "$SSL_KEY_PATH" \
        --sql-file "$SQL_FILE_PATH" \
        --backend-image "$BACKEND_IMAGE" \
        --frontend-image "$FRONTEND_IMAGE" \
        --rabbitmq-image "$RABBITMQ_IMAGE" \
        --replicas "$REPLICAS"
      
      # Clean up temporary files if needed
      if [ -d "$TEMP_DIR" ]; then
        rm -rf "$TEMP_DIR"
      fi
      
      log INFO "Enhanced tenant onboarding completed successfully!"
    else
      log WARN "New Python onboarding script not found. Using legacy deployment only."
    fi
    # === END MAIN ONBOARDING LOGIC ===
  )
  ONBOARD_EXIT_CODE=$?
  set -e
  if [ $ONBOARD_EXIT_CODE -eq 0 ]; then
    log INFO "Onboarding succeeded on attempt $ATTEMPT."
    exit 0
  else
    log ERROR "Onboarding failed on attempt $ATTEMPT (exit code $ONBOARD_EXIT_CODE)."
    log INFO "Running automated cleanup for tenant $TENANT_ID..."
    # Call delete_tenant.sh with --force to clean up
    if [ -n "$TENANT_ID" ]; then
      $SCRIPT_DIR/delete_tenant.sh --tenant-id "$TENANT_ID" --force || log WARN "Cleanup script failed or namespace already deleted."
    fi
    if [ $ATTEMPT -lt $MAX_RETRIES ]; then
      log INFO "Waiting $RETRY_DELAY seconds before retrying..."
      sleep $RETRY_DELAY
    fi
    ATTEMPT=$((ATTEMPT+1))
  fi

done
log ERROR "Onboarding failed after $MAX_RETRIES attempts. Exiting."
exit 1
