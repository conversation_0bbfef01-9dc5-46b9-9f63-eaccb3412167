package main

import (
	"bytes"
	"fmt"
	"os"
	"os/exec"
	"strings"
)

// Usage: go run preflight_autoscaling_check.go <tenant-id>
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run preflight_autoscaling_check.go <tenant-id>")
		os.Exit(1)
	}
	tenantID := os.Args[1]
	err := PreflightAutoscalingAndResourceCheck(tenantID)
	if err != nil {
		fmt.Fprintf(os.Stderr, "❌ Preflight check failed: %v\n", err)
		os.Exit(2)
	}
	fmt.Println("✅ Preflight autoscaling/resource check passed. Safe to onboard tenant.")
}

// PreflightAutoscalingAndResourceCheck checks cluster health and autoscaling conflicts for a tenant
func PreflightAutoscalingAndResourceCheck(tenantID string) error {
	namespace := fmt.Sprintf("tenant-%s", tenantID)

	// 1. Check node and pod capacity
	if err := runCmd("kubectl", "get", "nodes", "-o", "wide"); err != nil {
		return fmt.Errorf("failed to get nodes: %v", err)
	}
	if err := runCmd("kubectl", "top", "nodes"); err != nil {
		return fmt.Errorf("failed to get node metrics: %v", err)
	}

	// 2. Check for node pressure
	out, _ := exec.Command("kubectl", "get", "nodes", "-o", "json").Output()
	if bytes.Contains(out, []byte("DiskPressure")) || bytes.Contains(out, []byte("MemoryPressure")) {
		return fmt.Errorf("node(s) under disk or memory pressure, aborting onboarding")
	}

	// 3. Check autoscaler health
	for _, ns := range []string{"keda", "karpenter", "kube-system"} {
		exec.Command("kubectl", "get", "pods", "-n", ns).Run()
	}
	exec.Command("kubectl", "get", "pods", "-n", "kube-system", "-l", "k8s-app=metrics-server").Run()

	// 4. Detect and clean up conflicting HPAs
	hpaList, _ := exec.Command("kubectl", "get", "hpa", "-n", namespace, "-o", "jsonpath={.items[*].metadata.name}").Output()
	scaledObjList, _ := exec.Command("kubectl", "get", "scaledobject", "-n", namespace, "-o", "jsonpath={.items[*].metadata.name}").Output()
	for _, hpa := range strings.Fields(string(hpaList)) {
		for _, so := range strings.Fields(string(scaledObjList)) {
			if strings.HasPrefix(hpa, strings.TrimSuffix(so, "-scaler")) {
				// Conflict: HPA and KEDA ScaledObject for same deployment
				exec.Command("kubectl", "delete", "hpa", hpa, "-n", namespace).Run()
			}
		}
	}

	// 5. Check VPA mode
	vpaList, _ := exec.Command("kubectl", "get", "vpa", "-n", namespace, "-o", "jsonpath={.items[*].metadata.name}").Output()
	for _, vpa := range strings.Fields(string(vpaList)) {
		mode, _ := exec.Command("kubectl", "get", "vpa", vpa, "-n", namespace, "-o", "jsonpath={.spec.updatePolicy.updateMode}").Output()
		if string(mode) == "Auto" && len(hpaList) > 0 {
			// Optionally, patch VPA to Off or Initial
			exec.Command("kubectl", "patch", "vpa", vpa, "-n", namespace, "--type=json", "-p", `[{"op": "replace", "path": "/spec/updatePolicy/updateMode", "value":"Off"}]`).Run()
		}
	}

	// 6. Check for pending pods
	pods, _ := exec.Command("kubectl", "get", "pods", "-n", namespace, "--no-headers").Output()
	if bytes.Contains(pods, []byte("Pending")) {
		return fmt.Errorf("pending pods detected in %s, aborting onboarding", namespace)
	}

	return nil
}

func runCmd(name string, args ...string) error {
	cmd := exec.Command(name, args...)
	cmd.Stdout = nil
	cmd.Stderr = nil
	return cmd.Run()
} 