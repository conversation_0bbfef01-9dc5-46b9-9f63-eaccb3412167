#!/bin/bash

# Infrastructure Deployment Script
# This script deploys the infrastructure in stages to handle CRD dependencies

set -e

echo "🚀 Starting Infrastructure Deployment"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if terraform.tfvars exists
if [ ! -f "terraform.tfvars" ]; then
    print_error "terraform.tfvars file not found!"
    exit 1
fi

# Stage 1: Deploy core AWS infrastructure (excluding Kubernetes resources)
print_status "Stage 1: Deploying core AWS infrastructure..."
terraform apply -var-file=terraform.tfvars \
    -target=module.vpc \
    -target=module.alb \
    -target=module.rds \
    -target=module.kms \
    -target=module.eks \
    -target=module.bastion \
    -target=module.route53 \
    -target=module.acm \
    -target=module.dynamodb \
    -target=module.guardduty \
    -target=module.aws_config \
    -target=module.security \
    -auto-approve

if [ $? -eq 0 ]; then
    print_success "Core AWS infrastructure deployed successfully"
else
    print_error "Failed to deploy core AWS infrastructure"
    exit 1
fi

# Stage 2: Deploy Istio service mesh
print_status "Stage 2: Deploying Istio service mesh..."
terraform apply -var-file=terraform.tfvars \
    -target=module.istio \
    -auto-approve

if [ $? -eq 0 ]; then
    print_success "Istio service mesh deployed successfully"
else
    print_warning "Istio deployment failed, continuing with AWS resources only"
fi

# Stage 3: Deploy tenant CRDs first
print_status "Stage 3: Deploying tenant CRDs..."
terraform apply -var-file=terraform.tfvars \
    -target=module.tenant_management.kubernetes_manifest.tenant_crd \
    -target=module.tenant_management.time_sleep.wait_for_crd \
    -auto-approve

if [ $? -eq 0 ]; then
    print_success "Tenant CRDs deployed successfully"
    
    # Wait a bit more for CRD to be fully ready
    print_status "Waiting for CRD to be fully registered..."
    sleep 30
    
    # Stage 4: Deploy tenant operator
    print_status "Stage 4: Deploying tenant operator..."
    terraform apply -var-file=terraform.tfvars \
        -target=module.tenant_management.kubernetes_namespace.operator \
        -target=module.tenant_management.kubernetes_service_account.operator \
        -target=module.tenant_management.kubernetes_cluster_role.operator \
        -target=module.tenant_management.kubernetes_cluster_role_binding.operator \
        -target=module.tenant_management.kubernetes_deployment.operator \
        -target=module.tenant_management.kubernetes_service.operator \
        -auto-approve
    
    if [ $? -eq 0 ]; then
        print_success "Tenant operator deployed successfully"
        
        # Stage 5: Deploy tenant resources
        print_status "Stage 5: Deploying tenant resources..."
        terraform apply -var-file=terraform.tfvars \
            -target=module.tenant_management \
            -auto-approve
        
        if [ $? -eq 0 ]; then
            print_success "Tenant resources deployed successfully"
        else
            print_warning "Tenant resources deployment failed, but core infrastructure is ready"
        fi
    else
        print_warning "Tenant operator deployment failed"
    fi
else
    print_warning "CRD deployment failed, skipping tenant resources"
fi

# Stage 6: Deploy API management
print_status "Stage 6: Deploying API management..."
terraform apply -var-file=terraform.tfvars \
    -target=module.api_management \
    -auto-approve

if [ $? -eq 0 ]; then
    print_success "API management deployed successfully"
else
    print_warning "API management deployment failed"
fi

# Stage 7: Deploy remaining resources
print_status "Stage 7: Deploying remaining resources..."
terraform apply -var-file=terraform.tfvars -auto-approve

if [ $? -eq 0 ]; then
    print_success "All infrastructure deployed successfully!"
else
    print_warning "Some resources may have failed, but core infrastructure is ready"
fi

# Display outputs
print_status "Deployment Summary:"
terraform output

print_success "🎉 Infrastructure deployment completed!"
print_status "You can now access your infrastructure using the provided outputs."

# Optional: Display useful commands
echo ""
print_status "Useful commands:"
echo "  • Check EKS cluster: kubectl get nodes"
echo "  • Check Istio: kubectl get pods -n istio-system"
echo "  • Check tenants: kubectl get tenants"
echo "  • View dashboards: terraform output api_dashboard_url"
