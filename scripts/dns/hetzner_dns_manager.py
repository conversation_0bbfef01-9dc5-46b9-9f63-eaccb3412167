#!/usr/bin/env python3
"""
Hetzner DNS Manager
Manages DNS records using <PERSON><PERSON><PERSON>'s DNS API
"""

import os
import json
import logging
import requests
from typing import Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HetznerDNSManager:
    """Manages DNS records using <PERSON><PERSON><PERSON>'s DNS API."""
    
    def __init__(self, api_token: str, domain: str):
        """Initialize Hetzner DNS Manager."""
        self.api_token = api_token
        self.domain = domain
        self.base_url = "https://dns.hetzner.com/api/v1"
        self.headers = {
            "Auth-API-Token": self.api_token,
            "Content-Type": "application/json"
        }
        self.zone_id = self._get_zone_id()
        if not self.zone_id:
            raise ValueError(f"Could not find zone ID for domain: {domain}")
    
    def _get_zone_id(self) -> Optional[str]:
        """Get the zone ID for the domain."""
        try:
            response = requests.get(
                f"{self.base_url}/zones",
                headers=self.headers
            )
            response.raise_for_status()
            
            zones = response.json().get("zones", [])
            for zone in zones:
                if zone["name"] == self.domain:
                    return zone["id"]
            
            logger.error(f"❌ Zone not found for domain: {self.domain}")
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get zone ID: {e}")
            return None
    
    def create_record(self, name: str, value: str, record_type: str, ttl: int = 300) -> Tuple[bool, Optional[str]]:
        """Create a DNS record.
        
        Returns:
            Tuple[bool, Optional[str]]: (success, record_id)
        """
        try:
            data = {
                "zone_id": self.zone_id,
                "name": name,
                "value": value,
                "type": record_type,
                "ttl": ttl
            }
            
            response = requests.post(
                f"{self.base_url}/records",
                headers=self.headers,
                json=data
            )
            response.raise_for_status()
            
            record_id = response.json().get("record", {}).get("id")
            logger.info(f"✅ Created {record_type} record for {name}")
            return True, record_id
            
        except Exception as e:
            logger.error(f"❌ Failed to create record: {e}")
            return False, None
    
    def update_record(self, record_id: str, value: str, ttl: int = 300) -> bool:
        """Update a DNS record."""
        try:
            data = {
                "value": value,
                "ttl": ttl
            }
            
            response = requests.put(
                f"{self.base_url}/records/{record_id}",
                headers=self.headers,
                json=data
            )
            response.raise_for_status()
            
            logger.info(f"✅ Updated record {record_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to update record: {e}")
            return False
    
    def delete_record(self, record_id: str) -> bool:
        """Delete a DNS record."""
        try:
            response = requests.delete(
                f"{self.base_url}/records/{record_id}",
                headers=self.headers
            )
            response.raise_for_status()
            
            logger.info(f"✅ Deleted record {record_id}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to delete record: {e}")
            return False
    
    def get_records(self, record_id: Optional[str] = None) -> List[Dict]:
        """Get DNS records for the zone.
        
        Args:
            record_id: Optional record ID to filter by
            
        Returns:
            List of DNS records
        """
        try:
            params = {"zone_id": self.zone_id}
            if record_id:
                params["id"] = record_id
                
            response = requests.get(
                f"{self.base_url}/records",
                headers=self.headers,
                params=params
            )
            response.raise_for_status()
            
            return response.json().get("records", [])
            
        except Exception as e:
            logger.error(f"❌ Failed to get records: {e}")
            return []
    
    def setup_tenant_dns(self, tenant_id: str, subdomain: str, load_balancer_ip: str) -> Dict[str, str]:
        """Set up DNS records for a tenant.
        
        Args:
            tenant_id: Tenant identifier
            subdomain: Subdomain for the tenant
            load_balancer_ip: Load balancer IP address
            
        Returns:
            Dict containing record IDs for created records
        """
        record_ids = {}
        
        try:
            # Create A record for subdomain
            success, a_record_id = self.create_record(
                name=subdomain,
                value=load_balancer_ip,
                record_type="A"
            )
            if not success:
                raise Exception("Failed to create A record")
            record_ids["a_record"] = a_record_id
            
            # Create wildcard CNAME record
            success, cname_record_id = self.create_record(
                name=f"*.{subdomain}",
                value=f"{subdomain}.{self.domain}",
                record_type="CNAME"
            )
            if not success:
                raise Exception("Failed to create CNAME record")
            record_ids["wildcard_record"] = cname_record_id
            
            logger.info(f"✅ DNS records created for tenant {tenant_id}")
            return record_ids
            
        except Exception as e:
            logger.error(f"❌ Failed to set up tenant DNS: {e}")
            # Clean up any created records
            for record_id in record_ids.values():
                self.delete_record(record_id)
            return {}
    
    def setup_acm_validation(self, validation_records: List[Dict]) -> Dict[str, str]:
        """Set up ACM certificate validation records.
        
        Args:
            validation_records: List of validation records from ACM
            
        Returns:
            Dict containing record IDs for created validation records
        """
        record_ids = {}
        
        try:
            for record in validation_records:
                success, record_id = self.create_record(
                    name=record["name"],
                    value=record["value"],
                    record_type=record["type"]
                )
                if not success:
                    raise Exception(f"Failed to create validation record for {record['name']}")
                record_ids[record["name"]] = record_id
            
            logger.info("✅ ACM validation records created")
            return record_ids
            
        except Exception as e:
            logger.error(f"❌ Failed to set up ACM validation records: {e}")
            # Clean up any created records
            for record_id in record_ids.values():
                self.delete_record(record_id)
            return {}
    
    def verify_tenant_dns(self, tenant_id: str, record_ids: Dict[str, str]) -> Dict[str, bool]:
        """Verify DNS records for a tenant.
        
        Args:
            tenant_id: Tenant identifier
            record_ids: Dict of record IDs to verify
            
        Returns:
            Dict containing verification results
        """
        results = {
            "a_record": False,
            "wildcard_record": False,
            "all_records": False
        }
        
        try:
            # Verify A record
            a_records = self.get_records(record_ids.get("a_record"))
            results["a_record"] = len(a_records) > 0
            
            # Verify wildcard CNAME record
            cname_records = self.get_records(record_ids.get("wildcard_record"))
            results["wildcard_record"] = len(cname_records) > 0
            
            # All records are valid if both checks pass
            results["all_records"] = results["a_record"] and results["wildcard_record"]
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Failed to verify tenant DNS: {e}")
            return results

def main():
    """Main function for testing Hetzner DNS Manager."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Hetzner DNS Manager')
    parser.add_argument('--api-token', required=True, help='Hetzner DNS API token')
    parser.add_argument('--domain', required=True, help='Domain name')
    parser.add_argument('--tenant-id', required=True, help='Tenant ID')
    parser.add_argument('--subdomain', required=True, help='Subdomain for tenant')
    parser.add_argument('--load-balancer-ip', required=True, help='Load balancer IP')
    
    args = parser.parse_args()
    
    try:
        # Initialize Hetzner DNS Manager
        dns_manager = HetznerDNSManager(args.api_token, args.domain)
        
        # Set up tenant DNS
        record_ids = dns_manager.setup_tenant_dns(
            args.tenant_id,
            args.subdomain,
            args.load_balancer_ip
        )
        
        if record_ids:
            logger.info("✅ Tenant DNS setup completed successfully")
            # Verify the records
            verification = dns_manager.verify_tenant_dns(args.tenant_id, record_ids)
            if verification["all_records"]:
                logger.info("✅ DNS verification successful")
            else:
                logger.error("❌ DNS verification failed")
        else:
            logger.error("❌ Tenant DNS setup failed")
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 