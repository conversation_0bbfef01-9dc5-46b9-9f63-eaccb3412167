#!/bin/bash

# Comprehensive Autoscaling Monitor and Fix Script
# This script monitors and fixes KEDA, Karpenter, and Cluster Autoscaler issues

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed or not in PATH"
        exit 1
    fi
}

# Check if helm is available
check_helm() {
    if ! command -v helm &> /dev/null; then
        log_warning "helm is not installed - some fixes may not work"
    fi
}

# Check KEDA status
check_keda() {
    log_info "Checking KEDA status..."
    
    # Check if KEDA namespace exists (try both keda and keda-system)
    local keda_namespace=""
    if kubectl get namespace keda &> /dev/null; then
        keda_namespace="keda"
    elif kubectl get namespace keda-system &> /dev/null; then
        keda_namespace="keda-system"
    else
        log_warning "KEDA namespace not found"
        return 1
    fi
    
    # Check KEDA operator pods
    local operator_pods=$(kubectl get pods -n "$keda_namespace" -l app.kubernetes.io/name=keda-operator --field-selector=status.phase=Running --no-headers 2>/dev/null | wc -l)
    if [ "$operator_pods" -eq 0 ]; then
        log_warning "KEDA operator not running"
        return 1
    fi
    
    # Check KEDA metrics server
    local metrics_pods=$(kubectl get pods -n "$keda_namespace" -l app.kubernetes.io/name=keda-operator-metrics-apiserver --field-selector=status.phase=Running --no-headers 2>/dev/null | wc -l)
    if [ "$metrics_pods" -eq 0 ]; then
        log_warning "KEDA metrics server not running"
        return 1
    fi
    
    log_success "KEDA is healthy ($operator_pods operator pods, $metrics_pods metrics server pods) in namespace $keda_namespace"
    return 0
}

# Install KEDA
install_keda() {
    log_info "Installing KEDA..."
    
    if ! command -v helm &> /dev/null; then
        log_error "Helm is required to install KEDA"
        return 1
    fi
    
    # Add KEDA Helm repository
    helm repo add kedacore https://kedacore.github.io/charts
    helm repo update
    
    # Install KEDA
    helm upgrade --install keda kedacore/keda \
        --namespace keda \
        --create-namespace \
        --wait \
        --timeout 5m
    
    log_success "KEDA installed successfully"
}

# Fix KEDA issues
fix_keda() {
    log_info "Attempting to fix KEDA..."
    
    # Determine KEDA namespace
    local keda_namespace=""
    if kubectl get namespace keda &> /dev/null; then
        keda_namespace="keda"
    elif kubectl get namespace keda-system &> /dev/null; then
        keda_namespace="keda-system"
    else
        log_error "KEDA namespace not found"
        return 1
    fi
    
    # Restart KEDA operator pods
    kubectl delete pods -n "$keda_namespace" -l app.kubernetes.io/name=keda-operator --force --grace-period=0 2>/dev/null || true
    
    # Wait for KEDA to be ready
    log_info "Waiting for KEDA to restart..."
    sleep 30
    
    # Check if KEDA is now healthy
    if check_keda; then
        log_success "KEDA fixed successfully"
        return 0
    else
        log_error "KEDA still not healthy after restart"
        return 1
    fi
}

# Check Karpenter status
check_karpenter() {
    log_info "Checking Karpenter status..."
    
    # Check if Karpenter namespace exists
    if ! kubectl get namespace karpenter &> /dev/null; then
        log_warning "Karpenter namespace not found"
        return 1
    fi
    
    # Check Karpenter controller pods
    local controller_pods=$(kubectl get pods -n karpenter -l app.kubernetes.io/name=karpenter --field-selector=status.phase=Running --no-headers 2>/dev/null | wc -l)
    if [ "$controller_pods" -eq 0 ]; then
        log_warning "Karpenter controller not running"
        return 1
    fi
    
    # Check if provisioner exists
    local provisioners=$(kubectl get provisioner -n karpenter --no-headers 2>/dev/null | wc -l)
    if [ "$provisioners" -eq 0 ]; then
        log_warning "No Karpenter provisioners found"
        return 1
    fi
    
    log_success "Karpenter is healthy ($controller_pods controller pods, $provisioners provisioners)"
    return 0
}

# Install Karpenter
install_karpenter() {
    log_info "Installing Karpenter..."
    
    if ! command -v helm &> /dev/null; then
        log_error "Helm is required to install Karpenter"
        return 1
    fi
    
    # Add Karpenter Helm repository
    helm repo add karpenter https://charts.karpenter.sh
    helm repo update
    
    # Install Karpenter
    helm upgrade --install karpenter karpenter/karpenter \
        --namespace karpenter \
        --create-namespace \
        --wait \
        --timeout 5m
    
    log_success "Karpenter installed successfully"
}

# Create Karpenter provisioner
create_karpenter_provisioner() {
    log_info "Creating Karpenter provisioner..."
    
    cat <<EOF | kubectl apply -f -
apiVersion: karpenter.sh/v1alpha5
kind: Provisioner
metadata:
  name: default
  namespace: karpenter
spec:
  requirements:
    - key: karpenter.sh/capacity-type
      operator: In
      values: ["on-demand", "spot"]
    - key: kubernetes.io/arch
      operator: In
      values: ["amd64"]
    - key: node.kubernetes.io/instance-type
      operator: In
      values: ["t3.medium", "t3.large", "m5.large", "m5.xlarge"]
  limits:
    resources:
      cpu: 100
      memory: 100Gi
  ttlSecondsAfterEmpty: 60
  ttlSecondsUntilExpired: 2592000
EOF
    
    log_success "Karpenter provisioner created successfully"
}

# Fix Karpenter issues
fix_karpenter() {
    log_info "Attempting to fix Karpenter..."
    
    # Restart Karpenter controller pods
    kubectl delete pods -n karpenter -l app.kubernetes.io/name=karpenter --force --grace-period=0 2>/dev/null || true
    
    # Wait for Karpenter to be ready
    log_info "Waiting for Karpenter to restart..."
    sleep 30
    
    # Check if Karpenter is now healthy
    if check_karpenter; then
        log_success "Karpenter fixed successfully"
        return 0
    else
        log_error "Karpenter still not healthy after restart"
        return 1
    fi
}

# Check Cluster Autoscaler status
check_cluster_autoscaler() {
    log_info "Checking Cluster Autoscaler status..."
    
    # Check Cluster Autoscaler pods
    local autoscaler_pods=$(kubectl get pods -n kube-system -l app=cluster-autoscaler --field-selector=status.phase=Running --no-headers 2>/dev/null | wc -l)
    if [ "$autoscaler_pods" -eq 0 ]; then
        log_warning "Cluster Autoscaler not running"
        return 1
    fi
    
    log_success "Cluster Autoscaler is healthy ($autoscaler_pods pods)"
    return 0
}

# Fix Cluster Autoscaler issues
fix_cluster_autoscaler() {
    log_info "Attempting to fix Cluster Autoscaler..."
    
    # Restart Cluster Autoscaler pods
    kubectl delete pods -n kube-system -l app=cluster-autoscaler --force --grace-period=0 2>/dev/null || true
    
    # Wait for Cluster Autoscaler to be ready
    log_info "Waiting for Cluster Autoscaler to restart..."
    sleep 30
    
    # Check if Cluster Autoscaler is now healthy
    if check_cluster_autoscaler; then
        log_success "Cluster Autoscaler fixed successfully"
        return 0
    else
        log_error "Cluster Autoscaler still not healthy after restart"
        return 1
    fi
}

# Check node capacity and pressure
check_node_status() {
    log_info "Checking node status..."
    
    # Get total nodes
    local total_nodes=$(kubectl get nodes --no-headers 2>/dev/null | wc -l)
    log_info "Total nodes: $total_nodes"
    
    # Check for nodes under pressure
    local pressure_nodes=$(kubectl get nodes -o json 2>/dev/null | jq -r '.items[] | select(.status.conditions[] | select(.type=="DiskPressure" and .status=="True")) | .metadata.name' 2>/dev/null | wc -l)
    if [ "$pressure_nodes" -gt 0 ]; then
        log_warning "Found $pressure_nodes nodes under disk pressure"
        kubectl get nodes -o json 2>/dev/null | jq -r '.items[] | select(.status.conditions[] | select(.type=="DiskPressure" and .status=="True")) | .metadata.name' 2>/dev/null
    else
        log_success "No nodes under pressure"
    fi
    
    # Check for pending pods
    local pending_pods=$(kubectl get pods --all-namespaces --field-selector=status.phase=Pending --no-headers 2>/dev/null | wc -l)
    if [ "$pending_pods" -gt 0 ]; then
        log_warning "Found $pending_pods pending pods"
        kubectl get pods --all-namespaces --field-selector=status.phase=Pending --no-headers 2>/dev/null | head -5
    else
        log_success "No pending pods"
    fi
}

# Check KEDA ScaledObjects
check_keda_scaledobjects() {
    log_info "Checking KEDA ScaledObjects..."
    
    local scaledobjects=$(kubectl get scaledobject --all-namespaces --no-headers 2>/dev/null | wc -l)
    if [ "$scaledobjects" -eq 0 ]; then
        log_warning "No KEDA ScaledObjects found"
        return 1
    fi
    
    log_success "Found $scaledobjects KEDA ScaledObjects"
    
    # List ScaledObjects by namespace
    kubectl get scaledobject --all-namespaces --no-headers 2>/dev/null | while read -r line; do
        log_info "  $line"
    done
    
    return 0
}

# Check metrics server
check_metrics_server() {
    log_info "Checking metrics server..."
    
    # Try to get node metrics
    if kubectl top nodes --no-headers &> /dev/null; then
        log_success "Metrics server is working"
        return 0
    else
        log_warning "Metrics server not working or not installed"
        return 1
    fi
}

# Clean up conflicting HPAs
cleanup_conflicting_hpas() {
    log_info "Checking for conflicting HPAs..."
    
    # Find HPAs that might conflict with KEDA ScaledObjects
    local conflicting_hpas=$(kubectl get hpa --all-namespaces --no-headers 2>/dev/null | grep -E "(backend|frontend)" | wc -l)
    if [ "$conflicting_hpas" -gt 0 ]; then
        log_warning "Found $conflicting_hpas potentially conflicting HPAs"
        kubectl get hpa --all-namespaces --no-headers 2>/dev/null | grep -E "(backend|frontend)"
        
        read -p "Do you want to delete these HPAs? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            kubectl get hpa --all-namespaces --no-headers 2>/dev/null | grep -E "(backend|frontend)" | awk '{print "kubectl delete hpa " $2 " -n " $1}' | bash
            log_success "Conflicting HPAs deleted"
        fi
    else
        log_success "No conflicting HPAs found"
    fi
}

# Main monitoring function
monitor_autoscaling() {
    log_info "Starting comprehensive autoscaling monitoring..."
    
    local issues_found=0
    
    # Check KEDA
    if ! check_keda; then
        issues_found=$((issues_found + 1))
    fi
    
    # Check Karpenter
    if ! check_karpenter; then
        issues_found=$((issues_found + 1))
    fi
    
    # Check Cluster Autoscaler
    if ! check_cluster_autoscaler; then
        issues_found=$((issues_found + 1))
    fi
    
    # Check node status
    check_node_status
    
    # Check KEDA ScaledObjects
    if ! check_keda_scaledobjects; then
        issues_found=$((issues_found + 1))
    fi
    
    # Check metrics server
    if ! check_metrics_server; then
        issues_found=$((issues_found + 1))
    fi
    
    # Check for conflicting HPAs
    cleanup_conflicting_hpas
    
    if [ "$issues_found" -eq 0 ]; then
        log_success "All autoscaling components are healthy!"
        return 0
    else
        log_warning "Found $issues_found autoscaling issues"
        return 1
    fi
}

# Auto-fix function
auto_fix() {
    log_info "Starting automatic autoscaling fixes..."
    
    # Fix KEDA
    if ! check_keda; then
        log_info "Attempting to fix KEDA..."
        if ! fix_keda; then
            log_info "Trying to install KEDA..."
            install_keda
        fi
    fi
    
    # Fix Karpenter
    if ! check_karpenter; then
        log_info "Attempting to fix Karpenter..."
        if ! fix_karpenter; then
            log_info "Trying to install Karpenter..."
            install_karpenter
            create_karpenter_provisioner
        fi
    fi
    
    # Fix Cluster Autoscaler
    if ! check_cluster_autoscaler; then
        log_info "Attempting to fix Cluster Autoscaler..."
        fix_cluster_autoscaler
    fi
    
    log_success "Auto-fix completed"
}

# Show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -m, --monitor    Monitor autoscaling components (default)"
    echo "  -f, --fix        Automatically fix autoscaling issues"
    echo "  -i, --install    Install missing autoscaling components"
    echo "  -h, --help       Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                    # Monitor autoscaling components"
    echo "  $0 --fix              # Auto-fix autoscaling issues"
    echo "  $0 --install          # Install missing components"
}

# Main script
main() {
    # Parse command line arguments
    local action="monitor"
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--monitor)
                action="monitor"
                shift
                ;;
            -f|--fix)
                action="fix"
                shift
                ;;
            -i|--install)
                action="install"
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Check prerequisites
    check_kubectl
    check_helm
    
    # Execute action
    case $action in
        "monitor")
            monitor_autoscaling
            ;;
        "fix")
            auto_fix
            monitor_autoscaling
            ;;
        "install")
            install_keda
            install_karpenter
            create_karpenter_provisioner
            monitor_autoscaling
            ;;
    esac
}

# Run main function
main "$@" 