apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: enhanced-alerts
  namespace: monitoring
spec:
  groups:
  - name: performance-alerts
    rules:
    - alert: HighLatency
      expr: custom:api_latency:percentile_95 > 1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High API latency detected
        description: "API endpoint {{ $labels.endpoint }} has 95th percentile latency of {{ $value }}s for 5m"

    - alert: HighErrorRate
      expr: custom:error_rate > 0.05
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: High error rate detected
        description: "Error rate is {{ $value | humanizePercentage }} for 5m"

    - alert: HighResourceUsage
      expr: custom:tenant_resource_usage:ratio > 0.85
      for: 10m
      labels:
        severity: warning
      annotations:
        summary: High resource usage detected
        description: "Tenant {{ $labels.tenant }} is using {{ $value | humanizePercentage }} of resources"

  - name: database-alerts
    rules:
    - alert: DatabaseConnectionHigh
      expr: custom:database_connections:ratio > 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High database connection usage
        description: "Database {{ $labels.database }} has {{ $value | humanizePercentage }} connection usage"

    - alert: DatabaseConnectionCritical
      expr: custom:database_connections:ratio > 0.95
      for: 2m
      labels:
        severity: critical
      annotations:
        summary: Critical database connection usage
        description: "Database {{ $labels.database }} has {{ $value | humanizePercentage }} connection usage"

  - name: cache-alerts
    rules:
    - alert: LowCacheHitRatio
      expr: custom:cache_hit_ratio < 0.7
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: Low cache hit ratio
        description: "Cache hit ratio is {{ $value | humanizePercentage }} for 15m"

  - name: system-alerts
    rules:
    - alert: HighMemoryUsage
      expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High memory usage
        description: "Node {{ $labels.node }} has {{ $value | humanizePercentage }} memory usage"

    - alert: HighCPUUsage
      expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 85
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High CPU usage
        description: "Node {{ $labels.node }} has {{ $value | humanizePercentage }} CPU usage"

    - alert: DiskSpaceFilling
      expr: predict_linear(node_filesystem_free_bytes{mountpoint="/"}[1h], 24*3600) < 0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: Disk space filling up
        description: "Disk on {{ $labels.node }} will fill up within 24h" 