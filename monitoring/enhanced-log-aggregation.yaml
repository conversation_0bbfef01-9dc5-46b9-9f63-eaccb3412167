apiVersion: v1
kind: ConfigMap
metadata:
  name: enhanced-fluentd-config
  namespace: monitoring
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/*.log
      pos_file /var/log/fluentd-containers.log.pos
      tag kubernetes.*
      read_from_head true
      <parse>
        @type json
        time_key time
        time_format %Y-%m-%dT%H:%M:%S.%NZ
      </parse>
    </source>

    <filter kubernetes.**>
      @type kubernetes_metadata
      @id filter_k8s_metadata
    </filter>

    <filter kubernetes.**>
      @type record_transformer
      @id filter_containers_stream
      <record>
        stream_name ${record['stream']}
        pod_name ${record['kubernetes']['pod_name']}
        container_name ${record['kubernetes']['container_name']}
        namespace ${record['kubernetes']['namespace_name']}
        tenant ${record['kubernetes']['labels']['tenant']}
        app ${record['kubernetes']['labels']['app']}
        severity ${record['level'] || 'INFO'}
        message ${record['log']}
      </record>
    </filter>

    <filter kubernetes.**>
      @type grep
      <rule>
        key severity
        pattern /ERROR|WARN|CRITICAL/
        tag error_logs
      </rule>
    </filter>

    <match kubernetes.**>
      @type elasticsearch
      @id out_es
      @log_level info
      include_tag_key true
      host elasticsearch-master
      port 9200
      logstash_format true
      logstash_prefix k8s
      <buffer>
        @type file
        path /var/log/fluentd-buffers/kubernetes.buffer
        flush_mode interval
        timekey 1m
        timekey_wait 30s
        timekey_use_utc true
        flush_interval 5s
        retry_type exponential_backoff
        retry_forever false
        retry_max_interval 30
        chunk_limit_size 2M
        queue_limit_length 32
        overflow_action block
      </buffer>
    </match>

    <match error_logs>
      @type elasticsearch
      @id out_es_errors
      @log_level info
      include_tag_key true
      host elasticsearch-master
      port 9200
      logstash_format true
      logstash_prefix k8s-errors
      <buffer>
        @type file
        path /var/log/fluentd-buffers/error.buffer
        flush_mode interval
        timekey 1m
        timekey_wait 30s
        timekey_use_utc true
        flush_interval 5s
        retry_type exponential_backoff
        retry_forever false
        retry_max_interval 30
        chunk_limit_size 2M
        queue_limit_length 32
        overflow_action block
      </buffer>
    </match>
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: enhanced-fluentd
  namespace: monitoring
  labels:
    app: enhanced-fluentd
spec:
  selector:
    matchLabels:
      app: enhanced-fluentd
  template:
    metadata:
      labels:
        app: enhanced-fluentd
    spec:
      serviceAccount: fluentd
      serviceAccountName: fluentd
      tolerations:
      - key: node-role.kubernetes.io/master
        effect: NoSchedule
      containers:
      - name: fluentd
        image: fluent/fluentd-kubernetes-daemonset:v1-debian-elasticsearch
        env:
          - name:  FLUENT_ELASTICSEARCH_HOST
            value: "elasticsearch-master"
          - name:  FLUENT_ELASTICSEARCH_PORT
            value: "9200"
          - name: FLUENT_ELASTICSEARCH_SCHEME
            value: "http"
          - name: FLUENT_ELASTICSEARCH_USER
            value: "elastic"
          - name: FLUENT_ELASTICSEARCH_PASSWORD
            valueFrom:
              secretKeyRef:
                name: elasticsearch-credentials
                key: password
        resources:
          limits:
            memory: 1Gi
          requests:
            cpu: 200m
            memory: 400Mi
        volumeMounts:
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: fluentd-config
          mountPath: /fluentd/etc/fluent.conf
          subPath: fluent.conf
      volumes:
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: fluentd-config
        configMap:
          name: enhanced-fluentd-config 