apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cert-manager
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cert-manager
  namespaceSelector:
    matchNames:
      - cert-manager
  endpoints:
  - port: metrics
    interval: 30s
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: opa
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: opa
  namespaceSelector:
    matchNames:
      - kube-system
  endpoints:
  - port: metrics
    interval: 30s
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: istio
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: istiod
  namespaceSelector:
    matchNames:
      - istio-system
  endpoints:
  - port: metrics
    interval: 30s
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: security-alerts
  namespace: monitoring
spec:
  groups:
  - name: security.rules
    rules:
    - alert: CertificateExpiringSoon
      expr: cert_manager_certificate_expiration_timestamp_seconds - time() < 2592000
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: Certificate expiring soon
        description: Certificate {{ $labels.name }} in namespace {{ $labels.namespace }} will expire in less than 30 days
    - alert: OPAWebhookFailure
      expr: rate(opa_webhook_denied_total[5m]) > 0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: OPA webhook failures detected
        description: OPA webhook is denying requests at a rate of {{ $value }} per second
    - alert: mTLSIssues
      expr: rate(istio_requests_total{response_code=~"5.*"}[5m]) > 0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: mTLS communication issues detected
        description: Service {{ $labels.destination_service }} is experiencing mTLS communication issues 