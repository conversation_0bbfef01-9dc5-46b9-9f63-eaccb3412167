apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: custom-metrics
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: metrics-exporter
  endpoints:
  - port: metrics
    interval: 15s
    path: /metrics
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'custom_.*'
      action: keep
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: custom-metrics-rules
  namespace: monitoring
data:
  custom-metrics.rules: |
    groups:
    - name: custom-metrics
      rules:
      - record: custom:tenant_resource_usage:ratio
        expr: sum(tenant_resource_usage) by (tenant) / sum(tenant_resource_limit) by (tenant)
      
      - record: custom:api_latency:percentile_95
        expr: histogram_quantile(0.95, sum(rate(api_request_duration_seconds_bucket[5m])) by (le, endpoint))
      
      - record: custom:database_connections:ratio
        expr: sum(database_connections) by (database) / sum(database_connection_limit) by (database)
      
      - record: custom:cache_hit_ratio
        expr: sum(rate(cache_hits_total[5m])) / sum(rate(cache_requests_total[5m]))
      
      - record: custom:error_rate
        expr: sum(rate(http_requests_total{status=~"5.."}[5m])) / sum(rate(http_requests_total[5m])) 