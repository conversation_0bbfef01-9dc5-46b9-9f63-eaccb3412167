apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: enhanced-metrics
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: metrics-exporter
  endpoints:
  - port: metrics
    interval: 15s
    path: /metrics
    metricRelabelings:
    - sourceLabels: [__name__]
      regex: 'container_(cpu|memory)_usage_.*'
      action: keep
    - sourceLabels: [__name__]
      regex: 'node_(cpu|memory|disk|network)_.*'
      action: keep
    - sourceLabels: [__name__]
      regex: 'kube_(pod|node|namespace)_.*'
      action: keep
    - sourceLabels: [__name__]
      regex: 'custom:tenant_.*'
      action: keep
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: enhanced-alerts
  namespace: monitoring
spec:
  groups:
  - name: enhanced.rules
    rules:
    # Performance Metrics
    - alert: HighLatency
      expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le)) > 1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High latency detected
        description: 95th percentile latency is above 1s for 5 minutes

    # Resource Metrics
    - alert: MemoryPressure
      expr: (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes) < 0.1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High memory pressure
        description: Node {{ $labels.node }} has less than 10% memory available

    # Network Metrics
    - alert: HighNetworkErrors
      expr: rate(node_network_transmit_errs_total[5m]) > 0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: Network errors detected
        description: Node {{ $labels.node }} is experiencing network errors

    # Disk Metrics
    - alert: DiskSpaceFilling
      expr: predict_linear(node_filesystem_free_bytes[1h], 24*3600) < 0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: Disk space filling up
        description: Disk on {{ $labels.node }} will be full in 24 hours

    # Application Metrics
    - alert: HighErrorRate
      expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: High error rate
        description: Error rate is above 5% for 5 minutes

    # Business Metrics
    - alert: HighAPILatency
      expr: histogram_quantile(0.95, sum(rate(api_request_duration_seconds_bucket[5m])) by (le, endpoint)) > 2
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: API latency high
        description: API endpoint {{ $labels.endpoint }} has high latency

    # Custom Metrics
    - alert: TenantResourceUsage
      expr: tenant_resource_usage_percent > 90
      for: 15m
      labels:
        severity: warning
      annotations:
        summary: Tenant resource usage high
        description: Tenant {{ $labels.tenant }} is using more than 90% of allocated resources

  - name: tenant-alerts
    rules:
    - alert: HighTenantLatency
      expr: custom:tenant_api_latency:percentile_95 > 1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High API latency for tenant
        description: "Tenant {{ $labels.tenant }} has high API latency (95th percentile > 1s) for 5 minutes"
    
    - alert: HighTenantErrorRate
      expr: custom:tenant_error_rate > 0.05
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High error rate for tenant
        description: "Tenant {{ $labels.tenant }} has high error rate (>5%) for 5 minutes"
    
    - alert: TenantResourceExhaustion
      expr: custom:tenant_resource_usage:ratio > 0.9
      for: 10m
      labels:
        severity: critical
      annotations:
        summary: Tenant resource usage critical
        description: "Tenant {{ $labels.tenant }} is using more than 90% of allocated resources for 10 minutes"
    
    - alert: TenantDatabaseConnectionsHigh
      expr: custom:tenant_database_connections:ratio > 0.8
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High database connections for tenant
        description: "Tenant {{ $labels.tenant }} has high database connection usage (>80%) for 5 minutes"
    
    - alert: TenantCostSpike
      expr: custom:tenant_cost:rate > 1000
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: Cost spike detected for tenant
        description: "Tenant {{ $labels.tenant }} has cost spike (>$1000/hour) for 1 hour"
    
    - alert: TenantStorageQuota
      expr: custom:tenant_storage_usage:ratio > 0.9
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: Tenant storage quota warning
        description: "Tenant {{ $labels.tenant }} is using more than 90% of allocated storage for 5 minutes"
    
    - alert: TenantNetworkUsage
      expr: custom:tenant_network_usage:rate > 1000000
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High network usage for tenant
        description: "Tenant {{ $labels.tenant }} has high network usage (>1MB/s) for 5 minutes"
    
    - alert: TenantPodRestarts
      expr: increase(kube_pod_container_status_restarts_total{tenant=~".+"}[1h]) > 5
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High pod restarts for tenant
        description: "Tenant {{ $labels.tenant }} has more than 5 pod restarts in the last hour" 