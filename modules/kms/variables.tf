variable "environment" {
  description = "Deployment Environment"
  type        = string
}

variable "tenants" {
  description = "Map of Tenants"
  type        = any
}

variable "administrator_arns" {
  description = "List of IAM ARNs for administrators"
  type        = list(string)
}

variable "user_arns" {
  description = "List of IAM ARNs for users (e.g., backend pods)"
  type        = list(string)
}

variable "backend_pod_role_arn" {
  description = "ARN of the IAM role for backend pods"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
}

# Add this to your existing variables.tf file in the KMS module
variable "tenant_iam_role" {
  description = "The IAM role name for tenant access to KMS keys"
  type        = string
  default     = ""
}

variable "key_deletion_window_in_days" {
  description = "Duration in days after which the key is deleted after destruction of the resource"
  type        = number
  default     = 7 # Minimum required by AWS for easier cleanup during development
  validation {
    condition     = var.key_deletion_window_in_days >= 7 && var.key_deletion_window_in_days <= 30
    error_message = "Key deletion window must be between 7 and 30 days."
  }
}

variable "import_existing_resources" {
  description = "Whether to import existing resources instead of creating new ones"
  type        = bool
  default     = true
}

variable "allowed_ip_ranges" {
  description = "List of allowed IP ranges for KMS access"
  type        = list(string)
  default     = ["10.0.0.0/8", "**********/12", "***********/16"] # Default to private IP ranges
}
