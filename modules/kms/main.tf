// Add these data sources at the top of the file
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

data "aws_iam_policy_document" "tenant_key_policy" {
  for_each = var.tenants

  # Root account access - required by AWS
  statement {
    sid    = "EnableRootAccountAccess"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:Create*",
      "kms:Describe*",
      "kms:Enable*",
      "kms:List*",
      "kms:Put*",
      "kms:Update*",
      "kms:Revoke*",
      "kms:Disable*",
      "kms:Get*",
      "kms:Delete*",
      "kms:TagResource",
      "kms:UntagResource",
      "kms:ScheduleKeyDeletion",
      "kms:CancelKeyDeletion"
    ]
    resources = ["*"]
    condition {
      test     = "Bool"
      variable = "aws:SecureTransport"
      values   = ["true"]
    }
  }

  # Administrator access with MFA
  statement {
    sid    = "AllowAdministratorAccess"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = length(var.administrator_arns) > 0 ? var.administrator_arns : ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:Create*",
      "kms:Describe*",
      "kms:Enable*",
      "kms:List*",
      "kms:Put*",
      "kms:Update*",
      "kms:Revoke*",
      "kms:Disable*",
      "kms:Get*",
      "kms:Delete*",
      "kms:TagResource",
      "kms:UntagResource",
      "kms:ScheduleKeyDeletion",
      "kms:CancelKeyDeletion"
    ]
    resources = ["*"]
    condition {
      test     = "Bool"
      variable = "aws:SecureTransport"
      values   = ["true"]
    }
    condition {
      test     = "Bool"
      variable = "aws:MultiFactorAuthPresent"
      values   = ["true"]
    }
  }

  # User access with IP restrictions
  statement {
    sid    = "AllowUserAccess"
    effect = "Allow"
    principals {
      type        = "AWS"
      identifiers = length(var.user_arns) > 0 ? var.user_arns : ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"]
    }
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey"
    ]
    resources = ["*"]
    condition {
      test     = "Bool"
      variable = "aws:SecureTransport"
      values   = ["true"]
    }
    condition {
      test     = "IpAddress"
      variable = "aws:SourceIp"
      values   = var.allowed_ip_ranges
    }
  }

  # Only include this statement if tenant_iam_role is defined and not empty
  dynamic "statement" {
    for_each = lookup(each.value, "tenant_iam_role", "") != "" ? [1] : []
    content {
      sid    = "AllowTenantRoleAccess"
      effect = "Allow"
      principals {
        type        = "AWS"
        identifiers = ["arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/${each.value.tenant_iam_role}"]
      }
      actions = [
        "kms:Encrypt",
        "kms:Decrypt",
        "kms:ReEncrypt*",
        "kms:GenerateDataKey*",
        "kms:DescribeKey"
      ]
      resources = ["*"]
    }
  }
}

# Primary KMS key for general encryption
# SECURITY: Re-enabled with proper lifecycle management
resource "aws_kms_key" "primary" {
  description             = "KMS key for ${var.environment} environment"
  deletion_window_in_days = 30 # Increased for safety
  enable_key_rotation     = true

  # Lifecycle management to prevent accidental deletion (REMOVED for destroy)
  # lifecycle {
  #   prevent_destroy = true
  # }

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "AllowCloudWatchLogs"
        Effect = "Allow"
        Principal = {
          Service = "logs.${data.aws_region.current.name}.amazonaws.com"
        }
        Action = [
          "kms:Encrypt*",
          "kms:Decrypt*",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:Describe*"
        ]
        Resource = "*"
      },
      {
        Sid    = "AllowRDSService"
        Effect = "Allow"
        Principal = {
          Service = "rds.amazonaws.com"
        }
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = "*"
      },
      {
        Sid    = "AllowS3Service"
        Effect = "Allow"
        Principal = {
          Service = "s3.amazonaws.com"
        }
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(var.tags, {
    Name        = "${var.environment}-primary-kms-key"
    Purpose     = "Primary encryption key"
    Environment = var.environment
  })
}

# Tenant-specific KMS keys
# KMS keys have been disabled to avoid deletion protection
# resource "aws_kms_key" "tenant_keys" {
#   for_each                = var.tenants
#   description             = "KMS key for tenant ${each.value.id} in ${var.environment} environment"
#   deletion_window_in_days = 7 # Minimum required by AWS
#   enable_key_rotation     = true
#   policy                  = data.aws_iam_policy_document.tenant_key_policy[each.key].json
#   tags = merge(var.tags, {
#     Name        = "${var.environment}-tenant-${each.value.id}-key",
#     Tenant      = each.value.id,
#     Environment = var.environment
#   })
# }

# KMS key aliases for easier identification
# SECURITY: Re-enabled with proper configuration
resource "aws_kms_alias" "primary_alias" {
  count         = var.import_existing_resources ? 0 : 1
  name          = "alias/${var.environment}-primary"
  target_key_id = aws_kms_key.primary.key_id
}

# KMS aliases have been disabled to avoid deletion protection
# resource "aws_kms_alias" "tenant_aliases" {
#   for_each      = var.tenants
#   name          = "alias/${var.environment}-tenant-${each.value.id}"
#   target_key_id = aws_kms_key.tenant_keys[each.key].key_id
# }
