# Create S3 access logs bucket
resource "aws_s3_bucket" "s3_access_logs" {
  bucket        = "${var.environment}-${var.bucket_name}-access-logs"
  force_destroy = true

  tags = merge(var.tags, {
    Name        = "${var.environment}-${var.bucket_name}-access-logs",
    Environment = var.environment,
    ManagedBy   = "terraform"
  })
}

# Configure access logs bucket encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "logs_encryption" {
  bucket = aws_s3_bucket.s3_access_logs.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = var.kms_key_arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

# Block public access for access logs bucket
resource "aws_s3_bucket_public_access_block" "logs_public_access_block" {
  bucket = aws_s3_bucket.s3_access_logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Enable versioning for access logs bucket
resource "aws_s3_bucket_versioning" "logs_versioning" {
  bucket = aws_s3_bucket.s3_access_logs.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Configure lifecycle policy for access logs bucket
resource "aws_s3_bucket_lifecycle_configuration" "logs_lifecycle" {
  bucket = aws_s3_bucket.s3_access_logs.id

  rule {
    id     = "log-expiration"
    status = "Enabled"

    expiration {
      days = 30 # Reduced for easier cleanup during development
    }

    filter {
      prefix = ""
    }
  }
}

# Main S3 bucket
resource "aws_s3_bucket" "s3_bucket" {
  bucket        = "${var.environment}-${var.bucket_name}"
  force_destroy = true

  tags = merge(var.tags, {
    Name        = "${var.environment}-${var.bucket_name}",
    Environment = var.environment,
    ManagedBy   = "terraform"
  })
}

# Configure server-side encryption for main bucket
resource "aws_s3_bucket_server_side_encryption_configuration" "s3_encryption" {
  bucket = aws_s3_bucket.s3_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = var.kms_key_arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "s3_public_access_block" {
  bucket = aws_s3_bucket.s3_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_versioning" "main_versioning" {
  bucket = aws_s3_bucket.s3_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Configure lifecycle policy for main bucket
resource "aws_s3_bucket_lifecycle_configuration" "main_lifecycle" {
  bucket = aws_s3_bucket.s3_bucket.id

  rule {
    id     = "log-retention"
    status = "Enabled"

    transition {
      days          = 30
      storage_class = "STANDARD_IA"
    }

    transition {
      days          = 60
      storage_class = "GLACIER"
    }

    expiration {
      days = 90
    }

    filter {
      prefix = "logs/"
    }
  }

  # Add rule for general objects
  rule {
    id     = "general-objects-lifecycle"
    status = "Enabled"

    transition {
      days          = 60
      storage_class = "STANDARD_IA"
    }

    transition {
      days          = 180
      storage_class = "GLACIER"
    }

    noncurrent_version_transition {
      noncurrent_days = 30
      storage_class   = "STANDARD_IA"
    }

    noncurrent_version_transition {
      noncurrent_days = 60
      storage_class   = "GLACIER"
    }

    noncurrent_version_expiration {
      noncurrent_days = 90
    }

    filter {
      prefix = ""
    }
  }

  # Add rule for incomplete multipart uploads
  rule {
    id     = "abort-incomplete-multipart-upload"
    status = "Enabled"

    abort_incomplete_multipart_upload {
      days_after_initiation = var.incomplete_mpu_days
    }

    filter {
      prefix = ""
    }
  }
}

# Configure logging for main bucket
resource "aws_s3_bucket_logging" "s3_bucket_logging" {
  bucket = aws_s3_bucket.s3_bucket.id

  target_bucket = aws_s3_bucket.s3_access_logs.id
  target_prefix = "${var.environment}-${var.bucket_name}/"
}

# Configure secure transport policy for main bucket
resource "aws_s3_bucket_policy" "require_ssl" {
  bucket = aws_s3_bucket.s3_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "RequireSSLOnly"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.s3_bucket.arn,
          "${aws_s3_bucket.s3_bucket.arn}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      }
    ]
  })
}

# Tenant assets buckets
resource "aws_s3_bucket" "tenant_assets" {
  for_each = { for tenant_id, tenant in var.tenants : tenant_id => tenant }

  bucket        = "${var.environment}-${each.key}-assets"
  force_destroy = true # Enable force destroy for easier cleanup during development

  tags = merge(var.tags, {
    Tenant = each.key
  })
}

resource "aws_s3_bucket_server_side_encryption_configuration" "tenant_assets_encryption" {
  for_each = { for tenant_id, tenant in var.tenants : tenant_id => tenant }

  bucket = aws_s3_bucket.tenant_assets[each.key].id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = lookup(var.tenant_kms_key_arns, each.key, var.kms_key_arn)
      sse_algorithm     = "aws:kms" # Using KMS for encryption
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "block_public" {
  for_each                = var.tenants
  bucket                  = aws_s3_bucket.tenant_assets[each.key].id
  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_lifecycle_configuration" "tenant_assets" {
  for_each = { for tenant_id, tenant in var.tenants : tenant_id => tenant }

  bucket = aws_s3_bucket.tenant_assets[each.key].id

  rule {
    id     = "archive-old-objects"
    status = "Enabled"

    transition {
      days          = 30 # Reduced for easier cleanup during development
      storage_class = "STANDARD_IA"
    }

    transition {
      days          = 60 # Reduced for easier cleanup during development
      storage_class = "GLACIER"
    }

    expiration {
      days = 90 # Reduced for easier cleanup during development
    }

    filter {
      prefix = ""
    }
  }

  # Add rule for incomplete multipart uploads
  rule {
    id     = "abort-incomplete-multipart-upload"
    status = "Enabled"

    abort_incomplete_multipart_upload {
      days_after_initiation = var.incomplete_mpu_days
    }

    filter {
      prefix = ""
    }
  }
}

resource "aws_s3_bucket_versioning" "tenant_assets" {
  for_each = { for tenant_id, tenant in var.tenants : tenant_id => tenant }

  bucket = aws_s3_bucket.tenant_assets[each.key].id

  versioning_configuration {
    status = "Enabled"
  }
}

# Configure logging for tenant buckets
resource "aws_s3_bucket_logging" "tenant_assets_logging" {
  for_each = { for tenant_id, tenant in var.tenants : tenant_id => tenant }

  bucket = aws_s3_bucket.tenant_assets[each.key].id

  target_bucket = aws_s3_bucket.s3_access_logs.id
  target_prefix = "${var.environment}-${each.key}-assets/"
}

# Configure secure transport policy for tenant buckets
resource "aws_s3_bucket_policy" "tenant_require_ssl" {
  for_each = { for tenant_id, tenant in var.tenants : tenant_id => tenant }

  bucket = aws_s3_bucket.tenant_assets[each.key].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "RequireSSLOnly"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.tenant_assets[each.key].arn,
          "${aws_s3_bucket.tenant_assets[each.key].arn}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      }
    ]
  })
}

# Create archival bucket for tenant data
resource "aws_s3_bucket" "tenant_archival_bucket" {
  bucket        = "${var.environment}-tenant-archival"
  force_destroy = true

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-archival",
    Environment = var.environment,
    ManagedBy   = "terraform"
  })
}

# Configure archival bucket encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "archival_encryption" {
  bucket = aws_s3_bucket.tenant_archival_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = var.kms_key_arn
      sse_algorithm     = "aws:kms" # Using KMS for encryption
    }
    bucket_key_enabled = true
  }
}

# Block public access for archival bucket
resource "aws_s3_bucket_public_access_block" "archival_public_access_block" {
  bucket = aws_s3_bucket.tenant_archival_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Enable versioning for archival bucket
resource "aws_s3_bucket_versioning" "archival_versioning" {
  bucket = aws_s3_bucket.tenant_archival_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Configure lifecycle policy for archival bucket
resource "aws_s3_bucket_lifecycle_configuration" "archival_lifecycle" {
  bucket = aws_s3_bucket.tenant_archival_bucket.id

  rule {
    id     = "archive-retention"
    status = "Enabled"

    transition {
      days          = 365
      storage_class = "GLACIER"
    }

    expiration {
      days = 1095 # 3 years
    }

    filter {
      prefix = ""
    }
  }
}

resource "aws_s3_bucket" "log_bucket" {
  bucket        = "${var.bucket_name}-logs"
  force_destroy = true # Enable force destroy for easier cleanup during development
}

# Set ACL for log delivery (replacing deprecated inline acl)
resource "aws_s3_bucket_acl" "log_bucket_acl" {
  bucket = aws_s3_bucket.log_bucket.id
  acl    = "log-delivery-write"
}

# Block public access for log bucket
resource "aws_s3_bucket_public_access_block" "log_bucket_public_access_block" {
  bucket = aws_s3_bucket.log_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Set encryption configuration (replacing deprecated inline server_side_encryption_configuration)
resource "aws_s3_bucket_server_side_encryption_configuration" "log_bucket_encryption" {
  bucket = aws_s3_bucket.log_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = var.kms_key_arn
      sse_algorithm     = "aws:kms" # Using KMS for encryption
    }
    bucket_key_enabled = true
  }
}

# Add versioning to log bucket
resource "aws_s3_bucket_versioning" "log_bucket_versioning" {
  bucket = aws_s3_bucket.log_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Set lifecycle rules (replacing deprecated inline lifecycle_rule)
resource "aws_s3_bucket_lifecycle_configuration" "log_bucket_lifecycle" {
  bucket = aws_s3_bucket.log_bucket.id

  rule {
    id     = "log-expiration"
    status = "Enabled"

    expiration {
      days = 90
    }

    filter {
      prefix = ""
    }
  }
}

# Configure secure transport policy for log bucket
resource "aws_s3_bucket_policy" "log_bucket_require_ssl" {
  bucket = aws_s3_bucket.log_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "RequireSSLOnly"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.log_bucket.arn,
          "${aws_s3_bucket.log_bucket.arn}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      }
    ]
  })
}
