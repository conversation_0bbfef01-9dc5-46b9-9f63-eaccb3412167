variable "domain_name" {
  description = "The main domain name"
  type        = string
}

variable "load_balancer_ip" {
  description = "The IP address of the load balancer"
  type        = string
}

variable "acm_validation_records" {
  description = "ACM certificate validation records"
  type = list(object({
    domain_name           = string
    resource_record_name  = string
    resource_record_value = string
    resource_record_type  = string
  }))
  default = []
}

variable "tenant_records" {
  description = "Tenant-specific DNS records"
  type = map(object({
    subdomain        = string
    load_balancer_ip = string
  }))
  default = {}
} 