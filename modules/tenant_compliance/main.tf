# AWS Config rule for each tenant
resource "aws_config_config_rule" "tenant" {
  for_each = var.tenants

  name        = "${var.environment}-tenant-${each.key}-compliance-rule-v2"
  description = "Compliance rules for tenant ${each.key}"

  source {
    owner             = "AWS"
    source_identifier = "S3_BUCKET_VERSIONING_ENABLED" # Example valid managed rule
  }

  scope {
    compliance_resource_types = [
      "AWS::RDS::DBInstance",
      "AWS::EFS::FileSystem",
      "AWS::WAFv2::WebACL"
    ]
  }
}

# CloudWatch dashboard for compliance metrics
resource "aws_cloudwatch_dashboard" "compliance" {
  for_each = var.tenants

  dashboard_name = "${var.environment}-tenant-${each.key}-compliance-dashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/Config", "CompliantResourceCount", "ConfigRuleName", aws_config_config_rule.tenant[each.key].name],
            ["AWS/Config", "NonCompliantResourceCount", "ConfigRuleName", aws_config_config_rule.tenant[each.key].name]
          ]
          period = 300
          stat   = "Sum"
          region = data.aws_region.current.name
          title  = "Compliance Status"
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/Config", "ConfigurationRecorderStatus", "ConfigRuleName", aws_config_config_rule.tenant[each.key].name]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "Config Recorder Status"
        }
      }
    ]
  })
}

# SNS topic for compliance alerts
resource "aws_sns_topic" "compliance_alerts" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-compliance-alerts"

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-compliance-alerts"
    Tenant      = each.key
    Environment = var.environment
  })
}

# CloudWatch alarm for compliance violations
resource "aws_cloudwatch_metric_alarm" "compliance_violation" {
  for_each = var.tenants

  alarm_name          = "${var.environment}-tenant-${each.key}-compliance-violation"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "NonCompliantResourceCount"
  namespace           = "AWS/Config"
  period              = "300"
  statistic           = "Sum"
  threshold           = "0"
  alarm_description   = "This metric monitors compliance violations for tenant ${each.key}"
  alarm_actions       = [aws_sns_topic.compliance_alerts[each.key].arn]

  dimensions = {
    ConfigRuleName = aws_config_config_rule.tenant[each.key].name
  }
}

# Data source for current region
data "aws_region" "current" {} 