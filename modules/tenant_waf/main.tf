resource "aws_wafv2_web_acl" "tenant" {
  for_each = var.tenants

  name        = "${var.environment}-tenant-${each.key}-waf-v2"
  description = "WAF rules for tenant ${each.key}"
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  # Rate limiting rule
  rule {
    name     = "RateLimit"
    priority = 1

    action {
      block {}
    }

    statement {
      rate_based_statement {
        limit              = each.value.rate_limit
        aggregate_key_type = "IP"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimit"
      sampled_requests_enabled   = true
    }
  }

  # OWASP Top 10 rules
  rule {
    name     = "OWASP"
    priority = 2

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesOWASPTop10"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "OWASP"
      sampled_requests_enabled   = true
    }
  }

  # Custom rules for tenant
  dynamic "rule" {
    for_each = each.value.custom_rules
    content {
      name     = rule.value.name
      priority = rule.value.priority

      action {
        block {}
      }

      statement {
        byte_match_statement {
          search_string = rule.value.pattern
          field_to_match {
            body {}
          }
          text_transformation {
            priority = 1
            type     = "NONE"
          }
          positional_constraint = "CONTAINS"
        }
      }

      visibility_config {
        cloudwatch_metrics_enabled = true
        metric_name                = rule.value.name
        sampled_requests_enabled   = true
      }
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "TenantWAF"
    sampled_requests_enabled   = true
  }

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-waf-v2"
    Tenant      = each.key
    Environment = var.environment
  })
  depends_on = [aws_cloudwatch_log_group.waf_logs]
}

# CloudWatch Log Group for WAF logs
resource "aws_cloudwatch_log_group" "waf_logs" {
  for_each = var.tenants

  name              = "/aws/waf/${var.environment}/tenant-${each.key}-v2"
  retention_in_days = var.log_retention_days

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-waf-logs-v2"
    Tenant      = each.key
    Environment = var.environment
  })
}

# WAF logging configuration
resource "aws_wafv2_web_acl_logging_configuration" "tenant" {
  for_each = var.tenants

  log_destination_configs = [aws_cloudwatch_log_group.waf_logs[each.key].arn]
  resource_arn            = aws_wafv2_web_acl.tenant[each.key].arn

  logging_filter {
    default_behavior = "KEEP"

    filter {
      behavior = "KEEP"
      condition {
        action_condition {
          action = "BLOCK"
        }
      }
      requirement = "MEETS_ANY"
    }
  }
} 