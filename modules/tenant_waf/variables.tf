variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "tenants" {
  description = "Map of tenant configurations"
  type = map(object({
    rate_limit = number
    custom_rules = list(object({
      name     = string
      priority = number
      pattern  = string
    }))
  }))
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "log_retention_days" {
  description = "Number of days to retain WAF logs"
  type        = number
  default     = 90
} 