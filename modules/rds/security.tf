resource "aws_db_parameter_group" "enhanced_security" {
  family = var.engine_family
  name   = "${var.identifier}-enhanced-security"

  parameter {
    name  = "audit_logging"
    value = "1"
  }

  parameter {
    name  = "audit_log_events"
    value = "CONNECT,QUERY,QUERY_DCL,QUERY_DDL,QUERY_DML,TABLE"
  }

  parameter {
    name  = "audit_log_file_rotations"
    value = "10"
  }

  parameter {
    name  = "audit_log_file_size"
    value = "100000000"
  }

  parameter {
    name  = "max_connections"
    value = "1000"
  }

  parameter {
    name  = "max_allowed_packet"
    value = "67108864"
  }

  parameter {
    name  = "wait_timeout"
    value = "300"
  }

  parameter {
    name  = "interactive_timeout"
    value = "300"
  }

  parameter {
    name  = "innodb_buffer_pool_size"
    value = "{DBInstanceClassMemory*3/4}"
  }

  parameter {
    name  = "innodb_log_file_size"
    value = "1073741824"
  }

  parameter {
    name  = "innodb_flush_log_at_trx_commit"
    value = "1"
  }

  parameter {
    name  = "innodb_flush_method"
    value = "O_DIRECT"
  }

  parameter {
    name  = "innodb_file_per_table"
    value = "1"
  }

  parameter {
    name  = "innodb_stats_on_metadata"
    value = "0"
  }

  parameter {
    name  = "innodb_buffer_pool_instances"
    value = "4"
  }

  parameter {
    name  = "innodb_io_capacity"
    value = "2000"
  }

  parameter {
    name  = "innodb_read_io_threads"
    value = "4"
  }

  parameter {
    name  = "innodb_write_io_threads"
    value = "4"
  }

  tags = var.tags
}

resource "aws_db_instance" "enhanced_security" {
  identifier                            = var.identifier
  engine                                = "mysql"
  engine_version                        = "8.0"
  instance_class                        = "db.t3.medium"
  allocated_storage                     = 20
  storage_type                          = "gp2"
  db_name                               = var.db_name
  username                              = var.db_username
  password                              = var.db_password
  parameter_group_name                  = aws_db_parameter_group.enhanced_security.name
  skip_final_snapshot                   = false
  final_snapshot_identifier             = "${var.identifier}-final-snapshot"
  backup_retention_period               = 7
  backup_window                         = "03:00-04:00"
  maintenance_window                    = "mon:04:00-mon:05:00"
  multi_az                              = true
  publicly_accessible                   = false
  vpc_security_group_ids                = [var.security_group_id]
  db_subnet_group_name                  = aws_db_subnet_group.main.name
  monitoring_interval                   = 60
  monitoring_role_arn                   = aws_iam_role.rds_monitoring_role[0].arn
  enabled_cloudwatch_logs_exports       = ["audit", "error", "general", "slowquery"]
  performance_insights_enabled          = true
  performance_insights_retention_period = 7
  copy_tags_to_snapshot                 = true
  auto_minor_version_upgrade            = true

  tags = {
    ManagedBy = "Terraform"
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_cpu_utilization_enhanced" {
  alarm_name          = "${var.identifier}-cpu-utilization-enhanced"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors RDS CPU utilization"
  alarm_actions       = [aws_sns_topic.rds_alerts.arn]

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.enhanced_security.id
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_free_memory_enhanced" {
  alarm_name          = "${var.identifier}-free-memory-enhanced"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "FreeableMemory"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = "1000000000"
  alarm_description   = "This metric monitors RDS free memory"
  alarm_actions       = [aws_sns_topic.rds_alerts.arn]

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.enhanced_security.id
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_free_storage_enhanced" {
  alarm_name          = "${var.identifier}-free-storage-enhanced"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "FreeStorageSpace"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = "10000000000"
  alarm_description   = "This metric monitors RDS free storage space"
  alarm_actions       = [aws_sns_topic.rds_alerts.arn]

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.enhanced_security.id
  }
}

resource "aws_sns_topic" "rds_alerts" {
  name = "rds-alerts"
  # Add KMS encryption for SNS topic
  kms_master_key_id = var.sns_kms_key_arn # <-- Set this variable to your KMS key ARN
}

resource "aws_sns_topic_policy" "rds_alerts" {
  arn = aws_sns_topic.rds_alerts.arn

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "cloudwatch.amazonaws.com"
        }
        Action   = "SNS:Publish"
        Resource = aws_sns_topic.rds_alerts.arn
      }
    ]
  })
} 