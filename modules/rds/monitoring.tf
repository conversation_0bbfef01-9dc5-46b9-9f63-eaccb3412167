# Enhanced monitoring for RDS and Aurora Serverless
# This file implements comprehensive monitoring and alerting for multi-tenant database

# Create IAM role for RDS enhanced monitoring
resource "aws_iam_role" "rds_monitoring_role" {
  count = !var.import_existing_resources ? 1 : 0

  name = "${var.environment}-rds-monitoring-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "monitoring.rds.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Attach the AmazonRDSEnhancedMonitoringRole policy to the role
resource "aws_iam_role_policy_attachment" "rds_monitoring_role_attachment" {
  count = !var.import_existing_resources ? 1 : 0

  role       = try(aws_iam_role.rds_monitoring_role[0].name, "dummy-role")
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole"
}

# Create CloudWatch dashboard for RDS monitoring
resource "aws_cloudwatch_dashboard" "rds_dashboard" {
  count = !var.import_existing_resources ? 1 : 0

  dashboard_name = "${var.environment}-rds-dashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = var.use_aurora_serverless ? [
            ["AWS/RDS", "ServerlessDatabaseCapacity", "DBClusterIdentifier", "${var.environment}-aurora-serverless"]
            ] : [
            ["AWS/RDS", "CPUUtilization", "DBInstanceIdentifier", "${var.environment}-architrave-db"]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = var.use_aurora_serverless ? "Aurora Serverless Capacity" : "RDS CPU Utilization"
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/RDS", "DatabaseConnections", var.use_aurora_serverless ? "DBClusterIdentifier" : "DBInstanceIdentifier", var.use_aurora_serverless ? "${var.environment}-aurora-serverless" : "${var.environment}-architrave-db"]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "Database Connections"
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/RDS", "FreeableMemory", var.use_aurora_serverless ? "DBClusterIdentifier" : "DBInstanceIdentifier", var.use_aurora_serverless ? "${var.environment}-aurora-serverless" : "${var.environment}-architrave-db"]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "Freeable Memory"
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 6
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/RDS", "ReadIOPS", var.use_aurora_serverless ? "DBClusterIdentifier" : "DBInstanceIdentifier", var.use_aurora_serverless ? "${var.environment}-aurora-serverless" : "${var.environment}-architrave-db"],
            ["AWS/RDS", "WriteIOPS", var.use_aurora_serverless ? "DBClusterIdentifier" : "DBInstanceIdentifier", var.use_aurora_serverless ? "${var.environment}-aurora-serverless" : "${var.environment}-architrave-db"]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "Read/Write IOPS"
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 12
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/RDS", "ReadLatency", var.use_aurora_serverless ? "DBClusterIdentifier" : "DBInstanceIdentifier", var.use_aurora_serverless ? "${var.environment}-aurora-serverless" : "${var.environment}-architrave-db"],
            ["AWS/RDS", "WriteLatency", var.use_aurora_serverless ? "DBClusterIdentifier" : "DBInstanceIdentifier", var.use_aurora_serverless ? "${var.environment}-aurora-serverless" : "${var.environment}-architrave-db"]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "Read/Write Latency"
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 12
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/RDS", "DiskQueueDepth", var.use_aurora_serverless ? "DBClusterIdentifier" : "DBInstanceIdentifier", var.use_aurora_serverless ? "${var.environment}-aurora-serverless" : "${var.environment}-architrave-db"]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "Disk Queue Depth"
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 18
        width  = 24
        height = 6
        properties = {
          metrics = var.enable_proxy ? [
            ["AWS/RDS", "ClientConnections", "ProxyName", "${var.environment}-db-proxy"],
            ["AWS/RDS", "DatabaseConnectionBorrowLatency", "ProxyName", "${var.environment}-db-proxy"]
          ] : []
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "RDS Proxy Metrics"
        }
      }
    ]
  })
}

# Create CloudWatch alarms for RDS monitoring
resource "aws_cloudwatch_metric_alarm" "rds_cpu_utilization" {
  count = !var.import_existing_resources && !var.use_aurora_serverless ? 1 : 0

  alarm_name          = "${var.environment}-rds-cpu-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = 60
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "This metric monitors RDS CPU utilization"
  alarm_actions       = []
  ok_actions          = []

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.main[0].identifier
  }

  tags = var.tags
}

resource "aws_cloudwatch_metric_alarm" "rds_freeable_memory" {
  count = !var.import_existing_resources && !var.use_aurora_serverless ? 1 : 0

  alarm_name          = "${var.environment}-rds-freeable-memory"
  comparison_operator = "LessThanThreshold"
  evaluation_periods  = 2
  metric_name         = "FreeableMemory"
  namespace           = "AWS/RDS"
  period              = 60
  statistic           = "Average"
  threshold           = 1073741824 # 1 GB
  alarm_description   = "This metric monitors RDS freeable memory"
  alarm_actions       = []
  ok_actions          = []

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.main[0].identifier
  }

  tags = var.tags
}

resource "aws_cloudwatch_metric_alarm" "rds_connections" {
  count = !var.import_existing_resources && !var.use_aurora_serverless ? 1 : 0

  alarm_name          = "${var.environment}-rds-connections"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "DatabaseConnections"
  namespace           = "AWS/RDS"
  period              = 60
  statistic           = "Average"
  threshold           = 800 # 80% of max_connections (1000)
  alarm_description   = "This metric monitors RDS database connections"
  alarm_actions       = []
  ok_actions          = []

  dimensions = {
    DBInstanceIdentifier = aws_db_instance.main[0].identifier
  }

  tags = var.tags
}

# Create a Lambda function to optimize database performance
resource "aws_lambda_function" "db_optimizer" {
  count = !var.import_existing_resources ? 1 : 0

  function_name    = "${var.environment}-db-optimizer"
  role             = aws_iam_role.db_optimizer_role[0].arn
  handler          = "index.handler"
  runtime          = "nodejs16.x"
  timeout          = 60
  memory_size      = 256
  source_code_hash = data.archive_file.db_optimizer_code[0].output_base64sha256
  filename         = data.archive_file.db_optimizer_code[0].output_path

  environment {
    variables = {
      RDS_SECRET_ARN     = aws_secretsmanager_secret.rds_master.arn
      RDS_ENDPOINT       = var.use_aurora_serverless ? aws_rds_cluster.aurora_serverless[0].endpoint : aws_db_instance.main[0].endpoint
      RDS_PROXY_ENDPOINT = var.enable_proxy ? aws_db_proxy.db_proxy[0].endpoint : ""
      DEFAULT_DB_NAME    = var.db_name
      ENVIRONMENT        = var.environment
    }
  }

  vpc_config {
    subnet_ids         = var.subnet_ids
    security_group_ids = [var.security_group_id]
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-db-optimizer"
  })
}

# Create IAM role for the Lambda function
resource "aws_iam_role" "db_optimizer_role" {
  count = !var.import_existing_resources ? 1 : 0

  name = "${var.environment}-db-optimizer-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Create IAM policy for the Lambda function
resource "aws_iam_role_policy" "db_optimizer_policy" {
  count = !var.import_existing_resources ? 1 : 0

  name = "${var.environment}-db-optimizer-policy"
  role = aws_iam_role.db_optimizer_role[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue"
        ]
        Effect   = "Allow"
        Resource = aws_secretsmanager_secret.rds_master.arn
      },
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.environment}-db-optimizer:*"
      },
      {
        Action = [
          "ec2:CreateNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DeleteNetworkInterface"
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Action = [
          "cloudwatch:GetMetricData",
          "cloudwatch:GetMetricStatistics"
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Action = [
          "rds:DescribeDBInstances",
          "rds:DescribeDBClusters"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

# Create Lambda function code
data "archive_file" "db_optimizer_code" {
  count = !var.import_existing_resources ? 1 : 0

  type        = "zip"
  output_path = "${path.module}/db_optimizer.zip"

  source {
    content  = <<EOF
const AWS = require('aws-sdk');
const mysql = require('mysql2/promise');
const secretsManager = new AWS.SecretsManager();
const cloudwatch = new AWS.CloudWatch();
const rds = new AWS.RDS();

exports.handler = async (event) => {
    console.log('Received event:', JSON.stringify(event, null, 2));

    // Get RDS credentials from Secrets Manager
    const masterSecretData = await secretsManager.getSecretValue({
        SecretId: process.env.RDS_SECRET_ARN
    }).promise();

    const masterSecret = JSON.parse(masterSecretData.SecretString);

    // Connect to the database
    const endpoint = process.env.RDS_PROXY_ENDPOINT || process.env.RDS_ENDPOINT;
    const connection = await mysql.createConnection({
        host: endpoint,
        user: masterSecret.username,
        password: masterSecret.password,
        database: process.env.DEFAULT_DB_NAME,
        ssl: { rejectUnauthorized: false }
    });

    try {
        // Get database metrics from CloudWatch
        const metrics = await getDBMetrics();

        // Identify slow queries
        const slowQueries = await identifySlowQueries(connection);

        // Identify missing indexes
        const missingIndexes = await identifyMissingIndexes(connection);

        // Identify tenant-specific issues
        const tenantIssues = await identifyTenantIssues(connection);

        // Generate optimization recommendations
        const recommendations = generateRecommendations(metrics, slowQueries, missingIndexes, tenantIssues);

        // Apply automatic optimizations if enabled
        if (event.applyOptimizations) {
            await applyOptimizations(connection, recommendations);
        }

        return {
            statusCode: 200,
            body: {
                metrics: metrics,
                slowQueries: slowQueries,
                missingIndexes: missingIndexes,
                tenantIssues: tenantIssues,
                recommendations: recommendations
            }
        };
    } finally {
        await connection.end();
    }
};

async function getDBMetrics() {
    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    const params = {
        MetricDataQueries: [
            {
                Id: 'cpu',
                MetricStat: {
                    Metric: {
                        Namespace: 'AWS/RDS',
                        MetricName: 'CPUUtilization',
                        Dimensions: [
                            {
                                Name: process.env.RDS_ENDPOINT.includes('cluster') ? 'DBClusterIdentifier' : 'DBInstanceIdentifier',
                                Value: process.env.RDS_ENDPOINT.includes('cluster') ?
                                    process.env.RDS_ENDPOINT.split('.')[0] :
                                    process.env.RDS_ENDPOINT.split('.')[0]
                            }
                        ]
                    },
                    Period: 3600,
                    Stat: 'Average'
                }
            },
            {
                Id: 'connections',
                MetricStat: {
                    Metric: {
                        Namespace: 'AWS/RDS',
                        MetricName: 'DatabaseConnections',
                        Dimensions: [
                            {
                                Name: process.env.RDS_ENDPOINT.includes('cluster') ? 'DBClusterIdentifier' : 'DBInstanceIdentifier',
                                Value: process.env.RDS_ENDPOINT.includes('cluster') ?
                                    process.env.RDS_ENDPOINT.split('.')[0] :
                                    process.env.RDS_ENDPOINT.split('.')[0]
                            }
                        ]
                    },
                    Period: 3600,
                    Stat: 'Average'
                }
            },
            {
                Id: 'memory',
                MetricStat: {
                    Metric: {
                        Namespace: 'AWS/RDS',
                        MetricName: 'FreeableMemory',
                        Dimensions: [
                            {
                                Name: process.env.RDS_ENDPOINT.includes('cluster') ? 'DBClusterIdentifier' : 'DBInstanceIdentifier',
                                Value: process.env.RDS_ENDPOINT.includes('cluster') ?
                                    process.env.RDS_ENDPOINT.split('.')[0] :
                                    process.env.RDS_ENDPOINT.split('.')[0]
                            }
                        ]
                    },
                    Period: 3600,
                    Stat: 'Average'
                }
            }
        ],
        StartTime: oneDayAgo,
        EndTime: now
    };

    const result = await cloudwatch.getMetricData(params).promise();

    return {
        cpu: result.MetricDataResults.find(r => r.Id === 'cpu').Values,
        connections: result.MetricDataResults.find(r => r.Id === 'connections').Values,
        memory: result.MetricDataResults.find(r => r.Id === 'memory').Values
    };
}

async function identifySlowQueries(connection) {
    const [rows] = await connection.query(`
        SELECT
            query,
            count_star AS count,
            avg_timer_wait/1000000000 AS avg_latency_ms
        FROM
            performance_schema.events_statements_summary_by_digest
        ORDER BY
            avg_latency_ms DESC
        LIMIT 10
    `);

    return rows;
}

async function identifyMissingIndexes(connection) {
    const [rows] = await connection.query(`
        SELECT
            t.TABLE_SCHEMA,
            t.TABLE_NAME,
            s.COLUMN_NAME,
            s.SEQ_IN_INDEX,
            s.CARDINALITY,
            s.INDEX_TYPE
        FROM
            information_schema.TABLES t
        JOIN
            information_schema.STATISTICS s
        ON
            t.TABLE_SCHEMA = s.TABLE_SCHEMA
            AND t.TABLE_NAME = s.TABLE_NAME
        WHERE
            t.TABLE_SCHEMA LIKE 'tenant_%'
        ORDER BY
            t.TABLE_SCHEMA,
            t.TABLE_NAME,
            s.INDEX_NAME,
            s.SEQ_IN_INDEX
    `);

    // Analyze tables without indexes
    const [tablesWithoutIndexes] = await connection.query(`
        SELECT
            t.TABLE_SCHEMA,
            t.TABLE_NAME
        FROM
            information_schema.TABLES t
        LEFT JOIN
            information_schema.STATISTICS s
        ON
            t.TABLE_SCHEMA = s.TABLE_SCHEMA
            AND t.TABLE_NAME = s.TABLE_NAME
        WHERE
            t.TABLE_SCHEMA LIKE 'tenant_%'
            AND s.INDEX_NAME IS NULL
        GROUP BY
            t.TABLE_SCHEMA,
            t.TABLE_NAME
    `);

    return {
        existingIndexes: rows,
        tablesWithoutIndexes: tablesWithoutIndexes
    };
}

async function identifyTenantIssues(connection) {
    // Get list of tenant schemas
    const [tenantSchemas] = await connection.query(`
        SELECT SCHEMA_NAME
        FROM information_schema.SCHEMATA
        WHERE SCHEMA_NAME LIKE 'tenant_%_db'
    `);

    const tenantIssues = [];

    for (const schema of tenantSchemas) {
        const schemaName = schema.SCHEMA_NAME;

        // Check table sizes
        const [tableSizes] = await connection.query(`
            SELECT
                TABLE_NAME,
                DATA_LENGTH + INDEX_LENGTH AS total_size
            FROM
                information_schema.TABLES
            WHERE
                TABLE_SCHEMA = ?
            ORDER BY
                total_size DESC
            LIMIT 5
        `, [schemaName]);

        // Check for large tables
        const largeTablesExist = tableSizes.some(table => table.total_size > 100 * 1024 * 1024); // 100 MB

        if (largeTablesExist) {
            tenantIssues.push({
                schema: schemaName,
                issue: 'Large tables detected',
                details: tableSizes
            });
        }
    }

    return tenantIssues;
}

function generateRecommendations(metrics, slowQueries, missingIndexes, tenantIssues) {
    const recommendations = [];

    // CPU recommendations
    const avgCpu = metrics.cpu.reduce((a, b) => a + b, 0) / metrics.cpu.length;
    if (avgCpu > 70) {
        recommendations.push({
            type: 'CPU',
            severity: 'HIGH',
            description: 'High CPU utilization detected. Consider scaling up the database instance or optimizing queries.',
            metrics: { avgCpu }
        });
    }

    // Connection recommendations
    const avgConnections = metrics.connections.reduce((a, b) => a + b, 0) / metrics.connections.length;
    if (avgConnections > 800) {
        recommendations.push({
            type: 'Connections',
            severity: 'HIGH',
            description: 'High number of connections detected. Consider implementing connection pooling or increasing max_connections parameter.',
            metrics: { avgConnections }
        });
    }

    // Memory recommendations
    const avgMemory = metrics.memory.reduce((a, b) => a + b, 0) / metrics.memory.length;
    if (avgMemory < 1073741824) { // 1 GB
        recommendations.push({
            type: 'Memory',
            severity: 'MEDIUM',
            description: 'Low freeable memory detected. Consider scaling up the database instance or optimizing memory usage.',
            metrics: { avgMemory }
        });
    }

    // Slow query recommendations
    if (slowQueries.length > 0) {
        slowQueries.forEach(query => {
            if (query.avg_latency_ms > 1000) { // 1 second
                recommendations.push({
                    type: 'SlowQuery',
                    severity: 'HIGH',
                    description: 'Slow query detected. Consider optimizing the query or adding indexes.',
                    details: query
                });
            }
        });
    }

    // Missing index recommendations
    if (missingIndexes.tablesWithoutIndexes.length > 0) {
        missingIndexes.tablesWithoutIndexes.forEach(table => {
            recommendations.push({
                type: 'MissingIndex',
                severity: 'MEDIUM',
                description: "Table $${table.TABLE_NAME} in schema $${table.TABLE_SCHEMA} has no indexes. Consider adding appropriate indexes.",
                details: table
            });
        });
    }

    // Tenant-specific recommendations
    tenantIssues.forEach(issue => {
        recommendations.push({
            type: 'TenantIssue',
            severity: 'MEDIUM',
            description: "Issue detected in tenant schema $${issue.schema}: $${issue.issue}",
            details: issue
        });
    });

    return recommendations;
}

async function applyOptimizations(connection, recommendations) {
    // Apply safe optimizations automatically
    for (const recommendation of recommendations) {
        if (recommendation.type === 'MissingIndex' && recommendation.severity === 'MEDIUM') {
            const table = recommendation.details;

            // Add a simple index on the ID column if it exists
            try {
                await connection.query(`
                    USE \`$${table.TABLE_SCHEMA}\`;

                    -- Check if id column exists
                    SET @column_exists = (
                        SELECT COUNT(*)
                        FROM information_schema.COLUMNS
                        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = 'id'
                    );

                    -- Create index if id column exists
                    SET @sql = IF(@column_exists > 0,
                        CONCAT('CREATE INDEX idx_id ON \`', ?, '\`(id)'),
                        'SELECT 1'
                    );

                    PREPARE stmt FROM @sql;
                    EXECUTE stmt;
                    DEALLOCATE PREPARE stmt;
                `, [table.TABLE_SCHEMA, table.TABLE_NAME, table.TABLE_NAME]);

                console.log("Added index on id column for table $${table.TABLE_NAME} in schema $${table.TABLE_SCHEMA}");
            } catch (error) {
                console.error("Error adding index: $${error.message}");
            }
        }
    }
}
EOF
    filename = "index.js"
  }

  source {
    content  = <<EOF
{
  "name": "db-optimizer",
  "version": "1.0.0",
  "description": "Lambda function to optimize database performance",
  "main": "index.js",
  "dependencies": {
    "aws-sdk": "^2.1048.0",
    "mysql2": "^2.3.3"
  }
}
EOF
    filename = "package.json"
  }
}

# Create CloudWatch Event Rule to run the optimizer daily
resource "aws_cloudwatch_event_rule" "db_optimizer_rule" {
  count = !var.import_existing_resources ? 1 : 0

  name        = "${var.environment}-db-optimizer-rule"
  description = "Run database optimizer daily"

  schedule_expression = "rate(1 day)"

  tags = var.tags
}

# Create CloudWatch Event Target to invoke Lambda function
resource "aws_cloudwatch_event_target" "db_optimizer_target" {
  count = !var.import_existing_resources ? 1 : 0

  rule      = aws_cloudwatch_event_rule.db_optimizer_rule[0].name
  target_id = "db-optimizer"
  arn       = aws_lambda_function.db_optimizer[0].arn

  input = jsonencode({
    applyOptimizations = true
  })
}

# Grant permission for CloudWatch Events to invoke Lambda function
resource "aws_lambda_permission" "db_optimizer_permission" {
  count = !var.import_existing_resources ? 1 : 0

  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.db_optimizer[0].function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.db_optimizer_rule[0].arn
}
