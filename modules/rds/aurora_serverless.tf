resource "aws_rds_cluster" "aurora_serverless" {
  count = var.use_aurora_serverless && !var.import_existing_resources ? 1 : 0

  cluster_identifier     = "${var.environment}-aurora-serverless"
  engine                 = "aurora-mysql"
  engine_mode            = "provisioned"
  engine_version         = "8.0.mysql_aurora.3.04.0"
  database_name          = var.db_name
  master_username        = var.master_username
  master_password        = var.master_password != "" ? var.master_password : random_password.master_password[0].result
  skip_final_snapshot    = var.skip_final_snapshot
  db_subnet_group_name   = aws_db_subnet_group.main.name
  vpc_security_group_ids = [var.security_group_id]
  storage_encrypted      = true
  kms_key_id             = var.kms_key_arn

  # Enhanced security settings
  iam_database_authentication_enabled = true
  copy_tags_to_snapshot               = true
  enabled_cloudwatch_logs_exports     = ["audit", "error", "general", "slowquery"]

  # High availability settings
  availability_zones = var.availability_zones

  # Backup settings
  backup_retention_period = var.backup_retention_period
  preferred_backup_window = "03:00-04:00"

  # Maintenance settings
  preferred_maintenance_window = "sun:04:00-sun:05:00"
  apply_immediately            = true

  # Deletion protection - disabled for development
  deletion_protection = false

  # Serverless v2 scaling configuration
  serverlessv2_scaling_configuration {
    min_capacity = var.aurora_min_capacity
    max_capacity = var.aurora_max_capacity
  }

  # Parameter group
  db_cluster_parameter_group_name = aws_rds_cluster_parameter_group.aurora_mysql_cluster[0].name

  tags = merge(var.tags, {
    Name       = "${var.environment}-aurora-serverless"
    TenantType = "multi-tenant"
  })
}

resource "aws_rds_cluster_parameter_group" "aurora_mysql_cluster" {
  count = var.use_aurora_serverless && !var.import_existing_resources ? 1 : 0

  name        = "param-${var.environment}-aurora-mysql80-cluster-v1"
  family      = "aurora-mysql8.0"
  description = "Aurora MySQL 8.0 cluster parameter group for ${var.environment}"

  # Performance optimization parameters
  parameter {
    name         = "innodb_buffer_pool_size"
    value        = "{DBInstanceClassMemory*3/4}"
    apply_method = "pending-reboot" # Static parameter requires reboot
  }

  parameter {
    name         = "innodb_file_per_table"
    value        = "1"
    apply_method = "pending-reboot" # Static parameter requires reboot
  }

  parameter {
    name         = "innodb_stats_persistent"
    value        = "1"
    apply_method = "pending-reboot" # Static parameter requires reboot
  }

  # Security parameters
  parameter {
    name         = "require_secure_transport"
    value        = "ON"
    apply_method = "pending-reboot" # Static parameter requires reboot
  }

  # Logging parameters
  parameter {
    name         = "slow_query_log"
    value        = "1"
    apply_method = "immediate" # Dynamic parameter
  }

  parameter {
    name         = "long_query_time"
    value        = "2"
    apply_method = "immediate" # Dynamic parameter
  }

  parameter {
    name         = "log_output"
    value        = "FILE"
    apply_method = "immediate" # Dynamic parameter
  }

  parameter {
    name         = "general_log"
    value        = "1"
    apply_method = "immediate" # Dynamic parameter
  }

  # Multi-tenant optimization parameters
  parameter {
    name         = "max_connections"
    value        = "5000"           # Higher connection limit for multi-tenant
    apply_method = "pending-reboot" # Static parameter requires reboot
  }

  parameter {
    name         = "max_allowed_packet"
    value        = "67108864"  # 64MB
    apply_method = "immediate" # Dynamic parameter
  }

  parameter {
    name         = "tmp_table_size"
    value        = "134217728" # 128MB
    apply_method = "immediate" # Dynamic parameter
  }

  parameter {
    name         = "max_heap_table_size"
    value        = "134217728" # 128MB
    apply_method = "immediate" # Dynamic parameter
  }

  parameter {
    name         = "table_open_cache"
    value        = "4096"
    apply_method = "pending-reboot" # Static parameter requires reboot
  }

  parameter {
    name         = "thread_cache_size"
    value        = "32"
    apply_method = "immediate" # Dynamic parameter
  }

  tags = var.tags
}

resource "aws_rds_cluster_instance" "aurora_serverless_instances" {
  count = var.use_aurora_serverless && !var.import_existing_resources ? var.aurora_instance_count : 0

  identifier           = "${var.environment}-aurora-serverless-${count.index}"
  cluster_identifier   = aws_rds_cluster.aurora_serverless[0].id
  instance_class       = "db.serverless"
  engine               = aws_rds_cluster.aurora_serverless[0].engine
  engine_version       = aws_rds_cluster.aurora_serverless[0].engine_version
  db_subnet_group_name = aws_db_subnet_group.main.name

  # Performance Insights
  performance_insights_enabled          = true
  performance_insights_kms_key_id       = var.kms_key_arn
  performance_insights_retention_period = 7

  # Enhanced monitoring
  monitoring_interval = 60
  monitoring_role_arn = aws_iam_role.rds_monitoring_role[0].arn

  # Auto minor version upgrade
  auto_minor_version_upgrade = true

  # Parameter group
  db_parameter_group_name = aws_db_parameter_group.aurora_mysql_instance.name

  # Apply immediately
  apply_immediately = true

  tags = merge(var.tags, {
    Name = "${var.environment}-aurora-serverless-${count.index}"
    Role = count.index == 0 ? "writer" : "reader"
  })
}

# Create CloudWatch alarms for Aurora Serverless
resource "aws_cloudwatch_metric_alarm" "aurora_cpu_utilization" {
  count = var.use_aurora_serverless && !var.import_existing_resources ? 1 : 0

  alarm_name          = "${var.environment}-aurora-cpu-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = 60
  statistic           = "Average"
  threshold           = 80
  alarm_description   = "This metric monitors Aurora Serverless CPU utilization"
  alarm_actions       = []
  ok_actions          = []

  dimensions = {
    DBClusterIdentifier = aws_rds_cluster.aurora_serverless[0].cluster_identifier
  }

  tags = var.tags
}

resource "aws_cloudwatch_metric_alarm" "aurora_connections" {
  count = var.use_aurora_serverless && !var.import_existing_resources ? 1 : 0

  alarm_name          = "${var.environment}-aurora-connections"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "DatabaseConnections"
  namespace           = "AWS/RDS"
  period              = 60
  statistic           = "Average"
  threshold           = 4000 # 80% of max_connections (5000)
  alarm_description   = "This metric monitors Aurora Serverless database connections"
  alarm_actions       = []
  ok_actions          = []

  dimensions = {
    DBClusterIdentifier = aws_rds_cluster.aurora_serverless[0].cluster_identifier
  }

  tags = var.tags
}

resource "aws_cloudwatch_metric_alarm" "aurora_serverless_capacity" {
  count = var.use_aurora_serverless && !var.import_existing_resources ? 1 : 0

  alarm_name          = "${var.environment}-aurora-serverless-capacity"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "ServerlessDatabaseCapacity"
  namespace           = "AWS/RDS"
  period              = 60
  statistic           = "Average"
  threshold           = var.aurora_max_capacity * 0.8 # 80% of max capacity
  alarm_description   = "This metric monitors Aurora Serverless capacity utilization"
  alarm_actions       = []
  ok_actions          = []

  dimensions = {
    DBClusterIdentifier = aws_rds_cluster.aurora_serverless[0].cluster_identifier
  }

  tags = var.tags
}
