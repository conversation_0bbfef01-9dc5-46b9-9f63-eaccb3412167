
resource "aws_db_proxy" "db_proxy" {
  count = var.enable_proxy && !var.import_existing_resources ? 1 : 0

  name                   = "${var.environment}-db-proxy"
  debug_logging          = true
  engine_family          = "MYSQL"
  idle_client_timeout    = 1800
  require_tls            = true
  role_arn               = aws_iam_role.rds_proxy_role[0].arn
  vpc_security_group_ids = [var.security_group_id]
  vpc_subnet_ids         = var.subnet_ids

  auth {
    auth_scheme = "SECRETS"
    iam_auth    = "REQUIRED"
    secret_arn  = aws_secretsmanager_secret.rds_master.arn
    description = "RDS Proxy authentication using Secrets Manager with IAM auth"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-db-proxy"
    Type = "multi-tenant-proxy"
  })
}

resource "aws_iam_role" "rds_proxy_role" {
  count = var.import_existing_resources ? 0 : 1
  name  = "${var.environment}-rds-proxy-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "rds.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(var.tags, {
    Name = "${var.environment}-rds-proxy-role"
  })
}

resource "aws_iam_role_policy" "rds_proxy_secrets_policy" {
  count = var.import_existing_resources ? 0 : 1
  name  = "${var.environment}-rds-proxy-secrets-policy"
  role  = aws_iam_role.rds_proxy_role[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:*"
      },
      {
        Action = [
          "kms:Decrypt"
        ]
        Effect   = "Allow"
        Resource = "*" # KMS keys have been disabled
        Condition = {
          StringEquals = {
            "kms:ViaService" = "secretsmanager.${data.aws_region.current.name}.amazonaws.com"
          }
        }
      }
    ]
  })
}

resource "aws_db_proxy_default_target_group" "db_proxy_target" {
  count = var.enable_proxy && !var.import_existing_resources ? 1 : 0

  db_proxy_name = aws_db_proxy.db_proxy[0].name

  connection_pool_config {
    connection_borrow_timeout    = 120
    max_connections_percent      = 100
    max_idle_connections_percent = 50
    session_pinning_filters      = ["EXCLUDE_VARIABLE_SETS"] # Optimize connection reuse
    init_query                   = "SET time_zone='+00:00'"  # Set consistent timezone
  }
}

# Target for standard RDS instance
resource "aws_db_proxy_target" "db_proxy_target_rds" {
  count = var.enable_proxy && !var.import_existing_resources && !var.use_aurora_serverless ? 1 : 0

  db_proxy_name          = aws_db_proxy.db_proxy[0].name
  target_group_name      = aws_db_proxy_default_target_group.db_proxy_target[0].name
  db_instance_identifier = aws_db_instance.main[0].identifier
}

# Target for Aurora Serverless cluster
resource "aws_db_proxy_target" "db_proxy_target_aurora" {
  count = var.enable_proxy && !var.import_existing_resources && var.use_aurora_serverless ? 1 : 0

  db_proxy_name         = aws_db_proxy.db_proxy[0].name
  target_group_name     = aws_db_proxy_default_target_group.db_proxy_target[0].name
  db_cluster_identifier = aws_rds_cluster.aurora_serverless[0].id
}

# CloudWatch alarms for RDS Proxy
resource "aws_cloudwatch_metric_alarm" "db_proxy_connections" {
  count = var.enable_proxy && !var.import_existing_resources ? 1 : 0

  alarm_name          = "${var.environment}-db-proxy-connections"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "ClientConnections"
  namespace           = "AWS/RDS"
  period              = 60
  statistic           = "Average"
  threshold           = 4000 # 80% of max connections
  alarm_description   = "This metric monitors RDS Proxy client connections"
  alarm_actions       = []
  ok_actions          = []

  dimensions = {
    ProxyName = aws_db_proxy.db_proxy[0].name
  }

  tags = var.tags
}

resource "aws_cloudwatch_metric_alarm" "db_proxy_borrow_latency" {
  count = var.enable_proxy && !var.import_existing_resources ? 1 : 0

  alarm_name          = "${var.environment}-db-proxy-borrow-latency"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 2
  metric_name         = "DatabaseConnectionBorrowLatency"
  namespace           = "AWS/RDS"
  period              = 60
  statistic           = "Average"
  threshold           = 100 # 100ms
  alarm_description   = "This metric monitors RDS Proxy connection borrow latency"
  alarm_actions       = []
  ok_actions          = []

  dimensions = {
    ProxyName = aws_db_proxy.db_proxy[0].name
  }

  tags = var.tags
}
