output "rds_kms_key_arn" {
  description = "ARN of the KMS key used for RDS encryption"
  value       = var.kms_key_arn
}

output "cluster_endpoint" {
  description = "Endpoint for the RDS instance or Aurora cluster"
  value = var.import_existing_resources ? "${var.environment}-architrave-db.cluster-xxxxxxxxx.eu-central-1.rds.amazonaws.com" : (
    var.use_aurora_serverless ? aws_rds_cluster.aurora_serverless[0].endpoint : aws_db_instance.main[0].endpoint
  )
}

output "cluster_endpoint_ip" {
  description = "IP address of the RDS instance or Aurora cluster"
  value = var.import_existing_resources ? "${var.environment}-architrave-db.cluster-xxxxxxxxx.eu-central-1.rds.amazonaws.com" : (
    var.use_aurora_serverless ? replace(aws_rds_cluster.aurora_serverless[0].endpoint, "/^([^:]+).*$/", "$1") : replace(aws_db_instance.main[0].endpoint, "/^([^:]+).*$/", "$1")
  )
}

output "reader_endpoint" {
  description = "Reader endpoint for Aurora Serverless cluster"
  value       = var.use_aurora_serverless && !var.import_existing_resources ? aws_rds_cluster.aurora_serverless[0].reader_endpoint : ""
}

output "master_password" {
  description = "Master password for the RDS instance"
  value       = var.master_password != "" ? var.master_password : random_password.master_password[0].result
  sensitive   = true
}

output "rds_cluster_arn" {
  description = "ARN of the RDS instance or Aurora cluster"
  value = var.import_existing_resources ? "arn:aws:rds:eu-central-1:${data.aws_caller_identity.current.account_id}:db:${var.environment}-architrave-db" : (
    var.use_aurora_serverless ? aws_rds_cluster.aurora_serverless[0].arn : aws_db_instance.main[0].arn
  )
}

output "cluster_id" {
  description = "ID of the RDS instance or Aurora cluster"
  value = var.import_existing_resources ? "${var.environment}-architrave-db" : (
    var.use_aurora_serverless ? aws_rds_cluster.aurora_serverless[0].id : aws_db_instance.main[0].id
  )
}

output "serverless_capacity" {
  description = "Current capacity of Aurora Serverless cluster in ACUs"
  value       = var.use_aurora_serverless && !var.import_existing_resources ? "${var.aurora_min_capacity}-${var.aurora_max_capacity} ACUs" : "N/A"
}

output "secrets_arn" {
  description = "The ARN of the secrets manager secret as a list"
  value       = [aws_secretsmanager_secret.rds_master.arn]
}

output "rds_proxy_endpoint" {
  description = "Endpoint for the RDS proxy"
  value       = var.enable_proxy && !var.import_existing_resources ? aws_db_proxy.db_proxy[0].endpoint : ""
}

output "master_secret_arn" {
  description = "ARN of the RDS master secret"
  value       = aws_secretsmanager_secret.rds_master.arn
}
