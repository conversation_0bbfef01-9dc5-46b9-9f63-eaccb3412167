variable "environment" {
  description = "Deployment Environment"
  type        = string
}

variable "engine_version" {
  description = "Aurora MySQL engine version"
  type        = string
  default     = "8.0"

  validation {
    condition     = var.engine_version == "8.0"
    error_message = "Only Aurora MySQL 8.0 is supported."
  }
}

variable "instance_class" {
  description = "The instance class of the RDS instance"
  type        = string
  default     = "db.t3.medium"
}

variable "allocated_storage" {
  description = "Allocated storage in GB"
  type        = number
  default     = 20
}

variable "master_username" {
  description = "Master username for the RDS instance"
  type        = string
  default     = "admin"
}

variable "master_password" {
  description = "Master password for the RDS instance"
  type        = string
  default     = ""
  sensitive   = true
}

variable "lambda_role_arn" {
  description = "ARN of the IAM role for Lambda functions to access RDS"
  type        = string
  default     = ""
}

variable "vpc_id" {
  description = "VPC ID"
  type        = string
}

variable "subnet_ids" {
  description = "List of Subnet IDs"
  type        = list(string)
}

variable "tenant_kms_key_arns" {
  description = "Map of tenant IDs to their KMS key ARNs"
  type        = map(string)
}

variable "security_group_id" {
  description = "The security group ID to associate with the RDS instance."
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "tenants" {
  description = "Map of tenant configurations"
  type        = map(any)
  default     = {}
}

variable "skip_final_snapshot" {
  description = "Whether to skip final snapshot"
  type        = bool
  default     = true # Set to true to avoid final snapshot issues during development

  # Note: If this is set to false, final_snapshot_identifier must be provided
}

variable "multi_az" {
  description = "Whether to enable Multi-AZ deployment"
  type        = bool
  default     = true
}

variable "backup_retention_period" {
  description = "Number of days to retain backups"
  type        = number
  default     = 7
}

variable "apply_immediately" {
  description = "Whether to apply changes immediately"
  type        = bool
  default     = false
}

variable "kms_key_arn" {
  description = "ARN of the KMS key for RDS encryption"
  type        = string
  default     = ""
}

variable "enable_proxy" {
  description = "Whether to enable RDS Proxy"
  type        = bool
  default     = false
}

variable "db_name" {
  description = "The name of the database to create"
  type        = string
  default     = "architrave_db"
}

variable "db_username" {
  description = "The master username for the database"
  type        = string
  sensitive   = true
}

variable "db_password" {
  description = "The master password for the database"
  type        = string
  sensitive   = true
}

variable "deletion_protection" {
  description = "Whether to enable deletion protection"
  type        = bool
  default     = false # Changed to false to allow easier resource management during development
}

variable "import_existing_resources" {
  description = "Whether to import existing resources instead of creating new ones"
  type        = bool
  default     = true
}

variable "use_aurora_serverless" {
  description = "Whether to use Aurora Serverless v2 instead of standard RDS"
  type        = bool
  default     = false
}

variable "aurora_min_capacity" {
  description = "Minimum capacity for Aurora Serverless v2 (in ACUs)"
  type        = number
  default     = 0.5
}

variable "aurora_max_capacity" {
  description = "Maximum capacity for Aurora Serverless v2 (in ACUs)"
  type        = number
  default     = 16
}

variable "aurora_instance_count" {
  description = "Number of Aurora Serverless v2 instances (1 writer, rest are readers)"
  type        = number
  default     = 2
}

variable "availability_zones" {
  description = "List of availability zones for the RDS cluster"
  type        = list(string)
  default     = ["eu-central-1a", "eu-central-1b", "eu-central-1c"]
}

variable "engine_family" {
  description = "The family of the DB engine"
  type        = string
  default     = "mysql8.0"
}

variable "identifier" {
  description = "The identifier for the RDS instance"
  type        = string
}

variable "sns_kms_key_arn" {
  description = "KMS key ARN for encrypting SNS topics. Must be set for compliance."
  type        = string
  default     = ""
}
