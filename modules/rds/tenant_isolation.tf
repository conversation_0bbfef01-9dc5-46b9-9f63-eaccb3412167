# Enhanced Tenant isolation for multi-tenant database architecture
# This file implements schema-per-tenant isolation with proper security boundaries,
# tenant-specific encryption keys, and improved access controls

# Create KMS key for tenant database encryption
resource "aws_kms_key" "tenant_db_key" {
  count = !var.import_existing_resources ? 1 : 0

  description             = "${var.environment} tenant database encryption key"
  deletion_window_in_days = 30
  enable_key_rotation     = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "Allow RDS to use the key"
        Effect = "Allow"
        Principal = {
          Service = "rds.amazonaws.com"
        }
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = "*"
      },
      {
        Sid    = "Allow Lambda to use the key"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(var.tags, {
    Name = "${var.environment}-tenant-db-key"
  })
}

resource "aws_kms_alias" "tenant_db_key_alias" {
  count = !var.import_existing_resources ? 1 : 0

  name          = "alias/${var.environment}-tenant-db-key"
  target_key_id = aws_kms_key.tenant_db_key[0].key_id
}

# Create a Lambda function to manage tenant database schemas with enhanced isolation
resource "aws_lambda_function" "tenant_db_manager" {
  count = !var.import_existing_resources ? 1 : 0

  function_name    = "${var.environment}-tenant-db-manager"
  role             = aws_iam_role.tenant_db_manager_role[0].arn
  handler          = "index.handler"
  runtime          = "nodejs16.x"
  timeout          = 60
  memory_size      = 256
  source_code_hash = data.archive_file.tenant_db_manager_code[0].output_base64sha256
  filename         = data.archive_file.tenant_db_manager_code[0].output_path

  environment {
    variables = {
      RDS_SECRET_ARN       = aws_secretsmanager_secret.rds_master.arn
      RDS_ENDPOINT         = var.use_aurora_serverless ? aws_rds_cluster.aurora_serverless[0].endpoint : aws_db_instance.main[0].endpoint
      RDS_PROXY_ENDPOINT   = var.enable_proxy ? aws_db_proxy.db_proxy[0].endpoint : ""
      DEFAULT_DB_NAME      = var.db_name
      ENVIRONMENT          = var.environment
      TENANT_SECRET_PREFIX = "${var.environment}/tenant/"
      TENANT_KMS_KEY_ID    = aws_kms_key.tenant_db_key[0].key_id
    }
  }

  vpc_config {
    subnet_ids         = var.subnet_ids
    security_group_ids = [var.security_group_id]
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-tenant-db-manager"
  })
}

# Create IAM role for the Lambda function
resource "aws_iam_role" "tenant_db_manager_role" {
  count = !var.import_existing_resources ? 1 : 0

  name = "${var.environment}-tenant-db-manager-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Create IAM policy for the Lambda function
resource "aws_iam_role_policy" "tenant_db_manager_policy" {
  count = !var.import_existing_resources ? 1 : 0

  name = "${var.environment}-tenant-db-manager-policy"
  role = aws_iam_role.tenant_db_manager_role[0].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid = "SecretsManagerAccess"
        Action = [
          "secretsmanager:GetSecretValue"
        ]
        Effect = "Allow"
        Resource = [
          aws_secretsmanager_secret.rds_master.arn
        ]
      },
      {
        Sid = "TenantSecretsManagement"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:CreateSecret",
          "secretsmanager:PutSecretValue",
          "secretsmanager:UpdateSecret",
          "secretsmanager:DeleteSecret",
          "secretsmanager:TagResource"
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:${var.environment}/tenant/*"
        ]
      },
      {
        Sid = "CloudWatchLogsAccess"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.environment}-tenant-db-manager:*"
      },
      {
        Sid = "VPCNetworkInterfaceAccess"
        Action = [
          "ec2:CreateNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DeleteNetworkInterface",
          "ec2:DescribeSubnets",
          "ec2:DescribeSecurityGroups",
          "ec2:DescribeVpcs"
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      {
        Sid = "KMSAccess"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey",
          "kms:DescribeKey"
        ]
        Effect = "Allow"
        Resource = [
          aws_kms_key.tenant_db_key[0].arn,
          var.kms_key_arn != null ? var.kms_key_arn : aws_kms_key.tenant_db_key[0].arn
        ]
      }
    ]
  })
}

# Create Lambda function code
data "archive_file" "tenant_db_manager_code" {
  count = !var.import_existing_resources ? 1 : 0

  type        = "zip"
  output_path = "${path.module}/tenant_db_manager.zip"

  source {
    content  = <<EOF
const AWS = require('aws-sdk');
const mysql = require('mysql2/promise');
const secretsManager = new AWS.SecretsManager();

exports.handler = async (event) => {
    console.log('Received event:', JSON.stringify(event, null, 2));

    // Extract tenant information from the event
    const { action, tenantId, tenantName } = event;

    if (!tenantId) {
        throw new Error('Tenant ID is required');
    }

    // Get RDS credentials from Secrets Manager
    const masterSecretData = await secretsManager.getSecretValue({
        SecretId: process.env.RDS_SECRET_ARN
    }).promise();

    const masterSecret = JSON.parse(masterSecretData.SecretString);

    // Connect to the database
    const endpoint = process.env.RDS_PROXY_ENDPOINT || process.env.RDS_ENDPOINT;
    const connection = await mysql.createConnection({
        host: endpoint,
        user: masterSecret.username,
        password: masterSecret.password,
        database: process.env.DEFAULT_DB_NAME,
        ssl: { rejectUnauthorized: false }
    });

    try {
        // Perform the requested action
        switch (action) {
            case 'create':
                return await createTenant(connection, tenantId, tenantName);
            case 'delete':
                return await deleteTenant(connection, tenantId);
            case 'list':
                return await listTenants(connection);
            default:
                throw new Error("Unknown action: $${action}");
        }
    } finally {
        await connection.end();
    }
};

async function createTenant(connection, tenantId, tenantName) {
    // Create schema for tenant with enhanced isolation
    const schemaName = "tenant_$${tenantId}_db";

    // Create database with proper character set and collation
    await connection.query("CREATE DATABASE IF NOT EXISTS \`$${schemaName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");

    // Create tenant-specific user with limited permissions
    const username = "tenant_$${tenantId}";
    const password = generateSecurePassword();

    // Drop user if exists (for idempotency)
    await connection.query("DROP USER IF EXISTS '$${username}'@'%'");

    // Create user with access only to their schema and resource limits
    await connection.query("CREATE USER '$${username}'@'%' IDENTIFIED BY '$${password}' WITH MAX_QUERIES_PER_HOUR 100000 MAX_CONNECTIONS_PER_HOUR 1000 MAX_USER_CONNECTIONS 50");

    // Grant only necessary permissions - principle of least privilege
    await connection.query("GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, ALTER, INDEX, REFERENCES ON \`$${schemaName}\`.* TO '$${username}'@'%'");

    // Explicitly deny access to other databases
    await connection.query("REVOKE ALL PRIVILEGES ON *.* FROM '$${username}'@'%'");
    await connection.query("REVOKE GRANT OPTION ON *.* FROM '$${username}'@'%'");

    // Re-grant specific permissions to tenant's database only
    await connection.query("GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, ALTER, INDEX, REFERENCES ON \`$${schemaName}\`.* TO '$${username}'@'%'");

    await connection.query("FLUSH PRIVILEGES");

    // Store tenant credentials in Secrets Manager with encryption
    const secretName = "$${process.env.TENANT_SECRET_PREFIX}$${tenantId}";
    const secretValue = {
        username: username,
        password: password,
        engine: "mysql",
        host: process.env.RDS_ENDPOINT,
        port: 3306,
        dbname: schemaName,
        proxy_endpoint: process.env.RDS_PROXY_ENDPOINT || "",
        tenant_id: tenantId,
        created_at: new Date().toISOString()
    };

    try {
        // Try to get the secret first to see if it exists
        await secretsManager.getSecretValue({ SecretId: secretName }).promise();

        // If it exists, update it with KMS encryption
        await secretsManager.updateSecret({
            SecretId: secretName,
            SecretString: JSON.stringify(secretValue),
            KmsKeyId: process.env.TENANT_KMS_KEY_ID
        }).promise();

        console.log(`Updated secret for tenant $${tenantId} with KMS encryption`);
    } catch (error) {
        if (error.code === 'ResourceNotFoundException') {
            // If it doesn't exist, create it with KMS encryption
            await secretsManager.createSecret({
                Name: secretName,
                SecretString: JSON.stringify(secretValue),
                KmsKeyId: process.env.TENANT_KMS_KEY_ID,
                Tags: [
                    { Key: 'Environment', Value: process.env.ENVIRONMENT },
                    { Key: 'TenantId', Value: tenantId },
                    { Key: 'TenantName', Value: tenantName || tenantId },
                    { Key: 'ManagedBy', Value: 'terraform' },
                    { Key: 'Service', Value: 'tenant-db-manager' }
                ]
            }).promise();

            console.log(`Created new secret for tenant $${tenantId} with KMS encryption`);
        } else {
            console.error(`Error managing secret for tenant $${tenantId}: $${error.message}`);
            throw error;
        }
    }

    return {
        statusCode: 200,
        body: {
            message: "Tenant $${tenantId} created successfully",
            schemaName: schemaName,
            secretName: secretName
        }
    };
}

async function deleteTenant(connection, tenantId) {
    // Delete schema for tenant
    const schemaName = "tenant_$${tenantId}_db";
    await connection.query("DROP DATABASE IF EXISTS \`$${schemaName}\`");

    // Delete tenant-specific user
    const username = "tenant_$${tenantId}";
    await connection.query("DROP USER IF EXISTS '$${username}'@'%'");

    // Delete tenant credentials from Secrets Manager
    const secretName = "$${process.env.TENANT_SECRET_PREFIX}$${tenantId}";

    try {
        await secretsManager.deleteSecret({
            SecretId: secretName,
            ForceDeleteWithoutRecovery: true
        }).promise();
    } catch (error) {
        if (error.code !== 'ResourceNotFoundException') {
            throw error;
        }
    }

    return {
        statusCode: 200,
        body: {
            message: "Tenant $${tenantId} deleted successfully"
        }
    };
}

async function listTenants(connection) {
    // List all tenant schemas
    const [rows] = await connection.query(`
        SELECT SCHEMA_NAME
        FROM information_schema.SCHEMATA
        WHERE SCHEMA_NAME LIKE 'tenant_%_db'
    `);

    const tenants = rows.map(row => {
        const schemaName = row.SCHEMA_NAME;
        const tenantId = schemaName.replace('tenant_', '').replace('_db', '');
        return { tenantId, schemaName };
    });

    return {
        statusCode: 200,
        body: {
            tenants: tenants
        }
    };
}

function generateSecurePassword() {
    const length = 32;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+~`|}{[]:;?><,./-=';
    let password = '';

    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * charset.length);
        password += charset[randomIndex];
    }

    return password;
}
EOF
    filename = "index.js"
  }

  source {
    content  = <<EOF
{
  "name": "tenant-db-manager",
  "version": "1.0.0",
  "description": "Lambda function to manage tenant database schemas",
  "main": "index.js",
  "dependencies": {
    "aws-sdk": "^2.1048.0",
    "mysql2": "^2.3.3"
  }
}
EOF
    filename = "package.json"
  }
}

# Create CloudWatch Event Rule to monitor tenant database usage
resource "aws_cloudwatch_event_rule" "tenant_db_monitoring" {
  count = !var.import_existing_resources ? 1 : 0

  name        = "${var.environment}-tenant-db-monitoring"
  description = "Monitor tenant database usage"

  schedule_expression = "rate(1 hour)"

  tags = var.tags
}

# Create CloudWatch Event Target to invoke Lambda function
resource "aws_cloudwatch_event_target" "tenant_db_monitoring_target" {
  count = !var.import_existing_resources ? 1 : 0

  rule      = aws_cloudwatch_event_rule.tenant_db_monitoring[0].name
  target_id = "tenant-db-monitoring"
  arn       = aws_lambda_function.tenant_db_manager[0].arn

  input = jsonencode({
    action = "list"
  })
}

# Grant permission for CloudWatch Events to invoke Lambda function
resource "aws_lambda_permission" "tenant_db_monitoring_permission" {
  count = !var.import_existing_resources ? 1 : 0

  statement_id  = "AllowExecutionFromCloudWatch"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.tenant_db_manager[0].function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.tenant_db_monitoring[0].arn
}
