# Secrets Management Module
# This module implements secure secrets management using AWS Secrets Manager and KMS

# Create KMS key for secrets encryption
resource "aws_kms_key" "secrets_key" {
  description             = "${var.environment} secrets encryption key"
  deletion_window_in_days = 30
  enable_key_rotation     = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "Allow Secrets Manager to use the key"
        Effect = "Allow"
        Principal = {
          Service = "secretsmanager.amazonaws.com"
        }
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = "*"
      },
      {
        Sid    = "Allow EKS to use the key"
        Effect = "Allow"
        Principal = {
          Service = "eks.amazonaws.com"
        }
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey"
        ]
        Resource = "*"
      }
    ]
  })

  tags = merge(var.tags, {
    Name = "${var.environment}-secrets-key"
  })
}

# Create KMS alias for secrets encryption key
resource "aws_kms_alias" "secrets_key_alias" {
  name          = "alias/${var.environment}-secrets-key"
  target_key_id = aws_kms_key.secrets_key.key_id
}

# Create IAM role for EKS to access Secrets Manager
resource "aws_iam_role" "eks_secrets_manager_role" {
  name = "${var.environment}-eks-secrets-manager-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRoleWithWebIdentity"
        Effect = "Allow"
        Principal = {
          Federated = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${var.eks_oidc_provider}"
        }
        Condition = {
          StringEquals = {
            "${var.eks_oidc_provider}:sub" = "system:serviceaccount:kube-system:secrets-manager-sa"
          }
        }
      }
    ]
  })

  tags = merge(var.tags, {
    Name = "${var.environment}-eks-secrets-manager-role"
  })
}

# Create IAM policy for EKS to access Secrets Manager
resource "aws_iam_policy" "eks_secrets_manager_policy" {
  name        = "${var.environment}-eks-secrets-manager-policy"
  description = "Policy for EKS to access Secrets Manager"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "SecretsManagerAccess"
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ]
        Resource = [
          "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:${var.environment}/*"
        ]
      },
      {
        Sid    = "KMSAccess"
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey"
        ]
        Resource = [
          aws_kms_key.secrets_key.arn
        ]
      }
    ]
  })
}

# Attach IAM policy to EKS role
resource "aws_iam_role_policy_attachment" "eks_secrets_manager_policy_attachment" {
  role       = aws_iam_role.eks_secrets_manager_role.name
  policy_arn = aws_iam_policy.eks_secrets_manager_policy.arn
}

# Create Kubernetes service account for Secrets Manager
resource "kubernetes_service_account" "secrets_manager_sa" {
  metadata {
    name      = "secrets-manager-sa"
    namespace = "kube-system"
    annotations = {
      "eks.amazonaws.com/role-arn" = aws_iam_role.eks_secrets_manager_role.arn
    }
    labels = {
      "app.kubernetes.io/name"       = "secrets-manager-sa"
      "app.kubernetes.io/component"  = "secrets-manager"
      "app.kubernetes.io/part-of"    = "secrets-management"
      "app.kubernetes.io/managed-by" = "terraform"
    }
  }
}

# Create Kubernetes secret store driver
resource "kubernetes_manifest" "secret_store_driver" {
  manifest = {
    apiVersion = "secrets-store.csi.x-k8s.io/v1"
    kind       = "SecretProviderClass"
    metadata = {
      name      = "${var.environment}-aws-secrets"
      namespace = "kube-system"
    }
    spec = {
      provider = "aws"
      parameters = {
        region = data.aws_region.current.name
        pathToObjects = jsonencode([
          {
            objectName = "${var.environment}/*"
            objectType = "secretsmanager"
          }
        ])
        usePodIdentity = "true"
        roleArn        = aws_iam_role.eks_secrets_manager_role.arn
      }
    }
  }
}

# Create Kubernetes deployment for Secrets Manager CSI driver
resource "kubernetes_deployment" "secrets_manager_csi_driver" {
  metadata {
    name      = "secrets-manager-csi-driver"
    namespace = "kube-system"
    labels = {
      "app.kubernetes.io/name"       = "secrets-manager-csi-driver"
      "app.kubernetes.io/component"  = "secrets-manager"
      "app.kubernetes.io/part-of"    = "secrets-management"
      "app.kubernetes.io/managed-by" = "terraform"
    }
  }

  spec {
    replicas = 1

    selector {
      match_labels = {
        "app.kubernetes.io/name" = "secrets-manager-csi-driver"
      }
    }

    template {
      metadata {
        labels = {
          "app.kubernetes.io/name"       = "secrets-manager-csi-driver"
          "app.kubernetes.io/component"  = "secrets-manager"
          "app.kubernetes.io/part-of"    = "secrets-management"
          "app.kubernetes.io/managed-by" = "terraform"
        }
      }

      spec {
        service_account_name = kubernetes_service_account.secrets_manager_sa.metadata[0].name

        container {
          name  = "secrets-manager-csi-driver"
          image = "public.ecr.aws/aws-secrets-manager/secrets-store-csi-driver-provider-aws:1.0.0"

          volume_mount {
            name       = "socket-dir"
            mount_path = "/var/lib/kubelet/pods"
          }

          resources {
            limits = {
              cpu    = "100m"
              memory = "100Mi"
            }
            requests = {
              cpu    = "50m"
              memory = "50Mi"
            }
          }
        }

        volume {
          name = "socket-dir"
          host_path {
            path = "/var/lib/kubelet/pods"
          }
        }
      }
    }
  }
}
