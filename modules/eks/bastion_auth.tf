# This file configures the authentication for the bastion host to access the EKS cluster

# Create a ConfigMap to allow the bastion host to access the EKS cluster
resource "kubernetes_config_map" "aws_auth" {
  count = var.create_eks && !var.skip_kubernetes_resources ? 1 : 0

  metadata {
    name      = "aws-auth"
    namespace = "kube-system"
  }

  data = {
    mapRoles = yamlencode(
      concat(
        [
          {
            rolearn  = var.bastion_role_arn != "" ? var.bastion_role_arn : "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/${var.environment}-eks-bastion-role"
            username = "bastion-host"
            groups   = ["system:masters"]
          }
        ],
        var.map_roles
      )
    )
    mapUsers    = yamlencode(var.map_users)
    mapAccounts = yamlencode(var.map_accounts)
  }

  depends_on = [
    aws_eks_cluster.this,
    time_sleep.wait_for_cluster
  ]
}

# Create a script to update the aws-auth ConfigMap
resource "local_file" "update_aws_auth" {
  count = var.create_eks && !var.skip_kubernetes_resources ? 1 : 0

  content = <<-EOT
#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
NC='\033[0m' # No Color

# Get the bastion host IAM role ARN
BASTION_ROLE_ARN="${var.bastion_role_arn != "" ? var.bastion_role_arn : "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/${var.environment}-eks-bastion-role"}"
echo -e "$${YELLOW}Bastion host IAM role ARN: $BASTION_ROLE_ARN$${NC}"

# Create a ConfigMap to allow the bastion host to access the EKS cluster
echo -e "$${YELLOW}Creating ConfigMap to allow bastion host to access EKS cluster...$${NC}"
cat > aws-auth-configmap.yaml << EOC
apiVersion: v1
kind: ConfigMap
metadata:
  name: aws-auth
  namespace: kube-system
data:
  mapRoles: |
    - rolearn: $BASTION_ROLE_ARN
      username: bastion-host
      groups:
        - system:masters
EOC

# Apply the ConfigMap
echo -e "$${YELLOW}Applying ConfigMap...$${NC}"
kubectl apply -f aws-auth-configmap.yaml

if [ \$? -eq 0 ]; then
    echo -e "$${GREEN}ConfigMap applied successfully.$${NC}"
else
    echo -e "$${RED}Failed to apply ConfigMap.$${NC}"
    exit 1
fi

# Verify the ConfigMap
echo -e "$${YELLOW}Verifying ConfigMap...$${NC}"
kubectl get configmap aws-auth -n kube-system -o yaml

echo -e "$${GREEN}Done.$${NC}"
EOT

  filename        = "${path.module}/update-aws-auth.sh"
  file_permission = "0755"

  depends_on = [
    aws_eks_cluster.this,
    time_sleep.wait_for_cluster
  ]
}

# Create a null resource to run the script
resource "null_resource" "update_aws_auth" {
  count = var.create_eks && !var.skip_kubernetes_resources && !var.skip_eks_connectivity_check ? 1 : 0

  triggers = {
    cluster_name = aws_eks_cluster.this[0].name
    script_hash  = sha256(local_file.update_aws_auth[0].content)
  }

  provisioner "local-exec" {
    command = "${path.module}/update-aws-auth.sh"
  }

  depends_on = [
    aws_eks_cluster.this,
    time_sleep.wait_for_cluster,
    local_file.update_aws_auth
  ]
}
