# KEDA implementation for event-driven autoscaling

# Install KEDA using Helm
resource "helm_release" "keda" {
  count            = var.install_keda ? 1 : 0
  name             = "keda"
  repository       = "https://kedacore.github.io/charts"
  chart            = "keda"
  namespace        = "keda"
  create_namespace = true
  version          = var.keda_chart_version

  set {
    name  = "serviceAccount.create"
    value = "true"
  }

  set {
    name  = "podIdentity.azureWorkload.enabled"
    value = "false"
  }
}

# Create ScaledObject for RabbitMQ queues
resource "kubectl_manifest" "rabbitmq_scaled_object" {
  count     = var.install_keda ? 1 : 0
  yaml_body = <<-YAML
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: rabbitmq-scaler
  namespace: keda
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: rabbitmq-consumer
  pollingInterval: 30
  cooldownPeriod: 300
  minReplicaCount: 1
  maxReplicaCount: 10
  triggers:
  - type: rabbitmq
    metadata:
      protocol: amqp
      queueName: tenant-queue
      mode: QueueLength
      value: "5"
      host: rabbitmq.rabbitmq.svc.cluster.local
      queueLength: "10"
YAML

  depends_on = [helm_release.keda]
}

# Create ScaledObject for database metrics
resource "kubectl_manifest" "mysql_scaled_object" {
  count     = var.install_keda ? 1 : 0
  yaml_body = <<-YAML
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: mysql-scaler
  namespace: keda
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: mysql-worker
  pollingInterval: 30
  cooldownPeriod: 300
  minReplicaCount: 1
  maxReplicaCount: 10
  triggers:
  - type: mysql
    metadata:
      queryValue: "SELECT COUNT(*) FROM tenant_jobs WHERE status='pending'"
      targetQueryValue: "10"
      connectionStringFromEnv: MYSQL_CONN_STRING
YAML

  depends_on = [helm_release.keda]
}
