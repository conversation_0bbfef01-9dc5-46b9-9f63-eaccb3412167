# Autoscaling module for Kubernetes
# This module deploys various autoscaling components for Kubernetes

# Local variables
locals {
  namespace = var.namespace != "" ? var.namespace : "kube-system"
}

# Deploy Metrics Server for basic metrics
resource "helm_release" "metrics_server" {
  count      = var.deploy_metrics_server && !var.skip_k8s_connection ? 1 : 0
  name       = "metrics-server"
  repository = "https://kubernetes-sigs.github.io/metrics-server/"
  chart      = "metrics-server"
  namespace  = local.namespace
  version    = var.metrics_server_version

  set {
    name  = "args[0]"
    value = "--kubelet-insecure-tls"
  }

  set {
    name  = "resources.requests.cpu"
    value = "50m"
  }

  set {
    name  = "resources.requests.memory"
    value = "64Mi"
  }

  set {
    name  = "resources.limits.cpu"
    value = "100m"
  }

  set {
    name  = "resources.limits.memory"
    value = "128Mi"
  }
}

# Deploy Vertical Pod Autoscaler for resource recommendation
resource "helm_release" "vertical_pod_autoscaler" {
  count      = var.deploy_vpa && !var.skip_k8s_connection ? 1 : 0
  name       = "vpa"
  repository = "https://charts.fairwinds.com/stable"
  chart      = "vpa"
  namespace  = local.namespace
  version    = var.vpa_version

  set {
    name  = "recommender.enabled"
    value = "true"
  }

  set {
    name  = "recommender.resources.requests.cpu"
    value = "50m"
  }

  set {
    name  = "recommender.resources.requests.memory"
    value = "256Mi"
  }

  set {
    name  = "recommender.resources.limits.cpu"
    value = "100m"
  }

  set {
    name  = "recommender.resources.limits.memory"
    value = "512Mi"
  }

  set {
    name  = "updater.enabled"
    value = "true"
  }

  set {
    name  = "updater.resources.requests.cpu"
    value = "50m"
  }

  set {
    name  = "updater.resources.requests.memory"
    value = "256Mi"
  }

  set {
    name  = "updater.resources.limits.cpu"
    value = "100m"
  }

  set {
    name  = "updater.resources.limits.memory"
    value = "512Mi"
  }

  depends_on = [helm_release.metrics_server]
}

# Compute VPA update modes: if HPA is present for a deployment and VPA is set to Auto, force VPA to Off
locals {
  vpa_update_modes = {
    for k, v in var.vpas :
    k => (
      contains(keys(var.hpas), k) && v.update_mode == "Auto" ? "Off" : v.update_mode
    )
  }
}

# Create VPAs for deployments, with updateMode logic
resource "kubernetes_manifest" "vpa" {
  for_each = var.vpas

  manifest = {
    apiVersion = "autoscaling.k8s.io/v1"
    kind       = "VerticalPodAutoscaler"
    metadata = {
      name      = "${each.key}-vpa"
      namespace = each.value.namespace
    }
    spec = {
      targetRef = {
        apiVersion = "apps/v1"
        kind       = "Deployment"
        name       = each.key
      }
      updatePolicy = {
        updateMode = local.vpa_update_modes[each.key]
      }
      resourcePolicy = {
        containerPolicies = [
          {
            containerName = "*"
            minAllowed = {
              cpu    = each.value.min_cpu
              memory = each.value.min_memory
            }
            maxAllowed = {
              cpu    = each.value.max_cpu
              memory = each.value.max_memory
            }
            controlledResources = ["cpu", "memory"]
          }
        ]
      }
    }
  }

  depends_on = [helm_release.vertical_pod_autoscaler]
}

# Create Pod Disruption Budgets for deployments
resource "kubernetes_pod_disruption_budget_v1" "pdb" {
  for_each = var.deploy_pdbs && !var.skip_k8s_connection ? var.pdbs : {}

  metadata {
    name      = "${each.key}-pdb"
    namespace = each.value.namespace
  }

  spec {
    min_available = each.value.min_available
    selector {
      match_labels = {
        app = each.key
      }
    }
  }
}

# Create HorizontalPodAutoscalers for deployments
resource "kubernetes_horizontal_pod_autoscaler_v2" "hpa" {
  for_each = var.deploy_hpa && !var.skip_k8s_connection ? var.hpas : {}

  metadata {
    name      = "${each.key}-hpa"
    namespace = each.value.namespace
  }

  spec {
    scale_target_ref {
      api_version = "apps/v1"
      kind        = "Deployment"
      name        = each.key
    }

    min_replicas = each.value.min_replicas
    max_replicas = each.value.max_replicas

    metric {
      type = "Resource"
      resource {
        name = "cpu"
        target {
          type                = "Utilization"
          average_utilization = each.value.cpu_threshold
        }
      }
    }

    metric {
      type = "Resource"
      resource {
        name = "memory"
        target {
          type                = "Utilization"
          average_utilization = each.value.memory_threshold
        }
      }
    }
  }

  depends_on = [helm_release.metrics_server]
}

# Create namespace for Goldilocks if it doesn't exist
resource "kubernetes_namespace" "goldilocks" {
  count = var.deploy_goldilocks && !var.skip_k8s_connection ? 1 : 0

  metadata {
    name = var.goldilocks_namespace
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
    }
  }
}

# Deploy KEDA for event-driven autoscaling
resource "kubernetes_namespace" "keda" {
  count = var.deploy_keda && !var.skip_k8s_connection ? 1 : 0

  metadata {
    name = var.keda_namespace
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
    }
  }
}

# Create ScaledObjects for CPU-based scaling
resource "kubernetes_manifest" "cpu_scaled_object" {
  for_each = var.deploy_keda && !var.skip_k8s_connection ? var.cpu_scaled_objects : {}

  manifest = {
    apiVersion = "keda.sh/v1alpha1"
    kind       = "ScaledObject"
    metadata = {
      name      = "${each.key}-cpu-scaler"
      namespace = each.value.namespace
    }
    spec = {
      scaleTargetRef = {
        name = each.key
      }
      minReplicaCount = each.value.min_replicas
      maxReplicaCount = each.value.max_replicas
      pollingInterval = each.value.polling_interval
      cooldownPeriod  = each.value.cooldown_period
      triggers = [
        {
          type = "cpu"
          metadata = {
            type  = "Utilization"
            value = tostring(each.value.cpu_threshold)
          }
        }
      ]
    }
  }
}

# Create ScaledObjects for memory-based scaling
resource "kubernetes_manifest" "memory_scaled_object" {
  for_each = var.deploy_keda && !var.skip_k8s_connection ? var.memory_scaled_objects : {}

  manifest = {
    apiVersion = "keda.sh/v1alpha1"
    kind       = "ScaledObject"
    metadata = {
      name      = "${each.key}-memory-scaler"
      namespace = each.value.namespace
    }
    spec = {
      scaleTargetRef = {
        name = each.key
      }
      minReplicaCount = each.value.min_replicas
      maxReplicaCount = each.value.max_replicas
      pollingInterval = each.value.polling_interval
      cooldownPeriod  = each.value.cooldown_period
      triggers = [
        {
          type = "memory"
          metadata = {
            type  = "Utilization"
            value = tostring(each.value.memory_threshold)
          }
        }
      ]
    }
  }
}

# Create ScaledObjects for Prometheus-based scaling
resource "kubernetes_manifest" "prometheus_scaled_object" {
  for_each = var.deploy_keda && !var.skip_k8s_connection ? var.prometheus_scaled_objects : {}

  manifest = {
    apiVersion = "keda.sh/v1alpha1"
    kind       = "ScaledObject"
    metadata = {
      name      = "${each.key}-prometheus-scaler"
      namespace = each.value.namespace
    }
    spec = {
      scaleTargetRef = {
        name = each.key
      }
      minReplicaCount = each.value.min_replicas
      maxReplicaCount = each.value.max_replicas
      pollingInterval = each.value.polling_interval
      cooldownPeriod  = each.value.cooldown_period
      triggers = [
        {
          type = "prometheus"
          metadata = {
            serverAddress = each.value.prometheus_server
            metricName    = each.value.metric_name
            query         = each.value.query
            threshold     = tostring(each.value.threshold)
          }
        }
      ]
    }
  }
}
