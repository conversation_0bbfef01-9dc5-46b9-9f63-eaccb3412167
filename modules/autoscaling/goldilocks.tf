# Goldilocks implementation for resource recommendation

# Install Goldilocks using Helm
resource "helm_release" "goldilocks" {
  count            = var.install_goldilocks ? 1 : 0
  name             = "goldilocks"
  repository       = "https://charts.fairwinds.com/stable"
  chart            = "goldilocks"
  namespace        = "goldilocks"
  create_namespace = true
  version          = var.goldilocks_chart_version

  set {
    name  = "dashboard.enabled"
    value = "true"
  }

  set {
    name  = "dashboard.service.type"
    value = "ClusterIP"
  }

  set {
    name  = "vpa.enabled"
    value = "false"
  }

  depends_on = [helm_release.vpa]
}

# Label tenant namespaces for Goldilocks
resource "kubectl_manifest" "goldilocks_namespace_label" {
  for_each = var.install_goldilocks ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: v1
kind: Namespace
metadata:
  name: ${each.key}
  labels:
    goldilocks.fairwinds.com/enabled: "true"
YAML

  depends_on = [helm_release.goldilocks]
}
