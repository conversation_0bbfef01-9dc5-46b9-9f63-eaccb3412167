# Vertical Pod Autoscaler implementation

# Install VPA using Helm
resource "helm_release" "vpa" {
  count            = var.install_vpa ? 1 : 0
  name             = "vpa"
  repository       = "https://charts.fairwinds.com/stable"
  chart            = "vpa"
  namespace        = "vpa"
  create_namespace = true
  version          = var.vpa_chart_version

  set {
    name  = "recommender.enabled"
    value = "true"
  }

  set {
    name  = "updater.enabled"
    value = "true"
  }

  set {
    name  = "admissionController.enabled"
    value = "true"
  }
}

# Create VPA for tenant deployments
resource "kubectl_manifest" "tenant_vpa" {
  for_each = var.create_default_pdbs ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: ${each.key}-vpa
  namespace: ${each.key}
spec:
  targetRef:
    apiVersion: "apps/v1"
    kind: Deployment
    name: ${each.key}-backend
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
      - containerName: "*"
        minAllowed:
          cpu: 50m
          memory: 100Mi
        maxAllowed:
          cpu: 1000m
          memory: 2Gi
        controlledResources: ["cpu", "memory"]
YAML

  depends_on = [helm_release.vpa]
}
