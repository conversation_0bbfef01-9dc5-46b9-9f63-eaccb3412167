# API Management Module
# This module provides a comprehensive API management solution with multiple gateway options

locals {
  name_prefix = "${var.environment}-${var.name}"
  
  common_tags = merge(var.tags, {
    Environment = var.environment
    ManagedBy   = "terraform"
    Module      = "api-management"
    Project     = var.name
  })

  # Gateway selection logic
  use_aws_api_gateway = var.api_gateway_type == "aws"
  use_istio_gateway   = var.api_gateway_type == "istio"
  # use_kong_gateway    = var.api_gateway_type == "kong"      # Future implementation
  # use_ambassador      = var.api_gateway_type == "ambassador" # Future implementation
}

# AWS API Gateway Implementation
module "aws_api_gateway" {
  count  = local.use_aws_api_gateway ? 1 : 0
  source = "./aws_api_gateway"

  environment         = var.environment
  name               = var.name
  tags               = local.common_tags
  domain_name        = var.domain_name
  certificate_arn    = var.certificate_arn
  vpc_id             = var.vpc_id
  private_subnet_ids = var.private_subnet_ids
  alb_arn           = var.alb_arn
  tenants           = var.tenants
  rate_limiting     = var.rate_limiting
  api_versions      = var.api_versions
}

# Istio Gateway Implementation (leverages existing Istio setup)
module "istio_api_gateway" {
  count  = local.use_istio_gateway ? 1 : 0
  source = "./istio_api_gateway"

  environment           = var.environment
  name                 = var.name
  tags                 = local.common_tags
  domain_name          = var.domain_name
  istio_namespace      = var.istio_namespace
  skip_k8s_connection  = var.skip_k8s_connection
  tenants              = var.tenants
  rate_limiting        = var.rate_limiting
  api_versions         = var.api_versions
}

# Kong Gateway Implementation (Future implementation)
# module "kong_gateway" {
#   count  = local.use_kong_gateway ? 1 : 0
#   source = "./kong_gateway"
#
#   environment           = var.environment
#   name                 = var.name
#   tags                 = local.common_tags
#   domain_name          = var.domain_name
#   skip_k8s_connection  = var.skip_k8s_connection
#   kong_namespace       = var.kong_namespace
#   kong_version         = var.kong_version
#   tenants              = var.tenants
#   rate_limiting        = var.rate_limiting
#   api_versions         = var.api_versions
# }

# Ambassador Gateway Implementation (Future implementation)
# module "ambassador_gateway" {
#   count  = local.use_ambassador ? 1 : 0
#   source = "./ambassador_gateway"
#
#   environment           = var.environment
#   name                 = var.name
#   tags                 = local.common_tags
#   domain_name          = var.domain_name
#   skip_k8s_connection  = var.skip_k8s_connection
#   ambassador_namespace = var.ambassador_namespace
#   ambassador_version   = var.ambassador_version
#   tenants              = var.tenants
#   rate_limiting        = var.rate_limiting
#   api_versions         = var.api_versions
# }

# CloudWatch dashboard for API metrics
resource "aws_cloudwatch_dashboard" "api_management" {
  dashboard_name = "${local.name_prefix}-api-management"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6

        properties = {
          metrics = [
            ["AWS/ApiGateway", "Count", "ApiName", local.name_prefix],
            [".", "Latency", ".", "."],
            [".", "4XXError", ".", "."],
            [".", "5XXError", ".", "."]
          ]
          view    = "timeSeries"
          stacked = false
          region  = data.aws_region.current.name
          title   = "API Gateway Metrics"
          period  = 300
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 0
        width  = 12
        height = 6

        properties = {
          metrics = concat([
            for tenant_key, tenant in var.tenants : [
              "AWS/ApiGateway", "Count", "ApiName", "${local.name_prefix}-${tenant_key}"
            ]
          ])
          view    = "timeSeries"
          stacked = false
          region  = data.aws_region.current.name
          title   = "Per-Tenant API Metrics"
          period  = 300
        }
      }
    ]
  })

  # CloudWatch dashboards don't support tags
}

# CloudWatch alarms for API monitoring
resource "aws_cloudwatch_metric_alarm" "api_high_latency" {
  for_each = var.tenants

  alarm_name          = "${local.name_prefix}-${each.key}-high-latency"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "Latency"
  namespace           = "AWS/ApiGateway"
  period              = "300"
  statistic           = "Average"
  threshold           = var.latency_threshold
  alarm_description   = "This metric monitors API latency for tenant ${each.key}"
  alarm_actions       = [aws_sns_topic.api_alerts.arn]

  dimensions = {
    ApiName = "${local.name_prefix}-${each.key}"
  }

  tags = merge(local.common_tags, {
    Tenant = each.key
  })
}

resource "aws_cloudwatch_metric_alarm" "api_error_rate" {
  for_each = var.tenants

  alarm_name          = "${local.name_prefix}-${each.key}-error-rate"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "4XXError"
  namespace           = "AWS/ApiGateway"
  period              = "300"
  statistic           = "Sum"
  threshold           = var.error_rate_threshold
  alarm_description   = "This metric monitors API error rate for tenant ${each.key}"
  alarm_actions       = [aws_sns_topic.api_alerts.arn]

  dimensions = {
    ApiName = "${local.name_prefix}-${each.key}"
  }

  tags = merge(local.common_tags, {
    Tenant = each.key
  })
}

# SNS topic for API alerts
resource "aws_sns_topic" "api_alerts" {
  name = "${local.name_prefix}-api-alerts"
  tags = local.common_tags
}

# Data sources
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
