variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "name" {
  description = "Project name"
  type        = string
}

variable "tags" {
  description = "Map of tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "api_gateway_type" {
  description = "Type of API Gateway to use (aws, istio, kong, ambassador)"
  type        = string
  default     = "istio"
  validation {
    condition     = contains(["aws", "istio", "kong", "ambassador"], var.api_gateway_type)
    error_message = "API Gateway type must be one of: aws, istio, kong, ambassador."
  }
}

variable "domain_name" {
  description = "Domain name for API endpoints"
  type        = string
}

variable "certificate_arn" {
  description = "ACM certificate ARN for HTTPS"
  type        = string
  default     = ""
}

variable "vpc_id" {
  description = "VPC ID for AWS API Gateway"
  type        = string
  default     = ""
}

variable "private_subnet_ids" {
  description = "Private subnet IDs for AWS API Gateway"
  type        = list(string)
  default     = []
}

variable "alb_arn" {
  description = "ALB ARN for AWS API Gateway integration"
  type        = string
  default     = ""
}

variable "istio_namespace" {
  description = "Istio system namespace"
  type        = string
  default     = "istio-system"
}

variable "kong_namespace" {
  description = "Kong namespace"
  type        = string
  default     = "kong"
}

variable "kong_version" {
  description = "Kong version"
  type        = string
  default     = "3.4"
}

variable "ambassador_namespace" {
  description = "Ambassador namespace"
  type        = string
  default     = "ambassador"
}

variable "ambassador_version" {
  description = "Ambassador version"
  type        = string
  default     = "3.8.0"
}

variable "skip_k8s_connection" {
  description = "Skip Kubernetes connection for initial setup"
  type        = bool
  default     = false
}

variable "latency_threshold" {
  description = "Latency threshold for alarms (milliseconds)"
  type        = number
  default     = 1000
}

variable "error_rate_threshold" {
  description = "Error rate threshold for alarms"
  type        = number
  default     = 10
}

variable "tenants" {
  description = "Map of tenant configurations for API management"
  type = map(object({
    id               = string
    namespace_prefix = string
    api_config = optional(object({
      rate_limit_per_minute = optional(number, 1000)
      burst_limit          = optional(number, 2000)
      quota_per_day        = optional(number, 100000)
      enable_caching       = optional(bool, true)
      cache_ttl_seconds    = optional(number, 300)
      enable_compression   = optional(bool, true)
      cors_enabled         = optional(bool, true)
      cors_origins         = optional(list(string), ["*"])
    }), {})
    versions = optional(list(object({
      version     = string
      path_prefix = string
      is_default  = optional(bool, false)
      deprecated  = optional(bool, false)
      sunset_date = optional(string, "")
    })), [])
  }))
  default = {}
}

variable "rate_limiting" {
  description = "Global rate limiting configuration"
  type = object({
    enabled               = bool
    requests_per_minute   = number
    burst_limit          = number
    per_tenant_override  = bool
  })
  default = {
    enabled               = true
    requests_per_minute   = 1000
    burst_limit          = 2000
    per_tenant_override  = true
  }
}

variable "api_versions" {
  description = "API versioning configuration"
  type = object({
    strategy           = string # "path", "header", "query"
    default_version    = string
    supported_versions = list(string)
    deprecation_policy = object({
      notice_period_days = number
      sunset_period_days = number
    })
  })
  default = {
    strategy           = "path"
    default_version    = "v1"
    supported_versions = ["v1", "v2"]
    deprecation_policy = {
      notice_period_days = 90
      sunset_period_days = 180
    }
  }
}
