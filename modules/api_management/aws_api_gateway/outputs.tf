output "api_id" {
  description = "ID of the API Gateway"
  value       = aws_api_gateway_rest_api.main.id
}

output "api_arn" {
  description = "ARN of the API Gateway"
  value       = aws_api_gateway_rest_api.main.arn
}

output "stage_name" {
  description = "Name of the API Gateway stage"
  value       = aws_api_gateway_stage.main.stage_name
}

output "invoke_url" {
  description = "Invoke URL of the API Gateway"
  value       = "https://${aws_api_gateway_rest_api.main.id}.execute-api.${data.aws_region.current.name}.amazonaws.com/${aws_api_gateway_stage.main.stage_name}"
}

output "custom_domain_name" {
  description = "Custom domain name for the API"
  value       = var.certificate_arn != "" ? aws_api_gateway_domain_name.main[0].domain_name : null
}

output "tenant_api_keys" {
  description = "API keys for each tenant"
  value = {
    for tenant_key, tenant in var.tenants : tenant_key => aws_api_gateway_api_key.tenant_keys[tenant_key].value
  }
  sensitive = true
}

output "tenant_usage_plans" {
  description = "Usage plan IDs for each tenant"
  value = {
    for tenant_key, tenant in var.tenants : tenant_key => aws_api_gateway_usage_plan.tenant_plans[tenant_key].id
  }
}

output "cloudwatch_log_group" {
  description = "CloudWatch log group for API Gateway"
  value       = aws_cloudwatch_log_group.api_gateway.name
}

output "api_endpoint" {
  description = "API endpoint URL"
  value       = var.certificate_arn != "" ? "https://${aws_api_gateway_domain_name.main[0].domain_name}" : "https://${aws_api_gateway_rest_api.main.id}.execute-api.${data.aws_region.current.name}.amazonaws.com/${aws_api_gateway_stage.main.stage_name}"
}

output "tenant_endpoints" {
  description = "Tenant-specific API endpoints"
  value = {
    for tenant_key, tenant in var.tenants : tenant_key => {
      endpoint = var.certificate_arn != "" ? "https://${aws_api_gateway_domain_name.main[0].domain_name}/${tenant_key}" : "https://${aws_api_gateway_rest_api.main.id}.execute-api.${data.aws_region.current.name}.amazonaws.com/${aws_api_gateway_stage.main.stage_name}/${tenant_key}"
      api_key  = aws_api_gateway_api_key.tenant_keys[tenant_key].value
    }
  }
  sensitive = true
}

output "rate_limiting_config" {
  description = "Rate limiting configuration"
  value       = var.rate_limiting
}

output "supported_versions" {
  description = "Supported API versions"
  value       = var.api_versions.supported_versions
}

output "default_version" {
  description = "Default API version"
  value       = var.api_versions.default_version
}
