# AWS API Gateway Implementation
# Provides managed API Gateway service with ALB integration

locals {
  name_prefix = "${var.environment}-${var.name}"
  
  common_tags = merge(var.tags, {
    Environment = var.environment
    ManagedBy   = "terraform"
    Module      = "aws-api-gateway"
    Project     = var.name
  })
}

# API Gateway REST API
resource "aws_api_gateway_rest_api" "main" {
  name        = "${local.name_prefix}-api"
  description = "API Gateway for ${var.environment} environment"

  endpoint_configuration {
    types            = ["REGIONAL"]
    vpc_endpoint_ids = var.vpc_endpoint_ids
  }

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = "*"
        Action = "execute-api:Invoke"
        Resource = "*"
        Condition = {
          StringEquals = {
            "aws:sourceVpc" = var.vpc_id
          }
        }
      }
    ]
  })

  tags = local.common_tags
}

# API Gateway deployment
resource "aws_api_gateway_deployment" "main" {
  depends_on = [
    aws_api_gateway_method.proxy,
    aws_api_gateway_integration.proxy
  ]

  rest_api_id = aws_api_gateway_rest_api.main.id
  stage_name  = var.environment

  lifecycle {
    create_before_destroy = true
  }
}

# API Gateway stage
resource "aws_api_gateway_stage" "main" {
  deployment_id = aws_api_gateway_deployment.main.id
  rest_api_id   = aws_api_gateway_rest_api.main.id
  stage_name    = var.environment

  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.api_gateway.arn
    format = jsonencode({
      requestId      = "$context.requestId"
      ip             = "$context.identity.sourceIp"
      caller         = "$context.identity.caller"
      user           = "$context.identity.user"
      requestTime    = "$context.requestTime"
      httpMethod     = "$context.httpMethod"
      resourcePath   = "$context.resourcePath"
      status         = "$context.status"
      protocol       = "$context.protocol"
      responseLength = "$context.responseLength"
    })
  }

  tags = local.common_tags
}

# CloudWatch log group for API Gateway
resource "aws_cloudwatch_log_group" "api_gateway" {
  name              = "/aws/apigateway/${local.name_prefix}"
  retention_in_days = 14
  tags              = local.common_tags
}

# API Gateway resource for proxy
resource "aws_api_gateway_resource" "proxy" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  parent_id   = aws_api_gateway_rest_api.main.root_resource_id
  path_part   = "{proxy+}"
}

# API Gateway method
resource "aws_api_gateway_method" "proxy" {
  rest_api_id   = aws_api_gateway_rest_api.main.id
  resource_id   = aws_api_gateway_resource.proxy.id
  http_method   = "ANY"
  authorization = "NONE"

  request_parameters = {
    "method.request.path.proxy" = true
  }
}

# API Gateway integration with ALB
resource "aws_api_gateway_integration" "proxy" {
  rest_api_id = aws_api_gateway_rest_api.main.id
  resource_id = aws_api_gateway_method.proxy.resource_id
  http_method = aws_api_gateway_method.proxy.http_method

  integration_http_method = "ANY"
  type                   = "HTTP_PROXY"
  uri                    = "http://${data.aws_lb.alb.dns_name}/{proxy}"

  request_parameters = {
    "integration.request.path.proxy" = "method.request.path.proxy"
  }
}

# Usage plans for rate limiting
resource "aws_api_gateway_usage_plan" "tenant_plans" {
  for_each = var.tenants

  name         = "${local.name_prefix}-${each.key}-plan"
  description  = "Usage plan for tenant ${each.key}"

  api_stages {
    api_id = aws_api_gateway_rest_api.main.id
    stage  = aws_api_gateway_stage.main.stage_name
  }

  quota_settings {
    limit  = each.value.api_config.quota_per_day
    period = "DAY"
  }

  throttle_settings {
    rate_limit  = each.value.api_config.rate_limit_per_minute / 60
    burst_limit = each.value.api_config.burst_limit
  }

  tags = merge(local.common_tags, {
    Tenant = each.key
  })
}

# API keys for tenants
resource "aws_api_gateway_api_key" "tenant_keys" {
  for_each = var.tenants

  name        = "${local.name_prefix}-${each.key}-key"
  description = "API key for tenant ${each.key}"

  tags = merge(local.common_tags, {
    Tenant = each.key
  })
}

# Usage plan key associations
resource "aws_api_gateway_usage_plan_key" "tenant_plan_keys" {
  for_each = var.tenants

  key_id        = aws_api_gateway_api_key.tenant_keys[each.key].id
  key_type      = "API_KEY"
  usage_plan_id = aws_api_gateway_usage_plan.tenant_plans[each.key].id
}

# Custom domain name
resource "aws_api_gateway_domain_name" "main" {
  count = var.certificate_arn != "" ? 1 : 0

  domain_name              = "api.${var.domain_name}"
  regional_certificate_arn = var.certificate_arn

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  tags = local.common_tags
}

# Base path mapping
resource "aws_api_gateway_base_path_mapping" "main" {
  count = var.certificate_arn != "" ? 1 : 0

  api_id      = aws_api_gateway_rest_api.main.id
  stage_name  = aws_api_gateway_stage.main.stage_name
  domain_name = aws_api_gateway_domain_name.main[0].domain_name
}

# Route53 record for custom domain
resource "aws_route53_record" "api" {
  count = var.certificate_arn != "" && var.route53_zone_id != "" ? 1 : 0

  zone_id = var.route53_zone_id
  name    = "api.${var.domain_name}"
  type    = "A"

  alias {
    name                   = aws_api_gateway_domain_name.main[0].regional_domain_name
    zone_id                = aws_api_gateway_domain_name.main[0].regional_zone_id
    evaluate_target_health = true
  }
}

# Data sources
data "aws_lb" "alb" {
  arn = var.alb_arn
}

data "aws_region" "current" {}
