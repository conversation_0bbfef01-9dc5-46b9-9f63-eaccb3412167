variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "name" {
  description = "Project name"
  type        = string
}

variable "tags" {
  description = "Map of tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "domain_name" {
  description = "Domain name for API endpoints"
  type        = string
}

variable "certificate_arn" {
  description = "ACM certificate ARN for HTTPS"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID for API Gateway"
  type        = string
}

variable "vpc_endpoint_ids" {
  description = "VPC endpoint IDs for API Gateway"
  type        = list(string)
  default     = []
}

variable "private_subnet_ids" {
  description = "Private subnet IDs"
  type        = list(string)
}

variable "alb_arn" {
  description = "ALB ARN for integration"
  type        = string
}

variable "route53_zone_id" {
  description = "Route53 hosted zone ID"
  type        = string
  default     = ""
}

variable "tenants" {
  description = "Map of tenant configurations"
  type = map(object({
    id               = string
    namespace_prefix = string
    api_config = object({
      rate_limit_per_minute = number
      burst_limit          = number
      quota_per_day        = number
      enable_caching       = bool
      cache_ttl_seconds    = number
      enable_compression   = bool
      cors_enabled         = bool
      cors_origins         = list(string)
    })
  }))
  default = {}
}

variable "rate_limiting" {
  description = "Rate limiting configuration"
  type = object({
    enabled               = bool
    requests_per_minute   = number
    burst_limit          = number
    per_tenant_override  = bool
  })
}

variable "api_versions" {
  description = "API versioning configuration"
  type = object({
    strategy           = string
    default_version    = string
    supported_versions = list(string)
    deprecation_policy = object({
      notice_period_days = number
      sunset_period_days = number
    })
  })
}
