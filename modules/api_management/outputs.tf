output "api_gateway_type" {
  description = "Type of API Gateway deployed"
  value       = var.api_gateway_type
}

output "api_endpoint" {
  description = "API Gateway endpoint URL"
  value = var.api_gateway_type == "aws" && length(module.aws_api_gateway) > 0 ? (
    var.certificate_arn != "" ? "https://api.${var.domain_name}" : module.aws_api_gateway[0].invoke_url
  ) : "https://api.${var.domain_name}"
}

output "aws_api_gateway_id" {
  description = "AWS API Gateway ID"
  value       = var.api_gateway_type == "aws" && length(module.aws_api_gateway) > 0 ? module.aws_api_gateway[0].api_id : null
}

output "aws_api_gateway_stage" {
  description = "AWS API Gateway stage name"
  value       = var.api_gateway_type == "aws" && length(module.aws_api_gateway) > 0 ? module.aws_api_gateway[0].stage_name : null
}

output "tenant_api_keys" {
  description = "API keys for each tenant (AWS API Gateway only)"
  value = var.api_gateway_type == "aws" && length(module.aws_api_gateway) > 0 ? module.aws_api_gateway[0].tenant_api_keys : {}
  sensitive = true
}

output "istio_gateway_name" {
  description = "Istio Gateway name"
  value       = var.api_gateway_type == "istio" && length(module.istio_api_gateway) > 0 ? module.istio_api_gateway[0].gateway_name : null
}

output "kong_gateway_status" {
  description = "Kong Gateway deployment status"
  value       = var.api_gateway_type == "kong" ? "not_implemented" : null
}

output "ambassador_gateway_status" {
  description = "Ambassador Gateway deployment status"
  value       = var.api_gateway_type == "ambassador" ? "not_implemented" : null
}

output "api_dashboard_url" {
  description = "CloudWatch dashboard URL for API metrics"
  value       = "https://console.aws.amazon.com/cloudwatch/home?region=${data.aws_region.current.name}#dashboards:name=${aws_cloudwatch_dashboard.api_management.dashboard_name}"
}

output "rate_limiting_config" {
  description = "Rate limiting configuration applied"
  value       = var.rate_limiting
}

output "api_versions_config" {
  description = "API versioning configuration"
  value       = var.api_versions
}

output "tenant_endpoints" {
  description = "API endpoints for each tenant"
  value = {
    for tenant_key, tenant in var.tenants : tenant_key => {
      endpoint = var.api_gateway_type == "aws" ? (
        var.certificate_arn != "" ? "https://api.${var.domain_name}/${tenant_key}" : "${module.aws_api_gateway[0].invoke_url}/${tenant_key}"
      ) : "https://${tenant_key}-api.${var.domain_name}"
      rate_limit = tenant.api_config.rate_limit_per_minute
      quota      = tenant.api_config.quota_per_day
    }
  }
}
