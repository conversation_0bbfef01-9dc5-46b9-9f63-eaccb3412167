# Istio API Gateway Implementation
# Leverages existing Istio service mesh for API management

locals {
  name_prefix = "${var.environment}-${var.name}"
  
  common_tags = merge(var.tags, {
    Environment = var.environment
    ManagedBy   = "terraform"
    Module      = "istio-api-gateway"
    Project     = var.name
  })
}

# API Gateway using Istio Gateway
resource "kubernetes_manifest" "api_gateway" {
  count = !var.skip_k8s_connection ? 1 : 0

  manifest = {
    apiVersion = "networking.istio.io/v1beta1"
    kind       = "Gateway"
    metadata = {
      name      = "${local.name_prefix}-api-gateway"
      namespace = var.istio_namespace
    }
    spec = {
      selector = {
        istio = "ingressgateway"
      }
      servers = [
        {
          port = {
            number   = 80
            name     = "http"
            protocol = "HTTP"
          }
          hosts = ["api.${var.domain_name}"]
          tls = {
            httpsRedirect = true
          }
        },
        {
          port = {
            number   = 443
            name     = "https"
            protocol = "HTTPS"
          }
          hosts = ["api.${var.domain_name}"]
          tls = {
            mode = "SIMPLE"
          }
        }
      ]
    }
  }
}

# Virtual Service for API routing with versioning
resource "kubernetes_manifest" "api_virtual_service" {
  count = !var.skip_k8s_connection ? 1 : 0

  manifest = {
    apiVersion = "networking.istio.io/v1beta1"
    kind       = "VirtualService"
    metadata = {
      name      = "${local.name_prefix}-api-vs"
      namespace = var.istio_namespace
    }
    spec = {
      hosts = ["api.${var.domain_name}"]
      gateways = ["${local.name_prefix}-api-gateway"]
      http = concat([
        # Version-specific routing
        for version in var.api_versions.supported_versions : {
          match = [
            {
              uri = {
                prefix = "/${version}/"
              }
            }
          ]
          rewrite = {
            uri = "/"
          }
          route = [
            {
              destination = {
                host = "api-service-${version}"
                port = {
                  number = 80
                }
              }
            }
          ]
          timeout = "30s"
          retries = {
            attempts      = 3
            perTryTimeout = "10s"
            retryOn       = "gateway-error,connect-failure,refused-stream"
          }
        }
      ], [
        # Default version routing
        {
          route = [
            {
              destination = {
                host = "api-service-${var.api_versions.default_version}"
                port = {
                  number = 80
                }
              }
            }
          ]
          timeout = "30s"
        }
      ])
    }
  }

  depends_on = [kubernetes_manifest.api_gateway]
}

# Tenant-specific virtual services
resource "kubernetes_manifest" "tenant_api_virtual_services" {
  for_each = !var.skip_k8s_connection ? var.tenants : {}

  manifest = {
    apiVersion = "networking.istio.io/v1beta1"
    kind       = "VirtualService"
    metadata = {
      name      = "${each.key}-api-vs"
      namespace = "${each.value.namespace_prefix}-${each.key}"
    }
    spec = {
      hosts = ["${each.key}-api.${var.domain_name}"]
      gateways = ["${var.istio_namespace}/${local.name_prefix}-api-gateway"]
      http = [
        {
          match = [
            {
              headers = {
                "x-tenant-id" = {
                  exact = each.key
                }
              }
            }
          ]
          route = [
            {
              destination = {
                host = "${each.key}-api-service"
                port = {
                  number = 80
                }
              }
            }
          ]
          fault = {
            abort = {
              percentage = {
                value = 0.1
              }
              httpStatus = 503
            }
          }
          timeout = "30s"
        }
      ]
    }
  }

  depends_on = [kubernetes_manifest.api_gateway]
}

# Rate limiting using Envoy Filter
resource "kubernetes_manifest" "rate_limit_filter" {
  count = !var.skip_k8s_connection && var.rate_limiting.enabled ? 1 : 0

  manifest = {
    apiVersion = "networking.istio.io/v1alpha3"
    kind       = "EnvoyFilter"
    metadata = {
      name      = "${local.name_prefix}-rate-limit"
      namespace = var.istio_namespace
    }
    spec = {
      configPatches = [
        {
          applyTo = "HTTP_FILTER"
          match = {
            context = "GATEWAY"
            listener = {
              filterChain = {
                filter = {
                  name = "envoy.filters.network.http_connection_manager"
                }
              }
            }
          }
          patch = {
            operation = "INSERT_BEFORE"
            value = {
              name = "envoy.filters.http.local_ratelimit"
              typed_config = {
                "@type" = "type.googleapis.com/udpa.type.v1.TypedStruct"
                type_url = "type.googleapis.com/envoy.extensions.filters.http.local_ratelimit.v3.LocalRateLimit"
                value = {
                  stat_prefix = "http_local_rate_limiter"
                  token_bucket = {
                    max_tokens     = var.rate_limiting.burst_limit
                    tokens_per_fill = var.rate_limiting.requests_per_minute
                    fill_interval  = "60s"
                  }
                  filter_enabled = {
                    runtime_key = "local_rate_limit_enabled"
                    default_value = {
                      numerator   = 100
                      denominator = "HUNDRED"
                    }
                  }
                  filter_enforced = {
                    runtime_key = "local_rate_limit_enforced"
                    default_value = {
                      numerator   = 100
                      denominator = "HUNDRED"
                    }
                  }
                  response_headers_to_add = [
                    {
                      append = false
                      header = {
                        key   = "x-local-rate-limit"
                        value = "true"
                      }
                    }
                  ]
                }
              }
            }
          }
        }
      ]
    }
  }

  depends_on = [kubernetes_manifest.api_gateway]
}

# CORS policy
resource "kubernetes_manifest" "cors_policy" {
  count = !var.skip_k8s_connection ? 1 : 0

  manifest = {
    apiVersion = "networking.istio.io/v1beta1"
    kind       = "VirtualService"
    metadata = {
      name      = "${local.name_prefix}-cors"
      namespace = var.istio_namespace
    }
    spec = {
      hosts = ["api.${var.domain_name}"]
      gateways = ["${local.name_prefix}-api-gateway"]
      http = [
        {
          match = [
            {
              method = {
                exact = "OPTIONS"
              }
            }
          ]
          route = [
            {
              destination = {
                host = "cors-service"
              }
            }
          ]
          headers = {
            response = {
              add = {
                "Access-Control-Allow-Origin"  = "*"
                "Access-Control-Allow-Methods" = "GET,POST,PUT,DELETE,OPTIONS"
                "Access-Control-Allow-Headers" = "Content-Type,Authorization,X-Tenant-ID"
                "Access-Control-Max-Age"       = "86400"
              }
            }
          }
        }
      ]
    }
  }

  depends_on = [kubernetes_manifest.api_gateway]
}
