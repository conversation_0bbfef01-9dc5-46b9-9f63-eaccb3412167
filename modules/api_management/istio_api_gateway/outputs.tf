output "gateway_name" {
  description = "Name of the Istio Gateway"
  value       = !var.skip_k8s_connection ? kubernetes_manifest.api_gateway[0].manifest.metadata.name : null
}

output "virtual_service_name" {
  description = "Name of the main API Virtual Service"
  value       = !var.skip_k8s_connection ? kubernetes_manifest.api_virtual_service[0].manifest.metadata.name : null
}

output "tenant_virtual_services" {
  description = "Names of tenant-specific virtual services"
  value = {
    for tenant_key, tenant in var.tenants : tenant_key => (
      !var.skip_k8s_connection ? kubernetes_manifest.tenant_api_virtual_services[tenant_key].manifest.metadata.name : null
    )
  }
}

output "rate_limiting_enabled" {
  description = "Whether rate limiting is enabled"
  value       = var.rate_limiting.enabled
}

output "api_endpoint" {
  description = "API endpoint URL"
  value       = "https://api.${var.domain_name}"
}

output "tenant_endpoints" {
  description = "Tenant-specific API endpoints"
  value = {
    for tenant_key, tenant in var.tenants : tenant_key => "https://${tenant_key}-api.${var.domain_name}"
  }
}

output "cors_enabled" {
  description = "Whether CORS is enabled"
  value       = true
}

output "supported_versions" {
  description = "Supported API versions"
  value       = var.api_versions.supported_versions
}

output "default_version" {
  description = "Default API version"
  value       = var.api_versions.default_version
}
