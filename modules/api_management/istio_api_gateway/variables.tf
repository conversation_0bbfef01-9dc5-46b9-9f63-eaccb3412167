variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "name" {
  description = "Project name"
  type        = string
}

variable "tags" {
  description = "Map of tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "domain_name" {
  description = "Domain name for API endpoints"
  type        = string
}

variable "istio_namespace" {
  description = "Istio system namespace"
  type        = string
  default     = "istio-system"
}

variable "skip_k8s_connection" {
  description = "Skip Kubernetes connection for initial setup"
  type        = bool
  default     = false
}

variable "tenants" {
  description = "Map of tenant configurations"
  type = map(object({
    id               = string
    namespace_prefix = string
    api_config = optional(object({
      rate_limit_per_minute = optional(number, 1000)
      burst_limit          = optional(number, 2000)
      quota_per_day        = optional(number, 100000)
      enable_caching       = optional(bool, true)
      cache_ttl_seconds    = optional(number, 300)
      enable_compression   = optional(bool, true)
      cors_enabled         = optional(bool, true)
      cors_origins         = optional(list(string), ["*"])
    }), {})
  }))
  default = {}
}

variable "rate_limiting" {
  description = "Rate limiting configuration"
  type = object({
    enabled               = bool
    requests_per_minute   = number
    burst_limit          = number
    per_tenant_override  = bool
  })
  default = {
    enabled               = true
    requests_per_minute   = 1000
    burst_limit          = 2000
    per_tenant_override  = true
  }
}

variable "api_versions" {
  description = "API versioning configuration"
  type = object({
    strategy           = string
    default_version    = string
    supported_versions = list(string)
    deprecation_policy = object({
      notice_period_days = number
      sunset_period_days = number
    })
  })
  default = {
    strategy           = "path"
    default_version    = "v1"
    supported_versions = ["v1", "v2"]
    deprecation_policy = {
      notice_period_days = 90
      sunset_period_days = 180
    }
  }
}
