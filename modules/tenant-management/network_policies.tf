# Network policies for tenant isolation

# Create default deny all ingress/egress network policy
resource "kubectl_manifest" "tenant_default_deny" {
  for_each = var.tenant_network_policies ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny
  namespace: ${each.key}
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
YAML
}

# Allow ingress from Istio gateway
resource "kubectl_manifest" "tenant_allow_istio_ingress" {
  for_each = var.tenant_network_policies ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-istio-ingress
  namespace: ${each.key}
spec:
  podSelector:
    matchLabels:
      app: ${each.key}-backend
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          istio-injection: enabled
      podSelector:
        matchLabels:
          app: istio-ingressgateway
YAML

  depends_on = [kubectl_manifest.tenant_default_deny]
}

# Allow egress to RDS
resource "kubectl_manifest" "tenant_allow_rds_egress" {
  for_each = var.tenant_network_policies ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-rds-egress
  namespace: ${each.key}
spec:
  podSelector:
    matchLabels:
      app: ${each.key}-backend
  policyTypes:
  - Egress
  egress:
  - to:
    - ipBlock:
        cidr: 10.0.0.0/8
    ports:
    - protocol: TCP
      port: 3306
YAML

  depends_on = [kubectl_manifest.tenant_default_deny]
}

# Allow egress to S3
resource "kubectl_manifest" "tenant_allow_s3_egress" {
  for_each = var.tenant_network_policies ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-s3-egress
  namespace: ${each.key}
spec:
  podSelector:
    matchLabels:
      app: ${each.key}-backend
  policyTypes:
  - Egress
  egress:
  - to:
    - ipBlock:
        cidr: 0.0.0.0/0
    ports:
    - protocol: TCP
      port: 443
YAML

  depends_on = [kubectl_manifest.tenant_default_deny]
}

# Allow communication between frontend and backend
resource "kubectl_manifest" "tenant_allow_frontend_backend" {
  for_each = var.tenant_network_policies ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-frontend-backend
  namespace: ${each.key}
spec:
  podSelector:
    matchLabels:
      app: ${each.key}-backend
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: ${each.key}-frontend
YAML

  depends_on = [kubectl_manifest.tenant_default_deny]
}

# Allow DNS resolution
resource "kubectl_manifest" "tenant_allow_dns" {
  for_each = var.tenant_network_policies ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-dns
  namespace: ${each.key}
spec:
  podSelector: {}
  policyTypes:
  - Egress
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: kube-system
      podSelector:
        matchLabels:
          k8s-app: kube-dns
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
YAML

  depends_on = [kubectl_manifest.tenant_default_deny]
}
