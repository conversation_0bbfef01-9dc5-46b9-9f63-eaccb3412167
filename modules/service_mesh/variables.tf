variable "namespace" {
  description = "Namespace for Istio components"
  type        = string
  default     = "istio-system"
}

variable "create_namespace" {
  description = "Whether to create the namespace"
  type        = bool
  default     = true
}

variable "namespace_labels" {
  description = "Labels to apply to the namespace"
  type        = map(string)
  default     = {}
}

variable "istio_version" {
  description = "Version of Istio to deploy"
  type        = string
  default     = "1.20.0"
}

variable "istio_hub" {
  description = "Hub for Istio images"
  type        = string
  default     = "docker.io/istio"
}

variable "istio_tag" {
  description = "Tag for Istio images"
  type        = string
  default     = "1.16.1"
}

variable "pilot_resources" {
  description = "Resource requests and limits for Istio Pilot"
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  default = {
    requests = {
      cpu    = "100m"
      memory = "128Mi"
    }
    limits = {
      cpu    = "500m"
      memory = "512Mi"
    }
  }
}

variable "proxy_resources" {
  description = "Resource requests and limits for Istio Proxy (sidecar)"
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  default = {
    requests = {
      cpu    = "10m"
      memory = "64Mi"
    }
    limits = {
      cpu    = "100m"
      memory = "128Mi"
    }
  }
}

variable "deploy_ingress_gateway" {
  description = "Whether to deploy Istio ingress gateway"
  type        = bool
  default     = true
}

variable "ingress_gateway_service_type" {
  description = "Service type for Istio ingress gateway"
  type        = string
  default     = "LoadBalancer"
}

variable "ingress_gateway_internal" {
  description = "Whether the ingress gateway should be internal"
  type        = bool
  default     = false
}

variable "ingress_gateway_resources" {
  description = "Resource requests and limits for Istio ingress gateway"
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  default = {
    requests = {
      cpu    = "100m"
      memory = "128Mi"
    }
    limits = {
      cpu    = "500m"
      memory = "512Mi"
    }
  }
}

variable "deploy_egress_gateway" {
  description = "Whether to deploy Istio egress gateway"
  type        = bool
  default     = false
}

variable "egress_gateway_resources" {
  description = "Resource requests and limits for Istio egress gateway"
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  default = {
    requests = {
      cpu    = "100m"
      memory = "128Mi"
    }
    limits = {
      cpu    = "500m"
      memory = "512Mi"
    }
  }
}

variable "enable_kiali" {
  description = "Whether to deploy Kiali for service mesh visualization"
  type        = bool
  default     = true
}

variable "deploy_kiali" {
  description = "Whether to deploy Kiali for service mesh visualization (alias for enable_kiali)"
  type        = bool
  default     = true
}

variable "kiali_version" {
  description = "Version of Kiali to deploy"
  type        = string
  default     = "1.57.1"
}

variable "kiali_hostname" {
  description = "Hostname for Kiali ingress"
  type        = string
  default     = "kiali.example.com"
}

variable "prometheus_url" {
  description = "URL for Prometheus (required for Kiali)"
  type        = string
  default     = "http://prometheus-server.monitoring.svc.cluster.local:80"
}

variable "grafana_url" {
  description = "URL for Grafana (used by Kiali)"
  type        = string
  default     = "http://prometheus-grafana.monitoring.svc.cluster.local:80"
}

variable "enable_tracing" {
  description = "Whether to enable distributed tracing"
  type        = bool
  default     = true
}

variable "enable_prometheus" {
  description = "Whether to enable Prometheus for metrics collection"
  type        = bool
  default     = true
}

variable "enable_grafana" {
  description = "Whether to enable Grafana for metrics visualization"
  type        = bool
  default     = true
}

variable "trace_sampling" {
  description = "Percentage of traces to sample (0.0 to 100.0)"
  type        = number
  default     = 1.0
}

variable "enable_jaeger" {
  description = "Whether to deploy Jaeger for distributed tracing"
  type        = bool
  default     = true
}

variable "deploy_jaeger" {
  description = "Whether to deploy Jaeger for distributed tracing (alias for enable_jaeger)"
  type        = bool
  default     = true
}

variable "jaeger_version" {
  description = "Version of Jaeger to deploy"
  type        = string
  default     = "0.47.0"
}

variable "jaeger_hostname" {
  description = "Hostname for Jaeger ingress"
  type        = string
  default     = "jaeger.example.com"
}

variable "jaeger_url" {
  description = "URL for Jaeger (used by Kiali)"
  type        = string
  default     = "http://jaeger-query.istio-system.svc.cluster.local:16686"
}

variable "app_namespaces" {
  description = "List of application namespaces to enable Istio injection"
  type        = list(string)
  default     = []
}

variable "namespaces_to_inject" {
  description = "List of namespaces to enable Istio injection"
  type        = list(string)
  default     = []
}

variable "enable_mtls" {
  description = "Whether to enable mutual TLS for all services"
  type        = bool
  default     = true
}

variable "mtls_mode" {
  description = "Mode for mutual TLS (STRICT, PERMISSIVE, DISABLE)"
  type        = string
  default     = "STRICT"
}

variable "create_gateway" {
  description = "Whether to create a default Istio Gateway"
  type        = bool
  default     = true
}

variable "create_default_gateway" {
  description = "Whether to create a default Istio Gateway (alias for create_gateway)"
  type        = bool
  default     = true
}

variable "gateway_name" {
  description = "Name of the default Istio Gateway"
  type        = string
  default     = "default-gateway"
}

variable "gateway_hosts" {
  description = "List of hosts for the default Istio Gateway"
  type        = list(string)
  default     = ["*"]
}

variable "default_gateway_namespace" {
  description = "Namespace for the default Istio Gateway"
  type        = string
  default     = "istio-system"
}

variable "default_gateway_tls_secret" {
  description = "Name of the TLS secret for the default Istio Gateway"
  type        = string
  default     = "istio-ingressgateway-certs"
}

variable "create_grafana_dashboard" {
  description = "Whether to create Grafana dashboards for Istio"
  type        = bool
  default     = true
}

variable "grafana_namespace" {
  description = "Namespace where Grafana is deployed"
  type        = string
  default     = "monitoring"
}

variable "skip_k8s_connection" {
  description = "Skip Kubernetes connection in CI/CD environments"
  type        = bool
  default     = false
}

variable "skip_existing_resources" {
  description = "Whether to skip creation of resources that might already exist"
  type        = bool
  default     = false
}

variable "enable_istio_mtls" {
  description = "Whether to enable mutual TLS for Istio services"
  type        = bool
  default     = true
}

variable "istio_mtls_mode" {
  description = "Mode for Istio mutual TLS (STRICT, PERMISSIVE)"
  type        = string
  default     = "STRICT"
}

variable "canary_deployments" {
  description = "Map of canary deployments to configure"
  type = map(object({
    namespace                             = string
    app_name                              = string
    primary_weight                        = number
    canary_weight                         = number
    host_name                             = optional(string)
    gateway_name                          = optional(string)
    enable_fault_injection                = optional(bool)
    fault_delay_percentage                = optional(number)
    fault_delay_duration                  = optional(string)
    fault_abort_percentage                = optional(number)
    fault_abort_http_status               = optional(number)
    enable_retry                          = optional(bool)
    retry_attempts                        = optional(number)
    retry_per_try_timeout                 = optional(string)
    retry_on                              = optional(string)
    enable_timeout                        = optional(bool)
    timeout                               = optional(string)
    enable_circuit_breaking               = optional(bool)
    circuit_breaking_consecutive_errors   = optional(number)
    circuit_breaking_interval             = optional(string)
    circuit_breaking_base_ejection_time   = optional(string)
    circuit_breaking_max_ejection_percent = optional(number)
  }))
  default = {}
}

variable "destination_rules" {
  description = "Map of destination rules to configure"
  type = map(object({
    namespace                   = string
    app_name                    = string
    primary_version             = string
    canary_version              = string
    load_balancer_type          = optional(string)
    canary_load_balancer_type   = optional(string)
    max_connections             = optional(number)
    connect_timeout             = optional(string)
    http1_max_pending_requests  = optional(number)
    http2_max_requests          = optional(number)
    max_requests_per_connection = optional(number)
    max_retries                 = optional(number)
    consecutive_errors          = optional(number)
    interval                    = optional(string)
    base_ejection_time          = optional(string)
    max_ejection_percent        = optional(number)
    enable_tls                  = optional(bool)
    tls_mode                    = optional(string)
  }))
  default = {}
}

variable "gateways" {
  description = "Map of gateways to configure"
  type = map(object({
    namespace              = string
    hosts                  = list(string)
    enable_http            = optional(bool)
    enable_https           = optional(bool)
    redirect_http_to_https = optional(bool)
    tls_mode               = optional(string)
    credential_name        = optional(string)
  }))
  default = {}
}

variable "service_entries" {
  description = "Map of service entries to configure"
  type = map(object({
    namespace = string
    hosts     = list(string)
    ports = list(object({
      number   = number
      name     = string
      protocol = string
    }))
    location   = string
    resolution = string
    endpoints = optional(list(object({
      address = string
      ports   = optional(map(number))
    })))
  }))
  default = {}
}

variable "authorization_policies" {
  description = "Map of authorization policies to configure"
  type = map(object({
    namespace = string
    app_name  = string
    action    = optional(string)
    rules = list(object({
      from = list(object({
        principals = optional(list(string))
        namespaces = optional(list(string))
        ip_blocks  = optional(list(string))
      }))
      to = list(object({
        hosts   = optional(list(string))
        methods = optional(list(string))
        paths   = optional(list(string))
      }))
      when = optional(list(object({
        key    = string
        values = list(string)
      })))
    }))
  }))
  default = {}
}

variable "tenant_authorization_policies" {
  description = "Map of tenant-specific authorization policies to configure"
  type = map(object({
    namespace          = string
    tenant_id          = string
    app_name           = string
    action             = optional(string)
    allowed_namespaces = optional(list(string))
    rules = optional(list(object({
      from = list(object({
        principals = optional(list(string))
        namespaces = optional(list(string))
        ip_blocks  = optional(list(string))
      }))
      to = list(object({
        hosts   = optional(list(string))
        methods = optional(list(string))
        paths   = optional(list(string))
      }))
      when = optional(list(object({
        key    = string
        values = list(string)
      })))
    })))
  }))
  default = {}
}

variable "tenant_virtual_services" {
  description = "Map of tenant-specific virtual services to configure"
  type = map(object({
    namespace  = string
    tenant_id  = string
    host       = string
    gateway    = optional(string)
    uri_prefix = optional(string)
    port       = optional(number)
    timeout    = optional(string)
    retries = optional(object({
      attempts        = number
      per_try_timeout = string
      retry_on        = string
    }))
    fault_injection = optional(object({
      delay = optional(object({
        percentage  = number
        fixed_delay = string
      }))
      abort = optional(object({
        percentage  = number
        http_status = number
      }))
    }))
    api_routes = optional(list(object({
      match_type = string
      uri        = string
      headers = optional(map(object({
        match_type = string
        value      = string
      })))
      destination = object({
        host   = string
        port   = number
        subset = optional(string)
        weight = optional(number)
      })
      timeout = optional(string)
      retries = optional(object({
        attempts        = number
        per_try_timeout = string
        retry_on        = string
      }))
    })))
  }))
  default = {}
}

variable "tenant_destination_rules" {
  description = "Map of tenant-specific destination rules to configure"
  type = map(object({
    namespace                   = string
    tenant_id                   = string
    max_connections             = optional(number)
    connect_timeout             = optional(string)
    http1_max_pending_requests  = optional(number)
    http2_max_requests          = optional(number)
    max_requests_per_connection = optional(number)
    max_retries                 = optional(number)
    consecutive_errors          = optional(number)
    interval                    = optional(string)
    base_ejection_time          = optional(string)
    max_ejection_percent        = optional(number)
    subsets = optional(list(object({
      name   = string
      labels = map(string)
      traffic_policy = optional(object({
        load_balancer = optional(object({
          type = string
        }))
        connection_pool = optional(object({
          tcp = optional(object({
            max_connections = number
            connect_timeout = string
          }))
          http = optional(object({
            http1_max_pending_requests  = number
            http2_max_requests          = number
            max_requests_per_connection = number
            max_retries                 = number
          }))
        }))
        outlier_detection = optional(object({
          consecutive_errors   = number
          interval             = string
          base_ejection_time   = string
          max_ejection_percent = number
        }))
      }))
    })))
  }))
  default = {}
}

variable "tenant_namespaces" {
  description = "List of tenant namespaces to apply Istio policies"
  type        = list(string)
  default     = []
}

variable "monitoring_namespace" {
  description = "Namespace where monitoring tools are deployed"
  type        = string
  default     = "monitoring"
}

variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "name" {
  description = "Name prefix for resources"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "eks_cluster_id" {
  description = "ID of the EKS cluster"
  type        = string
}

variable "eks_cluster_endpoint" {
  description = "Endpoint of the EKS cluster"
  type        = string
}

variable "eks_cluster_certificate_authority_data" {
  description = "Certificate authority data for the EKS cluster"
  type        = string
}

variable "eks_oidc_provider_arn" {
  description = "ARN of the OIDC provider for the EKS cluster"
  type        = string
}

variable "enable_canary" {
  description = "Enable canary deployment configuration"
  type        = bool
  default     = false
}
