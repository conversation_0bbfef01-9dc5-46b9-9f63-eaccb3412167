# Backup vault for each tenant
resource "aws_backup_vault" "tenant" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-backup-vault"
  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-backup-vault"
    Tenant      = each.key
    Environment = var.environment
  })

  lifecycle {
    ignore_changes = [name]
  }
}

# Backup plan for each tenant
resource "aws_backup_plan" "tenant" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-backup-plan"

  rule {
    rule_name         = "daily_backups"
    target_vault_name = aws_backup_vault.tenant[each.key].name
    schedule          = "cron(0 5 ? * * *)" # Daily at 5 AM UTC

    lifecycle {
      delete_after = each.value.backup_retention_days
    }
  }

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-backup-plan"
    Tenant      = each.key
    Environment = var.environment
  })
}

# Backup selection for each tenant
resource "aws_backup_selection" "tenant" {
  for_each = var.tenants

  name         = "${var.environment}-tenant-${each.key}-backup-selection"
  iam_role_arn = aws_iam_role.backup_role[each.key].arn
  plan_id      = aws_backup_plan.tenant[each.key].id

  resources = [
    each.value.rds_arn,
    each.value.efs_arn
  ]
}

# IAM role for backup operations
resource "aws_iam_role" "backup_role" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-backup-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "backup.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-backup-role"
    Tenant      = each.key
    Environment = var.environment
  })
}

# IAM policy for backup operations
resource "aws_iam_role_policy" "backup_policy" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-backup-policy"
  role = aws_iam_role.backup_role[each.key].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "backup:StartBackupJob",
          "backup:StopBackupJob",
          "backup:PutBackupVaultAccessPolicy",
          "backup:GetBackupVaultAccessPolicy",
          "backup:DeleteBackupVaultAccessPolicy",
          "backup:DeleteBackupVault",
          "backup:DeleteBackupPlan",
          "backup:CreateBackupPlan",
          "backup:GetBackupPlan",
          "backup:GetBackupPlanFromJSON",
          "backup:GetBackupPlanFromTemplate",
          "backup:UpdateBackupPlan",
          "backup:DeleteBackupSelection",
          "backup:GetBackupSelection",
          "backup:CreateBackupSelection"
        ]
        Resource = "*"
      }
    ]
  })
}

# CloudWatch alarm for backup failures
resource "aws_cloudwatch_metric_alarm" "backup_failure" {
  for_each = var.tenants

  alarm_name          = "${var.environment}-tenant-${each.key}-backup-failure"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "BackupJobFailure"
  namespace           = "AWS/Backup"
  period              = "300"
  statistic           = "Sum"
  threshold           = "0"
  alarm_description   = "This metric monitors backup job failures for tenant ${each.key}"
  alarm_actions       = [aws_sns_topic.backup_alerts[each.key].arn]

  dimensions = {
    BackupVaultName = aws_backup_vault.tenant[each.key].name
  }
}

# SNS topic for backup alerts
resource "aws_sns_topic" "backup_alerts" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-backup-alerts"

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-backup-alerts"
    Tenant      = each.key
    Environment = var.environment
  })
} 