resource "kubernetes_namespace" "tenant" {
  for_each = var.tenants

  metadata {
    name = each.value.namespace
    labels = {
      tenant = each.key
    }
  }
}

resource "kubernetes_config_map" "tenant_monitoring" {
  for_each = var.tenants

  metadata {
    name      = "tenant-monitoring"
    namespace = each.value.namespace
  }

  data = {
    prometheus_rules = jsonencode({
      groups = [
        {
          name = "tenant-${each.key}"
          rules = [
            {
              alert = "HighCPUUsage"
              expr  = "sum(rate(container_cpu_usage_seconds_total{namespace=\"${each.value.namespace}\"}[5m])) by (pod) > 0.8"
              for   = "5m"
              labels = {
                severity = "warning"
                tenant   = each.key
              }
              annotations = {
                summary     = "High CPU usage detected"
                description = "Pod {{ $labels.pod }} in namespace ${each.value.namespace} has high CPU usage"
              }
            },
            {
              alert = "HighMemoryUsage"
              expr  = "sum(container_memory_usage_bytes{namespace=\"${each.value.namespace}\"}) by (pod) / sum(container_spec_memory_limit_bytes{namespace=\"${each.value.namespace}\"}) by (pod) > 0.8"
              for   = "5m"
              labels = {
                severity = "warning"
                tenant   = each.key
              }
              annotations = {
                summary     = "High memory usage detected"
                description = "Pod {{ $labels.pod }} in namespace ${each.value.namespace} has high memory usage"
              }
            }
          ]
        }
      ]
    })
  }
}

resource "kubernetes_role" "tenant_monitoring" {
  for_each = var.tenants

  metadata {
    name      = "tenant-monitoring"
    namespace = each.value.namespace
  }

  rule {
    api_groups = [""]
    resources  = ["pods", "services", "endpoints"]
    verbs      = ["get", "list", "watch"]
  }

  rule {
    api_groups = ["metrics.k8s.io"]
    resources  = ["pods", "nodes"]
    verbs      = ["get", "list", "watch"]
  }
}

resource "kubernetes_role_binding" "tenant_monitoring" {
  for_each = var.tenants

  metadata {
    name      = "tenant-monitoring"
    namespace = each.value.namespace
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "Role"
    name      = kubernetes_role.tenant_monitoring[each.key].metadata[0].name
  }

  subject {
    kind      = "ServiceAccount"
    name      = "prometheus"
    namespace = "monitoring"
  }
}

resource "kubernetes_network_policy" "tenant_isolation" {
  for_each = var.tenants

  metadata {
    name      = "tenant-isolation"
    namespace = each.value.namespace
  }

  spec {
    pod_selector {
      match_labels = {
        tenant = each.key
      }
    }

    ingress {
      from {
        namespace_selector {
          match_labels = {
            tenant = each.key
          }
        }
      }
    }

    egress {
      to {
        namespace_selector {
          match_labels = {
            tenant = each.key
          }
        }
      }
    }

    policy_types = ["Ingress", "Egress"]
  }
}

resource "aws_iam_policy" "tenant_db_access_policy" {
  for_each = var.tenants

  name        = "tenant-${each.key}-db-access"
  description = "IAM policy for tenant ${each.key} database access"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "rds-db:connect",
          "rds:DescribeDBInstances"
        ]
        Resource = [
          "arn:aws:rds-db:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:dbuser:${var.db_instance_id}/${each.value.db_username}"
        ]
      }
    ]
  })
}

resource "aws_iam_role" "tenant_db_role" {
  for_each = var.tenants

  name = "tenant-${each.key}-db-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "tenant_db_policy_attachment" {
  for_each = var.tenants

  role       = aws_iam_role.tenant_db_role[each.key].name
  policy_arn = aws_iam_policy.tenant_db_access_policy[each.key].arn
}

resource "kubernetes_secret" "tenant_db_credentials" {
  for_each = var.tenants

  metadata {
    name      = "db-credentials"
    namespace = each.value.namespace
  }

  data = {
    username = each.value.db_username
    password = each.value.db_password
  }

  type = "Opaque"
} 