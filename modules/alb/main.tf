# Get AWS managed prefix lists for S3 and DynamoDB
data "aws_prefix_list" "s3" {
  name = "com.amazonaws.*.s3"
}

data "aws_prefix_list" "dynamodb" {
  name = "com.amazonaws.*.dynamodb"
}

# No S3 bucket for ALB logs

# Create S3 bucket for ALB logs
resource "aws_s3_bucket" "alb_logs" {
  bucket = "${var.environment}-alb-logs-${random_string.bucket_suffix.result}"

  tags = merge(var.tags, {
    Name        = "${var.environment}-alb-logs"
    Environment = var.environment
    ManagedBy   = "Terraform"
  })
}

resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# Enable server-side encryption for ALB logs bucket
resource "aws_s3_bucket_server_side_encryption_configuration" "alb_logs" {
  bucket = aws_s3_bucket.alb_logs.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Block public access to ALB logs bucket
resource "aws_s3_bucket_public_access_block" "alb_logs" {
  bucket = aws_s3_bucket.alb_logs.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Add bucket policy to allow ALB to write logs
resource "aws_s3_bucket_policy" "alb_logs" {
  bucket = aws_s3_bucket.alb_logs.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::************:root" # ELB service account for eu-central-1
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.alb_logs.arn}/*"
      },
      {
        Effect = "Allow"
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.alb_logs.arn}/*"
        Condition = {
          StringEquals = {
            "s3:x-amz-acl" = "bucket-owner-full-control"
          }
        }
      },
      {
        Effect = "Allow"
        Principal = {
          Service = "delivery.logs.amazonaws.com"
        }
        Action   = "s3:GetBucketAcl"
        Resource = aws_s3_bucket.alb_logs.arn
      }
    ]
  })
}

# Create the ALB if it doesn't exist
resource "aws_lb" "main" {
  count              = var.alb_exists ? 0 : 1
  name               = "${var.environment}-alb"
  internal           = true # Force internal ALB for security
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = length(var.private_subnet_ids) >= 2 ? [var.private_subnet_ids[0], var.private_subnet_ids[1]] : length(var.public_subnet_ids) >= 2 ? [var.public_subnet_ids[0], var.public_subnet_ids[1]] : var.vpc_subnet_ids

  enable_deletion_protection = false

  # Enable access logs
  access_logs {
    bucket  = aws_s3_bucket.alb_logs.bucket
    prefix  = "${var.environment}-alb"
    enabled = true
  }

  # Enable dropping of invalid headers
  drop_invalid_header_fields = true

  tags = merge(var.tags, {
    Name        = "${var.environment}-alb"
    Environment = var.environment
    ManagedBy   = "Terraform"
  })
}

# Use data source for existing ALB
data "aws_lb" "main" {
  name = "${var.environment}-alb"

  # Skip this data source if the ALB doesn't exist or we're creating it
  count = var.alb_exists ? 1 : 0

  depends_on = [aws_lb.main]
}

resource "aws_security_group" "alb" {
  name        = "${var.environment}-alb-sg"
  description = "Security group for the ALB"
  vpc_id      = var.vpc_id

  # HTTP ingress - use allowed_http_cidr_blocks if provided, otherwise restrict to VPC CIDR
  dynamic "ingress" {
    for_each = length(var.allowed_http_cidr_blocks) > 0 ? [1] : []
    content {
      from_port   = 80
      to_port     = 80
      protocol    = "tcp"
      cidr_blocks = var.allowed_http_cidr_blocks
      description = "Allow HTTP traffic from specified CIDR blocks"
    }
  }

  # If no HTTP CIDR blocks specified, default to VPC CIDR
  dynamic "ingress" {
    for_each = length(var.allowed_http_cidr_blocks) == 0 ? [1] : []
    content {
      from_port   = 80
      to_port     = 80
      protocol    = "tcp"
      cidr_blocks = [var.vpc_cidr]
      description = "Allow HTTP traffic from VPC CIDR"
    }
  }

  # HTTPS ingress - use allowed_https_cidr_blocks if provided, otherwise restrict to VPC CIDR
  dynamic "ingress" {
    for_each = length(var.allowed_https_cidr_blocks) > 0 ? [1] : []
    content {
      from_port   = 443
      to_port     = 443
      protocol    = "tcp"
      cidr_blocks = var.allowed_https_cidr_blocks
      description = "Allow HTTPS traffic from specified CIDR blocks"
    }
  }

  # If no HTTPS CIDR blocks specified, default to VPC CIDR
  dynamic "ingress" {
    for_each = length(var.allowed_https_cidr_blocks) == 0 ? [1] : []
    content {
      from_port   = 443
      to_port     = 443
      protocol    = "tcp"
      cidr_blocks = [var.vpc_cidr]
      description = "Allow HTTPS traffic from VPC CIDR"
    }
  }

  # Restrict egress traffic to specific destinations

  # Allow HTTP/HTTPS egress to EKS cluster
  egress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "Allow HTTP outbound traffic to EKS cluster"
  }

  egress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "Allow HTTPS outbound traffic to EKS cluster"
  }

  # Allow HTTPS egress for S3
  egress {
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    prefix_list_ids = [data.aws_prefix_list.s3.id]
    description     = "Allow HTTPS outbound traffic for S3"
  }

  # Allow HTTPS egress for DynamoDB
  egress {
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    prefix_list_ids = [data.aws_prefix_list.dynamodb.id]
    description     = "Allow HTTPS outbound traffic for DynamoDB"
  }

  tags = merge(var.tags, {
    Name        = "${var.environment}-alb-sg"
    Environment = var.environment
    ManagedBy   = "Terraform"
  })
}

# Create HTTP listener for the ALB
resource "aws_lb_listener" "http" {
  count             = var.alb_exists ? 0 : 1
  load_balancer_arn = var.alb_exists ? data.aws_lb.main[0].arn : aws_lb.main[0].arn
  port              = 80
  protocol          = "HTTP"

  default_action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }
}

# Create HTTPS listener for the ALB
resource "aws_lb_listener" "https" {
  count             = var.alb_exists ? 0 : 1
  load_balancer_arn = var.alb_exists ? data.aws_lb.main[0].arn : aws_lb.main[0].arn
  port              = 443
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-FS-1-2-Res-2020-10"
  certificate_arn   = var.certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.default.arn
  }

  # Enable request tracing
  tags = merge(var.tags, {
    "elbv2.k8s.aws/tracing" = "enabled"
  })
}

# Use data source for existing HTTP listener
data "aws_lb_listener" "http" {
  count             = var.alb_exists ? 1 : 0
  load_balancer_arn = var.alb_exists ? data.aws_lb.main[0].arn : ""
  port              = 80
}

# We'll use the existing HTTP listener and modify it if needed
resource "aws_lb_listener_rule" "http_redirect" {
  count        = var.alb_exists ? 1 : 0
  listener_arn = var.alb_exists ? data.aws_lb_listener.http[0].arn : ""
  priority     = 1

  action {
    type = "redirect"
    redirect {
      port        = "443"
      protocol    = "HTTPS"
      status_code = "HTTP_301"
    }
  }

  condition {
    host_header {
      values = ["*.${var.domain_name}"]
    }
  }
}

# Create a default target group for the ALB
resource "aws_lb_target_group" "default" {
  name        = "${var.environment}-default-tg"
  port        = 80
  protocol    = "HTTP"
  vpc_id      = var.vpc_id
  target_type = "ip"

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 30
    matcher             = "200"
    path                = "/health"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 5
    unhealthy_threshold = 2
  }

  tags = merge(var.tags, {
    Name        = "${var.environment}-default-tg"
    Environment = var.environment
    ManagedBy   = "Terraform"
  })
}

# Create a Route53 record for the ALB
resource "aws_route53_record" "alb" {
  zone_id = var.route53_zone_id
  name    = "${var.environment}-alb.${var.domain_name}"
  type    = "A"

  alias {
    name                   = var.alb_exists ? data.aws_lb.main[0].dns_name : aws_lb.main[0].dns_name
    zone_id                = var.alb_exists ? data.aws_lb.main[0].zone_id : aws_lb.main[0].zone_id
    evaluate_target_health = true
  }
}

# Enhanced WAF rules for ALB
resource "aws_wafv2_web_acl" "alb_waf" {
  name        = "${var.environment}-alb-waf"
  description = "WAF rules for ALB with enhanced security"
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  # AWS Managed Rules for OWASP Top 10
  rule {
    name     = "AWSManagedRulesOWASP"
    priority = 1

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesOWASP"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AWSManagedRulesOWASP"
      sampled_requests_enabled   = true
    }
  }

  # Rate limiting rule
  rule {
    name     = "RateLimit"
    priority = 2

    action {
      block {}
    }

    statement {
      rate_based_statement {
        limit              = 2000
        aggregate_key_type = "IP"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimit"
      sampled_requests_enabled   = true
    }
  }

  # Geographic restrictions
  rule {
    name     = "GeoRestriction"
    priority = 3

    action {
      block {}
    }

    statement {
      geo_match_statement {
        country_codes = ["RU", "CN", "IR", "KP"]
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "GeoRestriction"
      sampled_requests_enabled   = true
    }
  }

  # IP reputation filtering
  rule {
    name     = "IPReputation"
    priority = 4

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "IPReputation"
      sampled_requests_enabled   = true
    }
  }

  # SQL Injection protection
  rule {
    name     = "SQLInjection"
    priority = 5

    action {
      block {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesSQLiRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "SQLInjection"
      sampled_requests_enabled   = true
    }
  }

  # XSS protection
  rule {
    name     = "XSS"
    priority = 6

    action {
      block {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesXSSRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "XSS"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "${var.environment}-alb-waf"
    sampled_requests_enabled   = true
  }

  tags = merge(var.tags, {
    Name    = "${var.environment}-alb-waf",
    Purpose = "Web Application Firewall"
  })
}

# Enable WAF logging
resource "aws_wafv2_web_acl_logging_configuration" "alb_waf_logging" {
  log_destination_configs = [aws_cloudwatch_log_group.waf_logs.arn]
  resource_arn            = aws_wafv2_web_acl.alb_waf.arn

  logging_filter {
    default_behavior = "KEEP"

    filter {
      behavior = "KEEP"
      condition {
        action_condition {
          action = "BLOCK"
        }
      }
      requirement = "MEETS_ANY"
    }
  }
}

# CloudWatch Log Group for WAF logs
resource "aws_cloudwatch_log_group" "waf_logs" {
  name              = "/aws/waf/${var.environment}-alb-waf"
  retention_in_days = 90

  tags = merge(var.tags, {
    Name    = "${var.environment}-waf-logs",
    Purpose = "WAF Log Storage"
  })
}

# Associate WAF with ALB
resource "aws_wafv2_web_acl_association" "alb_waf" {
  resource_arn = var.alb_exists ? data.aws_lb.main[0].arn : aws_lb.main[0].arn
  web_acl_arn  = aws_wafv2_web_acl.alb_waf.arn
}
