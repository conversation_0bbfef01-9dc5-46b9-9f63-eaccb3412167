resource "aws_config_configuration_recorder" "main" {
  count    = var.skip_config_creation ? 0 : 1
  name     = "${var.environment}-config-recorder"
  role_arn = aws_iam_service_linked_role.config_service_role[0].arn

  recording_group {
    all_supported                 = true
    include_global_resource_types = true
  }
}

resource "aws_config_delivery_channel" "main" {
  count          = var.skip_config_creation ? 0 : 1
  name           = "${var.environment}-config-delivery-channel"
  s3_bucket_name = aws_s3_bucket.config_bucket.bucket
  s3_key_prefix  = "config"

  snapshot_delivery_properties {
    delivery_frequency = "Six_Hours"
  }

  depends_on = [aws_s3_bucket_policy.config_bucket_policy, null_resource.disable_config_recorder]

  # Add lifecycle to create before destroy to ensure proper ordering
  lifecycle {
    create_before_destroy = true
  }
}

# First, disable the configuration recorder if it exists
resource "null_resource" "disable_config_recorder" {
  count = var.skip_config_creation ? 0 : 1

  # Add triggers to ensure this runs before any deletion attempts
  triggers = {
    always_run = timestamp()
  }

  provisioner "local-exec" {
    command = <<-EOT
      # Disable the configuration recorder first to allow deletion of delivery channel
      echo "Stopping AWS Config recorder ${var.environment}-config-recorder"
      aws configservice stop-configuration-recorder --configuration-recorder-name ${var.environment}-config-recorder || true
      # Wait for the recorder to stop
      echo "Waiting for recorder to stop..."
      sleep 15
    EOT
  }
}

resource "aws_config_configuration_recorder_status" "main" {
  count      = var.skip_config_creation ? 0 : 1
  name       = var.skip_config_creation ? "${var.environment}-config-recorder" : aws_config_configuration_recorder.main[0].name
  is_enabled = true # Enable the configuration recorder
  depends_on = [aws_config_delivery_channel.main, null_resource.disable_config_recorder]
}

# Create a service-linked role for AWS Config
resource "aws_iam_service_linked_role" "config_service_role" {
  count            = var.skip_config_creation ? 0 : 1
  aws_service_name = "config.amazonaws.com"
  description      = "Service-linked role for AWS Config"
}

# Keep the existing role for backward compatibility
resource "aws_iam_role" "config_role" {
  name = "${var.environment}-config-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "config.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "config_policy_attachment" {
  role       = aws_iam_role.config_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWS_ConfigRole"
}

resource "aws_s3_bucket" "config_bucket" {
  bucket        = "${var.environment}-aws-config-${data.aws_caller_identity.current.account_id}"
  force_destroy = true # Enable force destroy for easier cleanup during development

  tags = var.tags
}

resource "aws_s3_bucket_versioning" "config_bucket_versioning" {
  bucket = aws_s3_bucket.config_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

# Add logging to the bucket itself
resource "aws_s3_bucket_logging" "config_bucket_logging" {
  bucket = aws_s3_bucket.config_bucket.id

  target_bucket = aws_s3_bucket.config_bucket.id
  target_prefix = "log/"
}

# Create KMS key for S3 encryption
resource "aws_kms_key" "config_s3_key" {
  description             = "KMS key for AWS Config bucket encryption"
  deletion_window_in_days = 7
  enable_key_rotation     = true

  tags = merge(var.tags, {
    Name = "${var.environment}-config-s3-key"
  })
}

# Create KMS alias for S3 encryption key
resource "aws_kms_alias" "config_s3_key_alias" {
  name          = "alias/${var.environment}-config-s3-key"
  target_key_id = aws_kms_key.config_s3_key.key_id
}

resource "aws_s3_bucket_server_side_encryption_configuration" "config_bucket_encryption" {
  bucket = aws_s3_bucket.config_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.config_s3_key.arn
      sse_algorithm     = "aws:kms"
    }
    bucket_key_enabled = true
  }
}

resource "aws_s3_bucket_public_access_block" "config_bucket_public_access" {
  bucket = aws_s3_bucket.config_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_policy" "config_bucket_policy" {
  bucket = aws_s3_bucket.config_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AWSConfigBucketPermissionsCheck"
        Effect = "Allow"
        Principal = {
          Service = "config.amazonaws.com"
        }
        Action   = "s3:GetBucketAcl"
        Resource = aws_s3_bucket.config_bucket.arn
      },
      {
        Sid    = "AWSConfigBucketDelivery"
        Effect = "Allow"
        Principal = {
          Service = "config.amazonaws.com"
        }
        Action   = "s3:PutObject"
        Resource = "${aws_s3_bucket.config_bucket.arn}/config/AWSLogs/${data.aws_caller_identity.current.account_id}/Config/*"
        Condition = {
          StringEquals = {
            "s3:x-amz-acl" = "bucket-owner-full-control"
          }
        }
      },
      {
        Sid       = "RequireSSL"
        Effect    = "Deny"
        Principal = "*"
        Action    = "s3:*"
        Resource = [
          aws_s3_bucket.config_bucket.arn,
          "${aws_s3_bucket.config_bucket.arn}/*"
        ]
        Condition = {
          Bool = {
            "aws:SecureTransport" = "false"
          }
        }
      }
    ]
  })
}

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Use the KMS key ARN passed as a variable
locals {
  security_key_arn = var.kms_key_arn
}