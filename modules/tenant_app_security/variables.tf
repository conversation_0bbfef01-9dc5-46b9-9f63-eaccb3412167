variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "tenants" {
  description = "Map of tenant configurations"
  type = map(object({
    rate_limit         = number
    security_threshold = number
  }))
}

variable "tags" {
  description = "Map of tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "waf_log_group_arns" {
  description = "ARNs of the WAF CloudWatch Log Groups for each tenant"
  type        = map(string)
  default     = {}
} 