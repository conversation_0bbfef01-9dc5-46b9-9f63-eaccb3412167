# WAF rules for each tenant
resource "aws_wafv2_web_acl" "tenant" {
  for_each = var.tenants

  name        = "${var.environment}-tenant-${each.key}-waf-v2"
  description = "WAF rules for tenant ${each.key}"
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  rule {
    name     = "RateLimit"
    priority = 1

    override_action {
      none {}
    }

    statement {
      rate_based_statement {
        limit              = each.value.rate_limit
        aggregate_key_type = "IP"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "RateLimit"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "OWASP"
    priority = 2

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesOWASP"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "OWASP"
      sampled_requests_enabled   = true
    }
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "WAFMetrics"
    sampled_requests_enabled   = true
  }

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-waf-v2"
    Tenant      = each.key
    Environment = var.environment
  })
  depends_on = [var.waf_log_group_arns]
}

resource "aws_wafv2_web_acl_logging_configuration" "tenant" {
  for_each = var.tenants
  log_destination_configs = [var.waf_log_group_arns[each.key]]
  resource_arn            = aws_wafv2_web_acl.tenant[each.key].arn
  logging_filter {
    default_behavior = "KEEP"
    filter {
      behavior = "KEEP"
      condition {
        action_condition {
          action = "BLOCK"
        }
      }
      requirement = "MEETS_ANY"
    }
  }
}

# CloudWatch dashboard for application security metrics
resource "aws_cloudwatch_dashboard" "app_security" {
  for_each = var.tenants

  dashboard_name = "${var.environment}-tenant-${each.key}-app-security-dashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/WAF", "BlockedRequests", "WebACL", aws_wafv2_web_acl.tenant[each.key].name],
            ["AWS/WAF", "AllowedRequests", "WebACL", aws_wafv2_web_acl.tenant[each.key].name]
          ]
          period = 300
          stat   = "Sum"
          region = data.aws_region.current.name
          title  = "WAF Requests"
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/WAF", "RateBasedBlockedRequests", "WebACL", aws_wafv2_web_acl.tenant[each.key].name]
          ]
          period = 300
          stat   = "Sum"
          region = data.aws_region.current.name
          title  = "Rate Limited Requests"
        }
      }
    ]
  })
}

# SNS topic for security alerts
resource "aws_sns_topic" "security_alerts" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-security-alerts"

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-security-alerts"
    Tenant      = each.key
    Environment = var.environment
  })
}

# CloudWatch alarm for security violations
resource "aws_cloudwatch_metric_alarm" "security_violation" {
  for_each = var.tenants

  alarm_name          = "${var.environment}-tenant-${each.key}-security-violation"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "BlockedRequests"
  namespace           = "AWS/WAF"
  period              = "300"
  statistic           = "Sum"
  threshold           = each.value.security_threshold
  alarm_description   = "This metric monitors security violations for tenant ${each.key}"
  alarm_actions       = [aws_sns_topic.security_alerts[each.key].arn]

  dimensions = {
    WebACL = aws_wafv2_web_acl.tenant[each.key].name
  }
}

# Data source for current region
data "aws_region" "current" {} 