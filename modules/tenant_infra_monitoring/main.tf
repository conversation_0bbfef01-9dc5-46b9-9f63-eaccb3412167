# CloudWatch dashboard for infrastructure metrics
resource "aws_cloudwatch_dashboard" "infra" {
  for_each = var.tenants

  dashboard_name = "${var.environment}-tenant-${each.key}-infra-dashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/EC2", "CPUUtilization", "InstanceId", each.value.ec2_instance_id],
            ["AWS/EC2", "NetworkIn", "InstanceId", each.value.ec2_instance_id],
            ["AWS/EC2", "NetworkOut", "InstanceId", each.value.ec2_instance_id]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "EC2 Metrics"
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/RDS", "CPUUtilization", "DBInstanceIdentifier", each.value.rds_instance_id],
            ["AWS/RDS", "FreeableMemory", "DBInstanceIdentifier", each.value.rds_instance_id],
            ["AWS/RDS", "DatabaseConnections", "DBInstanceIdentifier", each.value.rds_instance_id]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "RDS Metrics"
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/EFS", "BurstCreditBalance", "FileSystemId", each.value.efs_id],
            ["AWS/EFS", "ClientConnections", "FileSystemId", each.value.efs_id]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "EFS Metrics"
        }
      }
    ]
  })
}

# CloudWatch alarms for infrastructure metrics
resource "aws_cloudwatch_metric_alarm" "ec2_cpu" {
  for_each = var.tenants

  alarm_name          = "${var.environment}-tenant-${each.key}-ec2-cpu"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "300"
  statistic           = "Average"
  threshold           = each.value.ec2_cpu_threshold
  alarm_description   = "This metric monitors EC2 CPU utilization for tenant ${each.key}"
  alarm_actions       = [aws_sns_topic.infra_alerts[each.key].arn]

  dimensions = {
    InstanceId = each.value.ec2_instance_id
  }
}

resource "aws_cloudwatch_metric_alarm" "rds_cpu" {
  for_each = var.tenants

  alarm_name          = "${var.environment}-tenant-${each.key}-rds-cpu"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = each.value.rds_cpu_threshold
  alarm_description   = "This metric monitors RDS CPU utilization for tenant ${each.key}"
  alarm_actions       = [aws_sns_topic.infra_alerts[each.key].arn]

  dimensions = {
    DBInstanceIdentifier = each.value.rds_instance_id
  }
}

# SNS topic for infrastructure alerts
resource "aws_sns_topic" "infra_alerts" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-infra-alerts"

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-infra-alerts"
    Tenant      = each.key
    Environment = var.environment
  })
}

# Data source for current region
data "aws_region" "current" {} 