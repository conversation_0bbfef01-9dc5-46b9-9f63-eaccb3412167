variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "tenants" {
  description = "Map of tenant configurations"
  type = map(object({
    ec2_instance_id   = string
    rds_instance_id   = string
    efs_id            = string
    ec2_cpu_threshold = number
    rds_cpu_threshold = number
  }))
}

variable "tags" {
  description = "Map of tags to apply to all resources"
  type        = map(string)
  default     = {}
} 