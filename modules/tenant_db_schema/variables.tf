variable "tenants" {
  description = "Map of tenant configurations"
  type = map(object({
    db_role     = string
    db_password = string
  }))
}

variable "db_name" {
  description = "Name of the database"
  type        = string
}

variable "db_host" {
  description = "Database host address"
  type        = string
}

variable "db_username" {
  description = "Database admin username"
  type        = string
}

variable "db_password" {
  description = "Database admin password"
  type        = string
  sensitive   = true
} 