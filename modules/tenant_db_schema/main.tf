# NOTE: Tenant schema and user creation for MySQL/Aurora is handled via Kubernetes jobs or external scripts.
# The previous PostgreSQL-specific null_resource blocks have been removed as this infra only supports MySQL/Aurora.

# NOTE: All PostgreSQL/psql logic and null_resource local-exec blocks have been removed.
#       Audit logging and triggers should be implemented in MySQL or at the application level, not via Terraform local-exec. 