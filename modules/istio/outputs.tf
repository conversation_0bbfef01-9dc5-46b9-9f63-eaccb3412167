output "istio_namespace" {
  description = "Istio system namespace"
  value       = !var.skip_k8s_connection ? kubernetes_namespace.istio_system[0].metadata[0].name : null
}

output "istio_version" {
  description = "Installed Istio version"
  value       = var.istio_version
}

output "mesh_id" {
  description = "Istio mesh ID"
  value       = var.mesh_id
}

output "ingress_gateway_status" {
  description = "Istio ingress gateway installation status"
  value       = var.enable_ingress_gateway && !var.skip_k8s_connection ? "installed" : "not_installed"
}

output "egress_gateway_status" {
  description = "Istio egress gateway installation status"
  value       = var.enable_egress_gateway && !var.skip_k8s_connection ? "installed" : "not_installed"
}

output "mtls_enabled" {
  description = "Whether mTLS is enabled"
  value       = var.enable_mtls
}

output "mtls_mode" {
  description = "mTLS mode configuration"
  value       = var.mtls_mode
}

output "circuit_breaker_config" {
  description = "Circuit breaker configuration"
  value       = var.circuit_breaker
}
