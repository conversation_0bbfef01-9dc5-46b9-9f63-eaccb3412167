variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "name" {
  description = "Project name"
  type        = string
}

variable "tags" {
  description = "Map of tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "domain_name" {
  description = "Domain name for tenant gateways"
  type        = string
  default     = "example.com"
}

variable "skip_k8s_connection" {
  description = "Skip Kubernetes connection for initial setup"
  type        = bool
  default     = false
}

variable "istio_version" {
  description = "Istio version to install"
  type        = string
  default     = "1.20.1"
}

variable "mesh_id" {
  description = "Mesh ID for Istio"
  type        = string
  default     = "mesh1"
}

variable "cluster_network" {
  description = "Network name for the cluster"
  type        = string
  default     = "network1"
}

variable "trace_sampling" {
  description = "Trace sampling percentage"
  type        = number
  default     = 1.0
}

variable "enable_ingress_gateway" {
  description = "Enable Istio ingress gateway"
  type        = bool
  default     = true
}

variable "enable_egress_gateway" {
  description = "Enable Istio egress gateway"
  type        = bool
  default     = false
}

variable "enable_mtls" {
  description = "Enable mutual TLS"
  type        = bool
  default     = true
}

variable "mtls_mode" {
  description = "mTLS mode (STRICT, PERMISSIVE, DISABLE)"
  type        = string
  default     = "STRICT"
  validation {
    condition     = contains(["STRICT", "PERMISSIVE", "DISABLE"], var.mtls_mode)
    error_message = "mTLS mode must be STRICT, PERMISSIVE, or DISABLE."
  }
}

variable "pilot_resources" {
  description = "Resource requests and limits for Istio pilot"
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  default = {
    requests = {
      cpu    = "500m"
      memory = "2Gi"
    }
    limits = {
      cpu    = "1000m"
      memory = "4Gi"
    }
  }
}

variable "circuit_breaker" {
  description = "Circuit breaker configuration"
  type = object({
    max_connections                = number
    connect_timeout               = string
    http1_max_pending_requests    = number
    http2_max_requests           = number
    max_requests_per_connection  = number
    max_retries                  = number
    consecutive_gateway_errors   = number
    interval                     = string
    base_ejection_time          = string
    max_ejection_percent        = number
    min_health_percent          = number
  })
  default = {
    max_connections                = 100
    connect_timeout               = "30s"
    http1_max_pending_requests    = 100
    http2_max_requests           = 100
    max_requests_per_connection  = 2
    max_retries                  = 3
    consecutive_gateway_errors   = 5
    interval                     = "30s"
    base_ejection_time          = "30s"
    max_ejection_percent        = 50
    min_health_percent          = 50
  }
}

variable "tenants" {
  description = "Map of tenant configurations for service mesh"
  type = map(object({
    id               = string
    namespace_prefix = string
    enable_injection = optional(bool, true)
    traffic_policy = optional(object({
      load_balancer = optional(string, "ROUND_ROBIN")
      connection_pool = optional(object({
        tcp = optional(object({
          max_connections = optional(number, 10)
          connect_timeout = optional(string, "10s")
        }), {})
        http = optional(object({
          http1_max_pending_requests = optional(number, 10)
          http2_max_requests        = optional(number, 100)
          max_requests_per_connection = optional(number, 2)
          max_retries               = optional(number, 3)
        }), {})
      }), {})
      outlier_detection = optional(object({
        consecutive_errors  = optional(number, 5)
        interval           = optional(string, "30s")
        base_ejection_time = optional(string, "30s")
        max_ejection_percent = optional(number, 50)
      }), {})
    }), {})
  }))
  default = {}
}
