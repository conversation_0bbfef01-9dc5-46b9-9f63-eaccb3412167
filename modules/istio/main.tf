# Istio Service Mesh Module
# This module implements Istio service mesh with traffic management, security, and observability

locals {
  name_prefix = "${var.environment}-${var.name}"
  
  common_tags = merge(var.tags, {
    Environment = var.environment
    ManagedBy   = "terraform"
    Module      = "istio"
    Project     = var.name
  })
}

# Istio namespace
resource "kubernetes_namespace" "istio_system" {
  count = !var.skip_k8s_connection ? 1 : 0

  metadata {
    name = "istio-system"
    labels = {
      "istio-injection" = "disabled"
      "name"           = "istio-system"
    }
  }
}

# Istio base components (CRDs and cluster roles)
resource "helm_release" "istio_base" {
  count = !var.skip_k8s_connection ? 1 : 0

  name       = "istio-base"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "base"
  version    = var.istio_version
  namespace  = kubernetes_namespace.istio_system[0].metadata[0].name

  set {
    name  = "global.meshID"
    value = var.mesh_id
  }

  set {
    name  = "global.meshConfig.defaultConfig.proxyStatsMatcher.inclusionRegexps"
    value = ".*outlier_detection.*"
  }

  depends_on = [kubernetes_namespace.istio_system]
}

# Istio control plane (istiod)
resource "helm_release" "istiod" {
  count = !var.skip_k8s_connection ? 1 : 0

  name       = "istiod"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "istiod"
  version    = var.istio_version
  namespace  = kubernetes_namespace.istio_system[0].metadata[0].name

  set {
    name  = "global.meshID"
    value = var.mesh_id
  }

  set {
    name  = "global.network"
    value = var.cluster_network
  }

  set {
    name  = "pilot.traceSampling"
    value = var.trace_sampling
  }

  set {
    name  = "pilot.resources.requests.cpu"
    value = var.pilot_resources.requests.cpu
  }

  set {
    name  = "pilot.resources.requests.memory"
    value = var.pilot_resources.requests.memory
  }

  set {
    name  = "pilot.resources.limits.cpu"
    value = var.pilot_resources.limits.cpu
  }

  set {
    name  = "pilot.resources.limits.memory"
    value = var.pilot_resources.limits.memory
  }

  depends_on = [helm_release.istio_base]
}

# Istio ingress gateway
resource "helm_release" "istio_ingress" {
  count = !var.skip_k8s_connection && var.enable_ingress_gateway ? 1 : 0

  name       = "istio-ingress"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "gateway"
  version    = var.istio_version
  namespace  = kubernetes_namespace.istio_system[0].metadata[0].name

  set {
    name  = "service.type"
    value = "LoadBalancer"
  }

  set {
    name  = "service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-type"
    value = "nlb"
  }

  set {
    name  = "service.annotations.service\\.beta\\.kubernetes\\.io/aws-load-balancer-scheme"
    value = "internet-facing"
  }

  depends_on = [helm_release.istiod]
}

# Istio egress gateway (optional)
resource "helm_release" "istio_egress" {
  count = !var.skip_k8s_connection && var.enable_egress_gateway ? 1 : 0

  name       = "istio-egress"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "gateway"
  version    = var.istio_version
  namespace  = kubernetes_namespace.istio_system[0].metadata[0].name

  set {
    name  = "service.type"
    value = "ClusterIP"
  }

  depends_on = [helm_release.istiod]
}

# Default destination rule for circuit breaker patterns
resource "kubernetes_manifest" "default_destination_rule" {
  count = !var.skip_k8s_connection ? 1 : 0

  manifest = {
    apiVersion = "networking.istio.io/v1beta1"
    kind       = "DestinationRule"
    metadata = {
      name      = "default-circuit-breaker"
      namespace = "istio-system"
    }
    spec = {
      host = "*.local"
      trafficPolicy = {
        connectionPool = {
          tcp = {
            maxConnections = var.circuit_breaker.max_connections
            connectTimeout = var.circuit_breaker.connect_timeout
          }
          http = {
            http1MaxPendingRequests  = var.circuit_breaker.http1_max_pending_requests
            http2MaxRequests         = var.circuit_breaker.http2_max_requests
            maxRequestsPerConnection = var.circuit_breaker.max_requests_per_connection
            maxRetries              = var.circuit_breaker.max_retries
            consecutiveGatewayErrors = var.circuit_breaker.consecutive_gateway_errors
            interval                = var.circuit_breaker.interval
            baseEjectionTime        = var.circuit_breaker.base_ejection_time
            maxEjectionPercent      = var.circuit_breaker.max_ejection_percent
          }
        }
        outlierDetection = {
          consecutiveGatewayErrors = var.circuit_breaker.consecutive_gateway_errors
          interval                = var.circuit_breaker.interval
          baseEjectionTime        = var.circuit_breaker.base_ejection_time
          maxEjectionPercent      = var.circuit_breaker.max_ejection_percent
          minHealthPercent        = var.circuit_breaker.min_health_percent
        }
      }
    }
  }

  depends_on = [helm_release.istiod]
}

# Peer authentication for mTLS
resource "kubernetes_manifest" "default_peer_authentication" {
  count = !var.skip_k8s_connection && var.enable_mtls ? 1 : 0

  manifest = {
    apiVersion = "security.istio.io/v1beta1"
    kind       = "PeerAuthentication"
    metadata = {
      name      = "default"
      namespace = "istio-system"
    }
    spec = {
      mtls = {
        mode = var.mtls_mode
      }
    }
  }

  depends_on = [helm_release.istiod]
}

# Tenant-specific traffic management
resource "kubernetes_namespace" "tenant_namespaces" {
  for_each = !var.skip_k8s_connection ? var.tenants : {}

  metadata {
    name = "${each.value.namespace_prefix}-${each.key}"
    labels = {
      "istio-injection" = each.value.enable_injection ? "enabled" : "disabled"
      "tenant"         = each.key
    }
  }

  depends_on = [helm_release.istiod]
}

# Tenant-specific destination rules for traffic policies
resource "kubernetes_manifest" "tenant_destination_rules" {
  for_each = !var.skip_k8s_connection ? var.tenants : {}

  manifest = {
    apiVersion = "networking.istio.io/v1beta1"
    kind       = "DestinationRule"
    metadata = {
      name      = "${each.key}-destination-rule"
      namespace = kubernetes_namespace.tenant_namespaces[each.key].metadata[0].name
    }
    spec = {
      host = "*.${kubernetes_namespace.tenant_namespaces[each.key].metadata[0].name}.svc.cluster.local"
      trafficPolicy = {
        loadBalancer = {
          simple = each.value.traffic_policy.load_balancer
        }
        connectionPool = {
          tcp = {
            maxConnections = each.value.traffic_policy.connection_pool.tcp.max_connections
            connectTimeout = each.value.traffic_policy.connection_pool.tcp.connect_timeout
          }
          http = {
            http1MaxPendingRequests  = each.value.traffic_policy.connection_pool.http.http1_max_pending_requests
            http2MaxRequests         = each.value.traffic_policy.connection_pool.http.http2_max_requests
            maxRequestsPerConnection = each.value.traffic_policy.connection_pool.http.max_requests_per_connection
            maxRetries              = each.value.traffic_policy.connection_pool.http.max_retries
          }
        }
        outlierDetection = {
          consecutiveGatewayErrors = each.value.traffic_policy.outlier_detection.consecutive_errors
          interval                = each.value.traffic_policy.outlier_detection.interval
          baseEjectionTime        = each.value.traffic_policy.outlier_detection.base_ejection_time
          maxEjectionPercent      = each.value.traffic_policy.outlier_detection.max_ejection_percent
        }
      }
    }
  }

  depends_on = [kubernetes_namespace.tenant_namespaces]
}

# Virtual service for canary deployments and A/B testing
resource "kubernetes_manifest" "tenant_virtual_services" {
  for_each = !var.skip_k8s_connection ? var.tenants : {}

  manifest = {
    apiVersion = "networking.istio.io/v1beta1"
    kind       = "VirtualService"
    metadata = {
      name      = "${each.key}-virtual-service"
      namespace = kubernetes_namespace.tenant_namespaces[each.key].metadata[0].name
    }
    spec = {
      hosts = ["*"]
      http = [
        {
          match = [
            {
              headers = {
                "x-canary" = {
                  exact = "true"
                }
              }
            }
          ]
          route = [
            {
              destination = {
                host   = "app-service"
                subset = "canary"
              }
              weight = 100
            }
          ]
          fault = {
            delay = {
              percentage = {
                value = 0.1
              }
              fixedDelay = "5s"
            }
          }
          retries = {
            attempts      = 3
            perTryTimeout = "2s"
            retryOn       = "gateway-error,connect-failure,refused-stream"
          }
        },
        {
          route = [
            {
              destination = {
                host   = "app-service"
                subset = "stable"
              }
              weight = 90
            },
            {
              destination = {
                host   = "app-service"
                subset = "canary"
              }
              weight = 10
            }
          ]
          timeout = "30s"
        }
      ]
    }
  }

  depends_on = [kubernetes_namespace.tenant_namespaces]
}

# Gateway for tenant traffic
resource "kubernetes_manifest" "tenant_gateways" {
  for_each = !var.skip_k8s_connection ? var.tenants : {}

  manifest = {
    apiVersion = "networking.istio.io/v1beta1"
    kind       = "Gateway"
    metadata = {
      name      = "${each.key}-gateway"
      namespace = kubernetes_namespace.tenant_namespaces[each.key].metadata[0].name
    }
    spec = {
      selector = {
        istio = "ingressgateway"
      }
      servers = [
        {
          port = {
            number   = 80
            name     = "http"
            protocol = "HTTP"
          }
          hosts = ["${each.key}.${var.domain_name}"]
          tls = {
            httpsRedirect = true
          }
        },
        {
          port = {
            number   = 443
            name     = "https"
            protocol = "HTTPS"
          }
          hosts = ["${each.key}.${var.domain_name}"]
          tls = {
            mode = "SIMPLE"
          }
        }
      ]
    }
  }

  depends_on = [helm_release.istio_ingress]
}

# Service monitor for Istio metrics
resource "kubernetes_manifest" "istio_service_monitor" {
  count = !var.skip_k8s_connection ? 1 : 0

  manifest = {
    apiVersion = "monitoring.coreos.com/v1"
    kind       = "ServiceMonitor"
    metadata = {
      name      = "istio-mesh"
      namespace = kubernetes_namespace.istio_system[0].metadata[0].name
      labels = {
        app = "istio-proxy"
      }
    }
    spec = {
      selector = {
        matchLabels = {
          app = "istiod"
        }
      }
      endpoints = [
        {
          port     = "http-monitoring"
          interval = "15s"
          path     = "/stats/prometheus"
        }
      ]
    }
  }

  depends_on = [helm_release.istiod]
}

# Telemetry configuration for distributed tracing
resource "kubernetes_manifest" "telemetry_config" {
  count = !var.skip_k8s_connection ? 1 : 0

  manifest = {
    apiVersion = "telemetry.istio.io/v1alpha1"
    kind       = "Telemetry"
    metadata = {
      name      = "default"
      namespace = kubernetes_namespace.istio_system[0].metadata[0].name
    }
    spec = {
      tracing = [
        {
          providers = [
            {
              name = "jaeger"
            }
          ]
        }
      ]
      metrics = [
        {
          providers = [
            {
              name = "prometheus"
            }
          ]
        }
      ]
      accessLogging = [
        {
          providers = [
            {
              name = "otel"
            }
          ]
        }
      ]
    }
  }

  depends_on = [helm_release.istiod]
}
