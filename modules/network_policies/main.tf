/**
 * # Network Policies Module
 *
 * This module implements network policies for tenant isolation in a multi-tenant Kubernetes environment.
 */

terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
  }
}

# Local variables
locals {
  name_prefix = "${var.environment}-${var.name}"
  tags = merge(
    var.tags,
    {
      "Environment" = var.environment
      "ManagedBy"   = "terraform"
      "Name"        = local.name_prefix
    }
  )
}

# Create default deny all ingress network policy for each tenant namespace
resource "kubernetes_network_policy" "default_deny_ingress" {
  for_each = var.enable_network_policies && !var.skip_k8s_connection ? toset(var.tenant_namespaces) : []

  metadata {
    name      = "default-deny-ingress"
    namespace = each.key
  }

  spec {
    pod_selector {}
    policy_types = ["Ingress"]
  }
}

# Create default deny all egress network policy for each tenant namespace
resource "kubernetes_network_policy" "default_deny_egress" {
  for_each = var.enable_network_policies && var.enable_egress_policies && !var.skip_k8s_connection ? toset(var.tenant_namespaces) : []

  metadata {
    name      = "default-deny-egress"
    namespace = each.key
  }

  spec {
    pod_selector {}
    policy_types = ["Egress"]
  }
}

# Create allow DNS egress network policy for each tenant namespace
resource "kubernetes_network_policy" "allow_dns_egress" {
  for_each = var.enable_network_policies && var.enable_egress_policies && !var.skip_k8s_connection ? toset(var.tenant_namespaces) : []

  metadata {
    name      = "allow-dns-egress"
    namespace = each.key
  }

  spec {
    pod_selector {}
    policy_types = ["Egress"]
    egress {
      ports {
        port     = 53
        protocol = "UDP"
      }
      ports {
        port     = 53
        protocol = "TCP"
      }
      to {
        namespace_selector {
          match_labels = {
            "kubernetes.io/metadata.name" = "kube-system"
          }
        }
        pod_selector {
          match_labels = {
            "k8s-app" = "kube-dns"
          }
        }
      }
    }
  }
}

# Create allow intra-namespace communication network policy for each tenant namespace
resource "kubernetes_network_policy" "allow_intra_namespace" {
  for_each = var.enable_network_policies && !var.skip_k8s_connection ? toset(var.tenant_namespaces) : []

  metadata {
    name      = "allow-intra-namespace"
    namespace = each.key
  }

  spec {
    pod_selector {}
    policy_types = ["Ingress"]
    ingress {
      from {
        pod_selector {}
      }
    }
  }
}

# Create allow monitoring ingress network policy for each tenant namespace
resource "kubernetes_network_policy" "allow_monitoring_ingress" {
  for_each = var.enable_network_policies && var.enable_monitoring && !var.skip_k8s_connection ? toset(var.tenant_namespaces) : []

  metadata {
    name      = "allow-monitoring-ingress"
    namespace = each.key
  }

  spec {
    pod_selector {}
    policy_types = ["Ingress"]
    ingress {
      from {
        namespace_selector {
          match_labels = {
            "kubernetes.io/metadata.name" = var.monitoring_namespace
          }
        }
      }
    }
  }
}

# Create allow istio ingress network policy for each tenant namespace
resource "kubernetes_network_policy" "allow_istio_ingress" {
  for_each = var.enable_network_policies && var.enable_istio && !var.skip_k8s_connection ? toset(var.tenant_namespaces) : []

  metadata {
    name      = "allow-istio-ingress"
    namespace = each.key
  }

  spec {
    pod_selector {}
    policy_types = ["Ingress"]
    ingress {
      from {
        namespace_selector {
          match_labels = {
            "kubernetes.io/metadata.name" = var.istio_namespace
          }
        }
      }
    }
  }
}

# Create allow istio egress network policy for each tenant namespace
resource "kubernetes_network_policy" "allow_istio_egress" {
  for_each = var.enable_network_policies && var.enable_istio && var.enable_egress_policies && !var.skip_k8s_connection ? toset(var.tenant_namespaces) : []

  metadata {
    name      = "allow-istio-egress"
    namespace = each.key
  }

  spec {
    pod_selector {}
    policy_types = ["Egress"]
    egress {
      to {
        namespace_selector {
          match_labels = {
            "kubernetes.io/metadata.name" = var.istio_namespace
          }
        }
      }
    }
  }
}

# Create allow cross-tenant network policy for each tenant namespace
resource "kubernetes_network_policy" "allow_cross_tenant" {
  for_each = var.enable_network_policies && !var.skip_k8s_connection ? {
    for pair in var.cross_tenant_communication : "${pair.source_namespace}-${pair.destination_namespace}" => pair
  } : {}

  metadata {
    name      = "allow-cross-tenant-${each.value.destination_namespace}"
    namespace = each.value.source_namespace
  }

  spec {
    pod_selector {
      match_labels = each.value.source_pod_labels
    }
    policy_types = ["Egress"]
    egress {
      to {
        namespace_selector {
          match_labels = {
            "kubernetes.io/metadata.name" = each.value.destination_namespace
          }
        }
        pod_selector {
          match_labels = each.value.destination_pod_labels
        }
      }
      ports {
        port     = each.value.port
        protocol = each.value.protocol
      }
    }
  }
}

# Create allow external egress network policy for each tenant namespace
resource "kubernetes_network_policy" "allow_external_egress" {
  for_each = var.enable_network_policies && var.enable_egress_policies && !var.skip_k8s_connection ? {
    for pair in var.external_egress_rules : "${pair.namespace}-${pair.name}" => pair
  } : {}

  metadata {
    name      = "allow-external-${each.value.name}"
    namespace = each.value.namespace
  }

  spec {
    pod_selector {
      match_labels = each.value.pod_labels
    }
    policy_types = ["Egress"]
    egress {
      to {
        ip_block {
          cidr   = each.value.cidr
          except = each.value.except_cidrs != null ? each.value.except_cidrs : []
        }
      }
      dynamic "ports" {
        for_each = each.value.ports
        content {
          port     = ports.value.port
          protocol = ports.value.protocol
        }
      }
    }
  }
}

# Create service-specific network policies for each tenant namespace
resource "kubernetes_network_policy" "service_specific" {
  for_each = var.enable_network_policies && !var.skip_k8s_connection ? {
    for policy in var.service_specific_policies : "${policy.namespace}-${policy.name}" => policy
  } : {}

  metadata {
    name      = each.value.name
    namespace = each.value.namespace
    labels = merge(
      {
        "app.kubernetes.io/managed-by" = "terraform"
      },
      each.value.labels != null ? each.value.labels : {}
    )
  }

  spec {
    pod_selector {
      match_labels = each.value.pod_selector_labels
    }
    policy_types = each.value.policy_types

    dynamic "ingress" {
      for_each = contains(each.value.policy_types, "Ingress") ? each.value.ingress_rules : []
      content {
        dynamic "from" {
          for_each = ingress.value.from != null ? ingress.value.from : []
          content {
            dynamic "namespace_selector" {
              for_each = from.value.namespace_selector != null ? [from.value.namespace_selector] : []
              content {
                match_labels = namespace_selector.value.match_labels
              }
            }
            dynamic "pod_selector" {
              for_each = from.value.pod_selector != null ? [from.value.pod_selector] : []
              content {
                match_labels = pod_selector.value.match_labels
              }
            }
            dynamic "ip_block" {
              for_each = from.value.ip_block != null ? [from.value.ip_block] : []
              content {
                cidr   = ip_block.value.cidr
                except = ip_block.value.except
              }
            }
          }
        }
        dynamic "ports" {
          for_each = ingress.value.ports != null ? ingress.value.ports : []
          content {
            port     = ports.value.port
            protocol = ports.value.protocol
          }
        }
      }
    }

    dynamic "egress" {
      for_each = contains(each.value.policy_types, "Egress") ? each.value.egress_rules : []
      content {
        dynamic "to" {
          for_each = egress.value.to != null ? egress.value.to : []
          content {
            dynamic "namespace_selector" {
              for_each = to.value.namespace_selector != null ? [to.value.namespace_selector] : []
              content {
                match_labels = namespace_selector.value.match_labels
              }
            }
            dynamic "pod_selector" {
              for_each = to.value.pod_selector != null ? [to.value.pod_selector] : []
              content {
                match_labels = pod_selector.value.match_labels
              }
            }
            dynamic "ip_block" {
              for_each = to.value.ip_block != null ? [to.value.ip_block] : []
              content {
                cidr   = ip_block.value.cidr
                except = ip_block.value.except
              }
            }
          }
        }
        dynamic "ports" {
          for_each = egress.value.ports != null ? egress.value.ports : []
          content {
            port     = ports.value.port
            protocol = ports.value.protocol
          }
        }
      }
    }
  }
}

# Create tier-specific network policies for each tenant tier
resource "kubernetes_network_policy" "tier_specific" {
  for_each = var.enable_network_policies && !var.skip_k8s_connection ? {
    for policy in var.tier_specific_policies : "${policy.namespace}-${policy.name}" => policy
  } : {}

  metadata {
    name      = each.value.name
    namespace = each.value.namespace
    labels = merge(
      {
        "app.kubernetes.io/managed-by" = "terraform"
        "tenant.architrave.io/tier"    = each.value.tier
      },
      each.value.labels != null ? each.value.labels : {}
    )
  }

  spec {
    pod_selector {
      match_labels = each.value.pod_selector_labels
    }
    policy_types = each.value.policy_types

    dynamic "ingress" {
      for_each = contains(each.value.policy_types, "Ingress") ? each.value.ingress_rules : []
      content {
        dynamic "from" {
          for_each = ingress.value.from != null ? ingress.value.from : []
          content {
            dynamic "namespace_selector" {
              for_each = from.value.namespace_selector != null ? [from.value.namespace_selector] : []
              content {
                match_labels = namespace_selector.value.match_labels
              }
            }
            dynamic "pod_selector" {
              for_each = from.value.pod_selector != null ? [from.value.pod_selector] : []
              content {
                match_labels = pod_selector.value.match_labels
              }
            }
            dynamic "ip_block" {
              for_each = from.value.ip_block != null ? [from.value.ip_block] : []
              content {
                cidr   = ip_block.value.cidr
                except = ip_block.value.except
              }
            }
          }
        }
        dynamic "ports" {
          for_each = ingress.value.ports != null ? ingress.value.ports : []
          content {
            port     = ports.value.port
            protocol = ports.value.protocol
          }
        }
      }
    }

    dynamic "egress" {
      for_each = contains(each.value.policy_types, "Egress") ? each.value.egress_rules : []
      content {
        dynamic "to" {
          for_each = egress.value.to != null ? egress.value.to : []
          content {
            dynamic "namespace_selector" {
              for_each = to.value.namespace_selector != null ? [to.value.namespace_selector] : []
              content {
                match_labels = namespace_selector.value.match_labels
              }
            }
            dynamic "pod_selector" {
              for_each = to.value.pod_selector != null ? [to.value.pod_selector] : []
              content {
                match_labels = pod_selector.value.match_labels
              }
            }
            dynamic "ip_block" {
              for_each = to.value.ip_block != null ? [to.value.ip_block] : []
              content {
                cidr   = ip_block.value.cidr
                except = ip_block.value.except
              }
            }
          }
        }
        dynamic "ports" {
          for_each = egress.value.ports != null ? egress.value.ports : []
          content {
            port     = ports.value.port
            protocol = ports.value.protocol
          }
        }
      }
    }
  }
}
