variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "name" {
  description = "Name prefix for resources"
  type        = string
  default     = "network-policies"
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "enable_network_policies" {
  description = "Whether to enable network policies"
  type        = bool
  default     = true
}

variable "enable_egress_policies" {
  description = "Whether to enable egress network policies"
  type        = bool
  default     = true
}

variable "enable_monitoring" {
  description = "Whether to enable monitoring"
  type        = bool
  default     = true
}

variable "enable_istio" {
  description = "Whether to enable Istio"
  type        = bool
  default     = true
}

variable "monitoring_namespace" {
  description = "Namespace where monitoring tools are deployed"
  type        = string
  default     = "monitoring"
}

variable "istio_namespace" {
  description = "Namespace where Istio is deployed"
  type        = string
  default     = "istio-system"
}

variable "tenant_namespaces" {
  description = "List of tenant namespaces"
  type        = list(string)
  default     = []
}

variable "cross_tenant_communication" {
  description = "List of cross-tenant communication rules"
  type = list(object({
    source_namespace       = string
    destination_namespace  = string
    source_pod_labels      = map(string)
    destination_pod_labels = map(string)
    port                   = number
    protocol               = string
  }))
  default = []
}

variable "external_egress_rules" {
  description = "List of external egress rules"
  type = list(object({
    namespace    = string
    name         = string
    pod_labels   = map(string)
    cidr         = string
    except_cidrs = optional(list(string))
    ports = list(object({
      port     = number
      protocol = string
    }))
  }))
  default = []
}

variable "service_specific_policies" {
  description = "List of service-specific network policies"
  type = list(object({
    namespace           = string
    name                = string
    labels              = optional(map(string))
    pod_selector_labels = map(string)
    policy_types        = list(string)
    ingress_rules = optional(list(object({
      from = optional(list(object({
        namespace_selector = optional(object({
          match_labels = map(string)
        }))
        pod_selector = optional(object({
          match_labels = map(string)
        }))
        ip_block = optional(object({
          cidr   = string
          except = optional(list(string))
        }))
      })))
      ports = optional(list(object({
        port     = number
        protocol = string
      })))
    })))
    egress_rules = optional(list(object({
      to = optional(list(object({
        namespace_selector = optional(object({
          match_labels = map(string)
        }))
        pod_selector = optional(object({
          match_labels = map(string)
        }))
        ip_block = optional(object({
          cidr   = string
          except = optional(list(string))
        }))
      })))
      ports = optional(list(object({
        port     = number
        protocol = string
      })))
    })))
  }))
  default = []
}

variable "tier_specific_policies" {
  description = "List of tier-specific network policies"
  type = list(object({
    namespace           = string
    name                = string
    tier                = string
    labels              = optional(map(string))
    pod_selector_labels = map(string)
    policy_types        = list(string)
    ingress_rules = optional(list(object({
      from = optional(list(object({
        namespace_selector = optional(object({
          match_labels = map(string)
        }))
        pod_selector = optional(object({
          match_labels = map(string)
        }))
        ip_block = optional(object({
          cidr   = string
          except = optional(list(string))
        }))
      })))
      ports = optional(list(object({
        port     = number
        protocol = string
      })))
    })))
    egress_rules = optional(list(object({
      to = optional(list(object({
        namespace_selector = optional(object({
          match_labels = map(string)
        }))
        pod_selector = optional(object({
          match_labels = map(string)
        }))
        ip_block = optional(object({
          cidr   = string
          except = optional(list(string))
        }))
      })))
      ports = optional(list(object({
        port     = number
        protocol = string
      })))
    })))
  }))
  default = []
}

variable "skip_k8s_connection" {
  description = "Skip Kubernetes connection in CI/CD environments"
  type        = bool
  default     = false
}
