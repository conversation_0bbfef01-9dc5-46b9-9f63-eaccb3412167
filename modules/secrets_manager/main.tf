# Enhanced Secrets Manager Module with Secret Rotation

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Generate a random suffix for the secret name
resource "random_id" "secret_suffix" {
  byte_length = 8
}

# Lambda function for RDS secret rotation
data "archive_file" "rotation_lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/rotation_lambda"
  output_path = "${path.module}/rotation_lambda.zip"
}

resource "aws_lambda_function" "rotation_lambda" {
  count         = var.enable_secret_rotation ? 1 : 0
  filename      = data.archive_file.rotation_lambda_zip.output_path
  function_name = "${var.environment}-secret-rotation-lambda"
  role          = aws_iam_role.rotation_lambda_role[0].arn
  handler       = "index.lambda_handler"
  runtime       = "python3.9"
  timeout       = 30

  environment {
    variables = {
      SECRETS_MANAGER_ENDPOINT = "https://secretsmanager.${data.aws_region.current.name}.amazonaws.com"
    }
  }

  # Enable tracing with AWS X-Ray
  tracing_config {
    mode = "Active"
  }

  # Use KMS for environment variable encryption
  # DISABLED: kms_key_arn = null # KMS keys removed to avoid deletion protection

  tags = var.tags
}

# IAM role for the rotation Lambda function
resource "aws_iam_role" "rotation_lambda_role" {
  count = var.enable_secret_rotation ? 1 : 0
  name  = "${var.environment}-secret-rotation-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# IAM policy for the rotation Lambda function
resource "aws_iam_policy" "rotation_lambda_policy" {
  count       = var.enable_secret_rotation ? 1 : 0
  name        = "${var.environment}-secret-rotation-policy"
  description = "Policy for the secret rotation Lambda function"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "secretsmanager:DescribeSecret",
          "secretsmanager:GetSecretValue",
          "secretsmanager:PutSecretValue",
          "secretsmanager:UpdateSecretVersionStage"
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:*"
        ]
      },
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Effect = "Allow"
        Resource = [
          "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.environment}-secret-rotation-lambda:*",
          "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/lambda/${var.environment}-secret-rotation-lambda"
        ]
      },
      {
        Action = [
          "rds:ModifyDBInstance",
          "rds:DescribeDBInstances",
          "rds:DescribeDBClusters",
          "rds:ModifyDBCluster"
        ]
        Effect = "Allow"
        Resource = ["*"]
      }
    ]
  })
}

# Attach the policy to the role
resource "aws_iam_role_policy_attachment" "rotation_lambda_policy_attachment" {
  count      = var.enable_secret_rotation ? 1 : 0
  role       = aws_iam_role.rotation_lambda_role[0].name
  policy_arn = aws_iam_policy.rotation_lambda_policy[0].arn
}

# Add Lambda permission for Secrets Manager to invoke the function
resource "aws_lambda_permission" "secretsmanager_permission" {
  count         = var.enable_secret_rotation ? 1 : 0
  statement_id  = "AllowSecretsManagerToInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.rotation_lambda[0].function_name
  principal     = "secretsmanager.amazonaws.com"
  source_arn    = "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:*"
}

# RDS master credentials secret
resource "aws_secretsmanager_secret" "rds_master" {
  name                           = "${var.environment}/rds/master-${random_id.secret_suffix.hex}"
  recovery_window_in_days        = 0 // Set to 0 for immediate deletion during development
  force_overwrite_replica_secret = true
  kms_key_id                     = var.kms_key_arn

  tags = var.tags
}

# Secret rotation configuration
resource "aws_secretsmanager_secret_rotation" "rds_master_rotation" {
  count               = var.enable_secret_rotation ? 1 : 0
  secret_id           = aws_secretsmanager_secret.rds_master.id
  rotation_lambda_arn = aws_lambda_function.rotation_lambda[0].arn

  depends_on = [aws_lambda_permission.secretsmanager_permission]

  rotation_rules {
    automatically_after_days = var.rotation_days
  }
}

# Tenant credentials secrets
resource "aws_secretsmanager_secret" "tenant_credentials" {
  for_each                       = var.tenant_credentials
  name                           = "${var.environment}/tenant/${each.key}/credentials"
  recovery_window_in_days        = 0 // Set to 0 for immediate deletion during development
  force_overwrite_replica_secret = true
  kms_key_id                     = var.kms_key_arn

  tags = merge(var.tags, {
    Tenant = each.key
  })
}

# Tenant credentials secret rotation
resource "aws_secretsmanager_secret_rotation" "tenant_credentials_rotation" {
  for_each            = var.enable_secret_rotation ? var.tenant_credentials : {}
  secret_id           = aws_secretsmanager_secret.tenant_credentials[each.key].id
  rotation_lambda_arn = aws_lambda_function.rotation_lambda[0].arn

  depends_on = [aws_lambda_permission.secretsmanager_permission]

  rotation_rules {
    automatically_after_days = var.rotation_days
  }
}