# S3 bucket for DR documentation and scripts
resource "aws_s3_bucket" "dr_docs" {
  for_each = var.tenants

  bucket = "${var.environment}-tenant-${each.key}-dr-docs"

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-dr-docs"
    Tenant      = each.key
    Environment = var.environment
  })
}

# S3 bucket policy for DR documentation
resource "aws_s3_bucket_policy" "dr_docs" {
  for_each = var.tenants

  bucket = aws_s3_bucket.dr_docs[each.key].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "AllowDRTeamAccess"
        Effect = "Allow"
        Principal = {
          AWS = each.value.dr_team_role_arn
        }
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = [
          aws_s3_bucket.dr_docs[each.key].arn,
          "${aws_s3_bucket.dr_docs[each.key].arn}/*"
        ]
      }
    ]
  })
}

# CloudWatch event rule for DR testing schedule
resource "aws_cloudwatch_event_rule" "dr_test" {
  for_each = var.tenants

  name                = "${var.environment}-tenant-${each.key}-dr-test"
  description         = "Schedule for DR testing for tenant ${each.key}"
  schedule_expression = each.value.dr_test_schedule
}

# CloudWatch event target for DR testing
resource "aws_cloudwatch_event_target" "dr_test" {
  for_each = var.tenants

  rule      = aws_cloudwatch_event_rule.dr_test[each.key].name
  target_id = "${var.environment}-tenant-${each.key}-dr-test-target"
  arn       = aws_lambda_function.dr_test[each.key].arn
}

# Lambda function for DR testing
resource "aws_lambda_function" "dr_test" {
  for_each = var.tenants

  filename      = "dr_test_script.zip"
  function_name = "${var.environment}-tenant-${each.key}-dr-test"
  role          = aws_iam_role.dr_test[each.key].arn
  handler       = "index.handler"
  runtime       = "nodejs14.x"
  timeout       = 300
  memory_size   = 256

  environment {
    variables = {
      TENANT_ID    = each.key
      ENVIRONMENT  = var.environment
      BACKUP_VAULT = each.value.backup_vault_arn
      RDS_INSTANCE = each.value.rds_instance_arn
    }
  }

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-dr-test"
    Tenant      = each.key
    Environment = var.environment
  })
}

# IAM role for DR testing Lambda
resource "aws_iam_role" "dr_test" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-dr-test-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-dr-test-role"
    Tenant      = each.key
    Environment = var.environment
  })
}

# IAM policy for DR testing Lambda
resource "aws_iam_role_policy" "dr_test" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-dr-test-policy"
  role = aws_iam_role.dr_test[each.key].id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "backup:StartRestoreJob",
          "backup:GetBackupVaultAccessPolicy",
          "rds:RestoreDBInstanceFromDBSnapshot",
          "rds:DescribeDBInstances",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "*"
      }
    ]
  })
}

# SNS topic for DR test results
resource "aws_sns_topic" "dr_test_results" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-dr-test-results"

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-dr-test-results"
    Tenant      = each.key
    Environment = var.environment
  })
} 