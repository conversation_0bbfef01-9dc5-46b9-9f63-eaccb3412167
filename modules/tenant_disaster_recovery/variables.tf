variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "tenants" {
  description = "Map of tenant configurations"
  type = map(object({
    dr_team_role_arn = string
    dr_test_schedule = string
    backup_vault_arn = string
    rds_instance_arn = string
  }))
}

variable "tags" {
  description = "Map of tags to apply to all resources"
  type        = map(string)
  default     = {}
} 