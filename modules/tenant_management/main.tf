/**
 * # Enhanced Tenant Management System Module
 *
 * This module implements a comprehensive tenant management system for multi-tenant Kubernetes environments.
 * It provides complete tenant lifecycle management (create, update, suspend, delete), isolation mechanisms,
 * resource quotas, and advanced monitoring and alerting.
 *
 * Features:
 * - Complete tenant lifecycle management
 * - Tenant isolation with network policies and Istio mTLS
 * - Resource quotas for CPU, memory, storage, and Kubernetes objects
 * - S3 bucket creation and management for tenant assets
 * - Database schema creation and management
 * - IAM role creation for tenant-specific access
 * - Advanced monitoring with Prometheus, Grafana, Loki, and Jaeger
 * - SSL certificate management for tenant domains
 */

# Data sources
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

# Local variables
locals {
  name_prefix = "${var.environment}-${var.name}"
  tags = merge(
    var.tags,
    {
      "Environment" = var.environment
      "ManagedBy"   = "terraform"
      "Name"        = local.name_prefix
    }
  )

  operator_namespace = var.create_operator_namespace && !var.skip_k8s_connection ? kubernetes_namespace.operator[0].metadata[0].name : var.operator_namespace

  # Default resource quotas based on tier
  resource_quotas = {
    small = {
      cpu      = "2"
      memory   = "4Gi"
      storage  = "10Gi"
      pods     = 20
      services = 10
    }
    medium = {
      cpu      = "4"
      memory   = "8Gi"
      storage  = "50Gi"
      pods     = 40
      services = 20
    }
    large = {
      cpu      = "8"
      memory   = "16Gi"
      storage  = "100Gi"
      pods     = 80
      services = 40
    }
  }
}

# Create namespace for the tenant operator
resource "kubernetes_namespace" "operator" {
  count = var.create_operator_namespace && !var.skip_k8s_connection ? 1 : 0

  metadata {
    name = var.operator_namespace
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
      "app.kubernetes.io/name"       = "tenant-operator"
      "app.kubernetes.io/part-of"    = "tenant-management-system"
    }
  }
}

# Create service account for the tenant operator
resource "kubernetes_service_account" "operator" {
  count = !var.skip_k8s_connection ? 1 : 0

  metadata {
    name      = "tenant-operator"
    namespace = local.operator_namespace
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
      "app.kubernetes.io/name"       = "tenant-operator"
      "app.kubernetes.io/part-of"    = "tenant-management-system"
    }
    annotations = {
      "eks.amazonaws.com/role-arn" = var.operator_role_arn
    }
  }

  depends_on = [kubernetes_namespace.operator]
}

# Create cluster role for the tenant operator
resource "kubernetes_cluster_role" "operator" {
  count = !var.skip_k8s_connection ? 1 : 0

  metadata {
    name = "tenant-operator-role"
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
      "app.kubernetes.io/name"       = "tenant-operator"
      "app.kubernetes.io/part-of"    = "tenant-management-system"
    }
  }

  rule {
    api_groups = [""]
    resources  = ["namespaces", "services", "configmaps", "secrets", "serviceaccounts", "pods", "events"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["apps"]
    resources  = ["deployments", "statefulsets", "daemonsets", "replicasets"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["networking.k8s.io"]
    resources  = ["networkpolicies", "ingresses"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["rbac.authorization.k8s.io"]
    resources  = ["roles", "rolebindings", "clusterroles", "clusterrolebindings"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["policy"]
    resources  = ["poddisruptionbudgets"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["autoscaling"]
    resources  = ["horizontalpodautoscalers"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["monitoring.coreos.com"]
    resources  = ["servicemonitors", "podmonitors", "prometheusrules"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["networking.istio.io"]
    resources  = ["virtualservices", "destinationrules", "gateways", "serviceentries"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["security.istio.io"]
    resources  = ["authorizationpolicies", "peerauthentications", "requestauthentications"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["cert-manager.io"]
    resources  = ["certificates", "issuers"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["batch"]
    resources  = ["jobs", "cronjobs"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }

  rule {
    api_groups = ["apiextensions.k8s.io"]
    resources  = ["customresourcedefinitions"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }
}

# Create cluster role binding for the tenant operator
resource "kubernetes_cluster_role_binding" "operator" {
  count = !var.skip_k8s_connection ? 1 : 0

  metadata {
    name = "tenant-operator-binding"
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
      "app.kubernetes.io/name"       = "tenant-operator"
      "app.kubernetes.io/part-of"    = "tenant-management-system"
    }
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "ClusterRole"
    name      = kubernetes_cluster_role.operator[0].metadata[0].name
  }

  subject {
    kind      = "ServiceAccount"
    name      = kubernetes_service_account.operator[0].metadata[0].name
    namespace = local.operator_namespace
  }
}

# Create custom resource definition for tenants
resource "kubernetes_manifest" "tenant_crd" {
  count = !var.skip_k8s_connection ? 1 : 0

  manifest = {
    apiVersion = "apiextensions.k8s.io/v1"
    kind       = "CustomResourceDefinition"
    metadata = {
      name = "tenants.tenant.architrave.io"
      labels = {
        "app.kubernetes.io/managed-by" = "terraform"
        "app.kubernetes.io/name"       = "tenant-operator"
        "app.kubernetes.io/part-of"    = "tenant-management-system"
      }
    }
    spec = {
      group = "tenant.architrave.io"
      names = {
        kind     = "Tenant"
        listKind = "TenantList"
        plural   = "tenants"
        singular = "tenant"
      }
      scope = "Cluster"
      versions = [
        {
          name    = "v1"
          served  = true
          storage = true
          schema = {
            openAPIV3Schema = {
              type = "object"
              properties = {
                spec = {
                  type = "object"
                  properties = {
                    id = {
                      type = "string"
                    }
                    displayName = {
                      type = "string"
                    }
                    description = {
                      type = "string"
                    }
                    contact = {
                      type = "object"
                      properties = {
                        name = {
                          type = "string"
                        }
                        email = {
                          type = "string"
                        }
                        phone = {
                          type = "string"
                        }
                      }
                    }
                    namespacePrefix = {
                      type = "string"
                    }
                    resourceQuota = {
                      type = "object"
                      properties = {
                        cpu = {
                          type = "string"
                        }
                        memory = {
                          type = "string"
                        }
                        storage = {
                          type = "string"
                        }
                        pods = {
                          type = "integer"
                        }
                        services = {
                          type = "integer"
                        }
                      }
                    }
                    networkPolicy = {
                      type = "object"
                      properties = {
                        enabled = {
                          type = "boolean"
                        }
                        allowedNamespaces = {
                          type = "array"
                          items = {
                            type = "string"
                          }
                        }
                        ingressRules = {
                          type = "array"
                          items = {
                            type = "object"
                            properties = {
                              from = {
                                type = "object"
                                properties = {
                                  namespaceSelector = {
                                    type = "object"
                                    properties = {
                                      matchLabels = {
                                        type = "object"
                                        additionalProperties = {
                                          type = "string"
                                        }
                                      }
                                    }
                                  }
                                  podSelector = {
                                    type = "object"
                                    properties = {
                                      matchLabels = {
                                        type = "object"
                                        additionalProperties = {
                                          type = "string"
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                              ports = {
                                type = "array"
                                items = {
                                  type = "object"
                                  properties = {
                                    port = {
                                      type = "integer"
                                    }
                                    protocol = {
                                      type = "string"
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                        egressRules = {
                          type = "array"
                          items = {
                            type = "object"
                            properties = {
                              to = {
                                type = "object"
                                properties = {
                                  namespaceSelector = {
                                    type = "object"
                                    properties = {
                                      matchLabels = {
                                        type = "object"
                                        additionalProperties = {
                                          type = "string"
                                        }
                                      }
                                    }
                                  }
                                  podSelector = {
                                    type = "object"
                                    properties = {
                                      matchLabels = {
                                        type = "object"
                                        additionalProperties = {
                                          type = "string"
                                        }
                                      }
                                    }
                                  }
                                }
                              }
                              ports = {
                                type = "array"
                                items = {
                                  type = "object"
                                  properties = {
                                    port = {
                                      type = "integer"
                                    }
                                    protocol = {
                                      type = "string"
                                    }
                                  }
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                    services = {
                      type = "array"
                      items = {
                        type = "object"
                        properties = {
                          name = {
                            type = "string"
                          }
                          type = {
                            type = "string"
                          }
                          version = {
                            type = "string"
                          }
                          replicas = {
                            type = "integer"
                          }
                          resources = {
                            type = "object"
                            properties = {
                              requests = {
                                type = "object"
                                properties = {
                                  cpu = {
                                    type = "string"
                                  }
                                  memory = {
                                    type = "string"
                                  }
                                }
                              }
                              limits = {
                                type = "object"
                                properties = {
                                  cpu = {
                                    type = "string"
                                  }
                                  memory = {
                                    type = "string"
                                  }
                                }
                              }
                            }
                          }
                          autoscaling = {
                            type = "object"
                            properties = {
                              enabled = {
                                type = "boolean"
                              }
                              minReplicas = {
                                type = "integer"
                              }
                              maxReplicas = {
                                type = "integer"
                              }
                              targetCPUUtilizationPercentage = {
                                type = "integer"
                              }
                              targetMemoryUtilizationPercentage = {
                                type = "integer"
                              }
                            }
                          }
                        }
                      }
                    }
                    database = {
                      type = "object"
                      properties = {
                        enabled = {
                          type = "boolean"
                        }
                        type = {
                          type = "string"
                        }
                        name = {
                          type = "string"
                        }
                        storageSize = {
                          type = "string"
                        }
                      }
                    }
                    storage = {
                      type = "object"
                      properties = {
                        enabled = {
                          type = "boolean"
                        }
                        type = {
                          type = "string"
                        }
                        size = {
                          type = "string"
                        }
                      }
                    }
                    monitoring = {
                      type = "object"
                      properties = {
                        enabled = {
                          type = "boolean"
                        }
                        prometheus = {
                          type = "boolean"
                        }
                        grafana = {
                          type = "boolean"
                        }
                        alerting = {
                          type = "boolean"
                        }
                      }
                    }
                    ingress = {
                      type = "object"
                      properties = {
                        enabled = {
                          type = "boolean"
                        }
                        domain = {
                          type = "string"
                        }
                        tls = {
                          type = "boolean"
                        }
                      }
                    }
                  }
                  required = ["id", "displayName"]
                }
                status = {
                  type = "object"
                  properties = {
                    phase = {
                      type = "string"
                    }
                    message = {
                      type = "string"
                    }
                    namespaces = {
                      type = "array"
                      items = {
                        type = "string"
                      }
                    }
                    services = {
                      type = "array"
                      items = {
                        type = "object"
                        properties = {
                          name = {
                            type = "string"
                          }
                          status = {
                            type = "string"
                          }
                          message = {
                            type = "string"
                          }
                        }
                      }
                    }
                    database = {
                      type = "object"
                      properties = {
                        status = {
                          type = "string"
                        }
                        message = {
                          type = "string"
                        }
                        connectionString = {
                          type = "string"
                        }
                      }
                    }
                    storage = {
                      type = "object"
                      properties = {
                        status = {
                          type = "string"
                        }
                        message = {
                          type = "string"
                        }
                        endpoint = {
                          type = "string"
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          additionalPrinterColumns = [
            {
              name     = "Display Name"
              type     = "string"
              jsonPath = ".spec.displayName"
            },
            {
              name     = "Status"
              type     = "string"
              jsonPath = ".status.phase"
            },
            {
              name     = "Age"
              type     = "date"
              jsonPath = ".metadata.creationTimestamp"
            }
          ]
          subresources = {
            status = {}
          }
        }
      ]
    }
  }
}

# Deploy the tenant operator
resource "kubernetes_deployment" "operator" {
  count = !var.skip_k8s_connection ? 1 : 0

  metadata {
    name      = "tenant-operator"
    namespace = local.operator_namespace
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
      "app.kubernetes.io/name"       = "tenant-operator"
      "app.kubernetes.io/part-of"    = "tenant-management-system"
    }
  }

  spec {
    replicas = var.operator_replicas

    selector {
      match_labels = {
        "app.kubernetes.io/name" = "tenant-operator"
      }
    }

    template {
      metadata {
        labels = {
          "app.kubernetes.io/name" = "tenant-operator"
        }
      }

      spec {
        service_account_name = kubernetes_service_account.operator[0].metadata[0].name

        container {
          name  = "tenant-operator"
          image = "${var.operator_image_repository}:${var.operator_image_tag}"

          env {
            name  = "LOG_LEVEL"
            value = var.operator_log_level
          }

          env {
            name  = "WATCH_NAMESPACE"
            value = ""
          }

          env {
            name  = "OPERATOR_NAME"
            value = "tenant-operator"
          }

          env {
            name  = "AWS_REGION"
            value = data.aws_region.current.name
          }

          env {
            name  = "DATABASE_SECRET_ARN"
            value = var.database_secret_arn
          }

          env {
            name  = "S3_BUCKET_NAME"
            value = var.s3_bucket_name
          }

          env {
            name  = "DEFAULT_RESOURCE_QUOTA_CPU"
            value = var.default_resource_quota_cpu
          }

          env {
            name  = "DEFAULT_RESOURCE_QUOTA_MEMORY"
            value = var.default_resource_quota_memory
          }

          env {
            name  = "DEFAULT_RESOURCE_QUOTA_STORAGE"
            value = var.default_resource_quota_storage
          }

          env {
            name  = "DEFAULT_RESOURCE_QUOTA_PODS"
            value = var.default_resource_quota_pods
          }

          env {
            name  = "ENABLE_NETWORK_POLICIES"
            value = var.enable_network_policies ? "true" : "false"
          }

          env {
            name  = "ENABLE_POD_SECURITY_POLICIES"
            value = var.enable_pod_security_policies ? "true" : "false"
          }

          env {
            name  = "ENABLE_ISTIO_MTLS"
            value = var.enable_istio_mtls ? "true" : "false"
          }

          env {
            name  = "ENABLE_MONITORING"
            value = var.enable_monitoring ? "true" : "false"
          }

          env {
            name  = "PROMETHEUS_NAMESPACE"
            value = var.prometheus_namespace
          }

          env {
            name  = "GRAFANA_NAMESPACE"
            value = var.grafana_namespace
          }

          resources {
            limits = {
              cpu    = var.operator_resources.limits.cpu
              memory = var.operator_resources.limits.memory
            }
            requests = {
              cpu    = var.operator_resources.requests.cpu
              memory = var.operator_resources.requests.memory
            }
          }

          liveness_probe {
            http_get {
              path = "/healthz"
              port = 8081
            }
            initial_delay_seconds = 15
            period_seconds        = 20
          }

          readiness_probe {
            http_get {
              path = "/readyz"
              port = 8081
            }
            initial_delay_seconds = 5
            period_seconds        = 10
          }
        }
      }
    }
  }

  depends_on = [
    kubernetes_namespace.operator,
    kubernetes_service_account.operator,
    kubernetes_cluster_role_binding.operator,
    kubernetes_manifest.tenant_crd
  ]
}

# Create a service for the tenant operator
resource "kubernetes_service" "operator" {
  count = !var.skip_k8s_connection ? 1 : 0

  metadata {
    name      = "tenant-operator"
    namespace = local.operator_namespace
    labels = {
      "app.kubernetes.io/managed-by" = "terraform"
      "app.kubernetes.io/name"       = "tenant-operator"
      "app.kubernetes.io/part-of"    = "tenant-management-system"
    }
  }

  spec {
    selector = {
      "app.kubernetes.io/name" = "tenant-operator"
    }

    port {
      name        = "metrics"
      port        = 8080
      target_port = 8080
    }

    port {
      name        = "health"
      port        = 8081
      target_port = 8081
    }

    type = "ClusterIP"
  }

  depends_on = [kubernetes_deployment.operator]
}

# Create KMS key for tenant encryption
resource "aws_kms_key" "tenant_key" {
  description             = "${var.environment} tenant encryption key"
  deletion_window_in_days = 30
  enable_key_rotation     = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "Allow tenant operator to use the key"
        Effect = "Allow"
        Principal = {
          AWS = var.operator_role_arn
        }
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = "*"
      }
    ]
  })

  tags = local.tags
}

# Create KMS alias for tenant encryption key
resource "aws_kms_alias" "tenant_key_alias" {
  name          = "alias/${var.environment}-tenant-key"
  target_key_id = aws_kms_key.tenant_key.key_id
}

# Create IAM role for tenant S3 access
resource "aws_iam_role" "tenant_s3_role" {
  name = "${var.environment}-tenant-s3-role-v2"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRoleWithWebIdentity"
        Effect = "Allow"
        Principal = {
          Federated = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${var.eks_oidc_provider}"
        }
        Condition = {
          StringEquals = {
            "${var.eks_oidc_provider}:sub" = "system:serviceaccount:tenant-*:tenant-s3-sa"
          }
        }
      }
    ]
  })

  tags = local.tags
}

# Create IAM policy for tenant S3 access
resource "aws_iam_policy" "tenant_s3_policy" {
  name        = "${var.environment}-tenant-s3-policy-v2"
  description = "Policy for tenant S3 access"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "TenantS3Access"
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::tenant-*-assets",
          "arn:aws:s3:::tenant-*-assets/*"
        ]
        Condition = {
          StringEquals = {
            "aws:ResourceTag/TenantId" = "$${aws:PrincipalTag/TenantId}"
          }
        }
      },
      {
        Sid    = "TenantS3ListBuckets"
        Effect = "Allow"
        Action = [
          "s3:ListAllMyBuckets"
        ]
        Resource = [
          "*"
        ]
      },
      {
        Sid    = "TenantKMSAccess"
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = [
          aws_kms_key.tenant_key.arn
        ]
      }
    ]
  })
}

# Attach IAM policy to tenant S3 role
resource "aws_iam_role_policy_attachment" "tenant_s3_policy_attachment_main" {
  role       = aws_iam_role.tenant_s3_role.name
  policy_arn = aws_iam_policy.tenant_s3_policy.arn
}

# Create IAM role for tenant RDS access
resource "aws_iam_role" "tenant_rds_role" {
  name = "${var.environment}-tenant-rds-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRoleWithWebIdentity"
        Effect = "Allow"
        Principal = {
          Federated = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${var.eks_oidc_provider}"
        }
        Condition = {
          StringEquals = {
            "${var.eks_oidc_provider}:sub" = "system:serviceaccount:tenant-*:tenant-rds-sa"
          }
        }
      }
    ]
  })

  tags = local.tags
}

# Create IAM policy for tenant RDS access
resource "aws_iam_policy" "tenant_rds_policy" {
  name        = "${var.environment}-tenant-rds-policy"
  description = "Policy for tenant RDS access"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "TenantRDSAccess"
        Effect = "Allow"
        Action = [
          "rds-db:connect"
        ]
        Resource = [
          "arn:aws:rds-db:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:dbuser:*/tenant_*"
        ]
        Condition = {
          StringEquals = {
            "aws:ResourceTag/TenantId" = "$${aws:PrincipalTag/TenantId}"
          }
        }
      },
      {
        Sid    = "TenantSecretsManagerAccess"
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ]
        Resource = [
          "arn:aws:secretsmanager:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:secret:${var.environment}/tenant/*"
        ]
        Condition = {
          StringEquals = {
            "aws:ResourceTag/TenantId" = "$${aws:PrincipalTag/TenantId}"
          }
        }
      },
      {
        Sid    = "TenantKMSAccess"
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey"
        ]
        Resource = [
          aws_kms_key.tenant_key.arn
        ]
      }
    ]
  })
}

# Attach IAM policy to tenant RDS role
resource "aws_iam_role_policy_attachment" "tenant_rds_policy_attachment" {
  role       = aws_iam_role.tenant_rds_role.name
  policy_arn = aws_iam_policy.tenant_rds_policy.arn
}

# Wait for CRD to be ready
resource "time_sleep" "wait_for_crd" {
  count = !var.skip_k8s_connection ? 1 : 0

  depends_on = [kubernetes_manifest.tenant_crd]

  create_duration = "60s"
}

# Check if CRD is ready using kubectl
resource "null_resource" "check_crd_ready" {
  count = !var.skip_k8s_connection ? 1 : 0

  provisioner "local-exec" {
    command = <<-EOT
      # Wait for CRD to be established
      timeout=300
      while [ $timeout -gt 0 ]; do
        if kubectl get crd tenants.tenant.architrave.io >/dev/null 2>&1; then
          echo "CRD is ready"
          exit 0
        fi
        echo "Waiting for CRD to be ready..."
        sleep 5
        timeout=$((timeout - 5))
      done
      echo "Timeout waiting for CRD"
      exit 1
    EOT
  }

  depends_on = [time_sleep.wait_for_crd]
}

# Create tenant resources for each tenant
resource "kubernetes_manifest" "tenant" {
  for_each = (var.skip_k8s_connection || var.skip_tenant_resources) ? {} : var.tenants

  manifest = {
    apiVersion = "tenant.architrave.io/v1"
    kind       = "Tenant"
    metadata = {
      name = each.key
      labels = {
        "app.kubernetes.io/managed-by" = "terraform"
      }
    }
    spec = {
      id          = each.value.id
      displayName = each.value.display_name
      description = each.value.description
      contact = {
        name  = each.value.contact.name
        email = each.value.contact.email
        phone = each.value.contact.phone
      }
      namespacePrefix = each.value.namespace_prefix
      resourceQuota = {
        cpu      = each.value.resource_quota.cpu
        memory   = each.value.resource_quota.memory
        storage  = each.value.resource_quota.storage
        pods     = each.value.resource_quota.pods
        services = each.value.resource_quota.services
      }
      networkPolicy = {
        enabled           = each.value.network_policy.enabled
        allowedNamespaces = each.value.network_policy.allowed_namespaces
      }
      database = {
        enabled      = each.value.database.enabled
        type         = each.value.database.type
        name         = each.value.database.name
        storageSize  = each.value.database.storage_size
        importFromS3 = lookup(each.value.database, "importFromS3", false)
        s3Bucket     = lookup(each.value.database, "s3Bucket", "architravetestdb")
        s3Key        = lookup(each.value.database, "s3Key", "architrave_1.45.2.sql")
      }
      storage = {
        enabled    = each.value.storage.enabled
        type       = each.value.storage.type
        size       = each.value.storage.size
        bucketName = lookup(each.value.storage, "bucketName", "tenant-${each.value.id}-assets")
        encryption = lookup(each.value.storage, "encryption", true)
        kmsKeyId   = aws_kms_key.tenant_key.id
      }
      monitoring = {
        enabled    = each.value.monitoring.enabled
        prometheus = each.value.monitoring.prometheus
        grafana    = each.value.monitoring.grafana
        alerting   = each.value.monitoring.alerting
        loki       = lookup(each.value.monitoring, "loki", true)
        jaeger     = lookup(each.value.monitoring, "jaeger", true)
      }
      ingress = {
        enabled      = each.value.ingress.enabled
        domain       = each.value.ingress.domain
        tls          = each.value.ingress.tls
        istioGateway = lookup(each.value.ingress, "istioGateway", "tenant-gateway")
        mtls         = lookup(each.value.ingress, "mtls", true)
      }
      iamRoles = {
        s3Role  = aws_iam_role.tenant_s3_role.arn
        rdsRole = aws_iam_role.tenant_rds_role.arn
      }
    }
  }

  depends_on = [
    kubernetes_deployment.operator,
    kubernetes_service.operator,
    null_resource.check_crd_ready
  ]
}

# Create tenant namespace with security labels
resource "kubernetes_namespace" "tenant" {
  for_each = var.tenants
  metadata {
    name = "tenant-${each.key}-v2"
    labels = {
      "tenant.architrave.io/tenant-id"     = each.key
      "pod-security.kubernetes.io/enforce" = "restricted"
      "pod-security.kubernetes.io/audit"   = "restricted"
      "pod-security.kubernetes.io/warn"    = "restricted"
      "istio-injection"                    = "enabled"
    }
  }
}

# Create tenant service account
resource "kubernetes_service_account" "tenant" {
  for_each = var.tenants
  metadata {
    name      = "${each.key}-sa"
    namespace = kubernetes_namespace.tenant[each.key].metadata[0].name
    labels = {
      "tenant.architrave.io/tenant-id" = each.key
    }
  }
  depends_on = [kubernetes_namespace.tenant]
}

# Create tenant role
resource "kubernetes_role" "tenant" {
  for_each = var.tenants
  metadata {
    name      = "${each.key}-role"
    namespace = kubernetes_namespace.tenant[each.key].metadata[0].name
    labels = {
      "tenant.architrave.io/tenant-id" = each.key
    }
  }
  rule {
    api_groups = [""]
    resources  = ["pods", "services", "configmaps", "secrets"]
    verbs      = ["get", "list", "watch", "create", "update", "patch", "delete"]
  }
}

# Create tenant role binding
resource "kubernetes_role_binding" "tenant" {
  for_each = var.tenants
  metadata {
    name      = "${each.key}-role-binding"
    namespace = kubernetes_namespace.tenant[each.key].metadata[0].name
    labels = {
      "tenant.architrave.io/tenant-id" = each.key
    }
  }
  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "Role"
    name      = kubernetes_role.tenant[each.key].metadata[0].name
  }
  subject {
    kind      = "ServiceAccount"
    name      = kubernetes_service_account.tenant[each.key].metadata[0].name
    namespace = kubernetes_namespace.tenant[each.key].metadata[0].name
  }
}

# Create network policy to enforce tenant isolation
resource "kubernetes_manifest" "tenant_network_policy" {
  for_each = var.tenants
  manifest = {
    apiVersion = "networking.k8s.io/v1"
    kind       = "NetworkPolicy"
    metadata = {
      name      = "${each.key}-isolation"
      namespace = kubernetes_namespace.tenant[each.key].metadata[0].name
      labels = {
        "tenant.architrave.io/tenant-id" = each.key
      }
    }
    spec = {
      podSelector = {}
      policyTypes = ["Ingress", "Egress"]
      ingress = [
        {
          from = [
            {
              podSelector = {
                matchLabels = {
                  "tenant.architrave.io/tenant-id" = each.key
                }
              }
            }
          ]
        }
      ]
      egress = [
        {
          to = [
            {
              podSelector = {
                matchLabels = {
                  "tenant.architrave.io/tenant-id" = each.key
                }
              }
            }
          ]
        }
      ]
    }
  }
}

# Create tenant-specific mTLS policy
resource "kubernetes_manifest" "tenant_mtls_policy" {
  for_each = var.tenants
  manifest = {
    apiVersion = "security.istio.io/v1beta1"
    kind       = "PeerAuthentication"
    metadata = {
      name      = "default"
      namespace = kubernetes_namespace.tenant[each.key].metadata[0].name
      labels = {
        "tenant.architrave.io/tenant-id" = each.key
        "istio-injection"                = "enabled"
      }
    }
    spec = {
      mtls = {
        mode = "STRICT"
      }
    }
  }
}

# Tenant WAF and Rate Limiting
module "tenant_waf" {
  source      = "../tenant_waf"
  environment = var.environment
  tags        = var.tags
  tenants = { for k, v in var.tenants : k => {
    rate_limit         = v.rate_limit
    security_threshold = v.security_threshold
    custom_rules       = lookup(v, "custom_rules", [])
  } }
}

# Tenant Monitoring
module "tenant_monitoring" {
  source   = "../tenant_monitoring"
  for_each = var.tenants

  environment = var.environment
  tags        = var.tags
  tenants = {
    for k, v in var.tenants : k => {
      db_instance           = v.db_instance
      alb_arn_suffix        = v.alb_arn_suffix
      waf_blocked_threshold = v.waf_blocked_threshold
      db_cpu_threshold      = v.db_cpu_threshold
    }
  }
}

# Tenant Database Schema
module "tenant_db_schema" {
  source      = "../tenant_db_schema"
  db_name     = var.db_name
  db_host     = var.db_host
  db_username = var.db_username
  db_password = var.db_password
  tenants = { for k, v in var.tenants : k => {
    db_role     = v.db_role
    db_password = v.db_password
  } }
}

# Tenant Backup
module "tenant_backup" {
  source   = "../tenant_backup"
  for_each = var.tenants

  environment = var.environment
  tags        = var.tags
  tenants = {
    for k, v in var.tenants : k => {
      backup_retention_days = lookup(v, "backup_retention_days", 30)
      backup_window         = lookup(v, "backup_window", "03:00-04:00")
      maintenance_window    = lookup(v, "maintenance_window", "Mon:04:00-Mon:05:00")
      efs_arn               = v.efs_arn
      rds_arn               = v.rds_arn
    }
  }
}

# Tenant Compliance
module "tenant_compliance" {
  source      = "../tenant_compliance"
  environment = var.environment
  tags        = var.tags
  tenants = { for k, v in var.tenants : k => {
    compliance_rules     = lookup(v, "compliance_rules", [])
    audit_retention_days = lookup(v, "audit_retention_days", 90)
  } }
}

# Tenant Disaster Recovery
module "tenant_disaster_recovery" {
  source   = "../tenant_disaster_recovery"
  for_each = var.tenants

  environment = var.environment
  tags        = var.tags
  tenants = {
    for k, v in var.tenants : k => {
      rto_minutes      = lookup(v, "rto_minutes", 60)
      rpo_minutes      = lookup(v, "rpo_minutes", 15)
      dr_region        = lookup(v, "dr_region", var.region)
      backup_vault_arn = v.backup_vault_arn
      dr_team_role_arn = v.dr_team_role_arn
      dr_test_schedule = v.dr_test_schedule
      rds_instance_arn = v.rds_instance_arn
    }
  }
}

# Tenant Application Security
module "tenant_app_security" {
  source   = "../tenant_app_security"
  for_each = var.tenants

  environment = var.environment
  tags        = var.tags
  tenants = {
    for k, v in var.tenants : k => {
      security_threshold = v.security_threshold
      alert_threshold    = lookup(v, "alert_threshold", 80)
      scan_frequency     = lookup(v, "scan_frequency", "daily")
      rate_limit         = v.rate_limit
    }
  }
  waf_log_group_arns = module.tenant_waf.waf_logs_arns
}

# Tenant Infrastructure Monitoring
module "tenant_infra_monitoring" {
  source      = "../tenant_infra_monitoring"
  environment = var.environment
  tags        = var.tags
  tenants = { for k, v in var.tenants : k => {
    ec2_instance_id   = v.ec2_instance_id
    rds_instance_id   = v.rds_instance_id
    efs_id            = v.efs_id
    ec2_cpu_threshold = v.ec2_cpu_threshold
    rds_cpu_threshold = v.rds_cpu_threshold
  } }
}

# Outputs
output "waf_web_acl_arns" {
  description = "ARNs of the WAF Web ACLs created for each tenant"
  value       = module.tenant_waf.web_acl_arns
}

output "monitoring_dashboard_urls" {
  description = "URLs of the CloudWatch dashboards created for each tenant"
  value       = { for k, v in module.tenant_monitoring : k => v.dashboard_urls }
}
output "waf_logs_arns" {
  description = "ARNs of the WAF CloudWatch Log Groups for each tenant"
  value       = module.tenant_waf.waf_logs_arns
}

# output "backup_vault_arns" {
#   description = "ARNs of the backup vaults created for each tenant"
#   value       = module.tenant_backup.vault_arns
# }

# output "compliance_rule_arns" {
#   description = "ARNs of the compliance rules created for each tenant"
#   value       = module.tenant_compliance.rule_arns
# }

# output "dr_test_function_arns" {
#   description = "ARNs of the DR test Lambda functions created for each tenant"
#   value       = module.tenant_disaster_recovery.function_arns
# }

# output "security_alert_topic_arns" {
#   description = "ARNs of the security alert SNS topics created for each tenant"
#   value       = module.tenant_app_security.alert_topic_arns
# }

# output "infra_monitoring_dashboard_urls" {
#   description = "URLs of the infrastructure monitoring dashboards created for each tenant"
#   value       = module.tenant_infra_monitoring.dashboard_urls
# }

