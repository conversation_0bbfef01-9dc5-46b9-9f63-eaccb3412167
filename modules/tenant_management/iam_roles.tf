# Tenant-specific IAM roles with proper permissions
# This file implements IAM roles for tenant isolation and least privilege access

# Create IAM role for tenant pods to access S3 buckets
resource "aws_iam_role" "tenant_s3_access_role" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-s3-role-v2"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRoleWithWebIdentity"
        Effect = "Allow"
        Principal = {
          Federated = var.eks_oidc_provider_arn
        }
        Condition = {
          StringEquals = {
            "${replace(var.eks_oidc_provider_url, "https://", "")}:sub" = "system:serviceaccount:tenant-${each.key}:${each.key}-s3-sa"
          }
        }
      }
    ]
  })

  tags = merge(var.tags, {
    TenantId = each.key
    Name     = "${var.environment}-tenant-${each.key}-s3-role"
  })
}

# Create IAM policy for tenant S3 access with least privilege
resource "aws_iam_policy" "tenant_s3_access_policy" {
  for_each = var.tenants

  name        = "${var.environment}-tenant-${each.key}-s3-policy-v2"
  description = "Policy for tenant ${each.key} to access its S3 bucket"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "ListBucketAccess"
        Effect = "Allow"
        Action = [
          "s3:ListBucket",
          "s3:GetBucketLocation"
        ]
        Resource = "arn:aws:s3:::tenant-${each.key}-assets"
        Condition = {
          StringEquals = {
            "aws:ResourceTag/TenantId" = each.key
          }
        }
      },
      {
        Sid    = "ObjectAccess"
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject"
        ]
        Resource = "arn:aws:s3:::tenant-${each.key}-assets/*"
      },
      {
        Sid    = "DenyAccessToOtherTenantBuckets"
        Effect = "Deny"
        Action = [
          "s3:*"
        ]
        NotResource = [
          "arn:aws:s3:::tenant-${each.key}-assets",
          "arn:aws:s3:::tenant-${each.key}-assets/*"
        ]
      }
    ]
  })

  tags = merge(var.tags, {
    TenantId = each.key
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "tenant_s3_policy_attachment" {
  for_each = var.tenants

  role       = aws_iam_role.tenant_s3_access_role[each.key].name
  policy_arn = aws_iam_policy.tenant_s3_access_policy[each.key].arn
}

# Create IAM role for tenant pods to access their database
resource "aws_iam_role" "tenant_db_access_role" {
  for_each = var.tenants

  name = "${var.environment}-tenant-${each.key}-db-role-v2"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRoleWithWebIdentity"
        Effect = "Allow"
        Principal = {
          Federated = var.eks_oidc_provider_arn
        }
        Condition = {
          StringEquals = {
            "${replace(var.eks_oidc_provider_url, "https://", "")}:sub" = "system:serviceaccount:tenant-${each.key}:${each.key}-db-sa"
          }
        }
      }
    ]
  })

  tags = merge(var.tags, {
    TenantId = each.key
    Name     = "${var.environment}-tenant-${each.key}-db-role"
  })
}

# Create IAM policy for tenant database access with least privilege
resource "aws_iam_policy" "tenant_db_access_policy" {
  for_each = var.tenants

  name        = "${var.environment}-tenant-${each.key}-db-policy-v2"
  description = "IAM policy for tenant ${each.key} database access"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "rds-db:connect",
          "rds:DescribeDBInstances",
          "rds:DescribeDBClusters"
        ]
        Resource = [
          "arn:aws:rds:${var.region}:${var.account_id}:db:${each.key}-db",
          "arn:aws:rds:${var.region}:${var.account_id}:cluster:${each.key}-db"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:DescribeKey"
        ]
        Resource = lookup(var.tenant_kms_key_arns, each.key, "arn:aws:kms:${var.region}:${var.account_id}:key/*")
      }
    ]
  })

  tags = merge(var.tags, {
    Name = "${var.environment}-tenant-${each.key}-db-access-policy"
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "tenant_db_policy_attachment" {
  for_each = var.tenants

  role       = aws_iam_role.tenant_db_access_role[each.key].name
  policy_arn = aws_iam_policy.tenant_db_access_policy[each.key].arn
}

# Create service account for S3 access
resource "kubernetes_service_account" "tenant_s3_service_account" {
  for_each = var.tenants

  metadata {
    name      = "${each.key}-s3-sa-v2"
    namespace = "tenant-${each.key}-v2"
    annotations = {
      "eks.amazonaws.com/role-arn" = aws_iam_role.tenant_s3_access_role[each.key].arn
    }
    labels = {
      "app.kubernetes.io/name"       = "${each.key}-s3-sa"
      "app.kubernetes.io/component"  = "storage"
      "app.kubernetes.io/managed-by" = "terraform"
      "tenant.architrave.io/id"      = each.key
    }
  }

  depends_on = [
    aws_iam_role.tenant_s3_access_role,
    kubernetes_namespace.tenant
  ]
}

# Create service account for database access
resource "kubernetes_service_account" "tenant_db_service_account" {
  for_each = var.tenants

  metadata {
    name      = "${each.key}-db-sa-v2"
    namespace = "tenant-${each.key}-v2"
    annotations = {
      "eks.amazonaws.com/role-arn" = aws_iam_role.tenant_db_access_role[each.key].arn
    }
    labels = {
      "app.kubernetes.io/name"       = "${each.key}-db-sa"
      "app.kubernetes.io/component"  = "database"
      "app.kubernetes.io/managed-by" = "terraform"
      "tenant.architrave.io/id"      = each.key
    }
  }

  depends_on = [
    aws_iam_role.tenant_db_access_role,
    kubernetes_namespace.tenant
  ]
}
