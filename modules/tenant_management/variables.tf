variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "name" {
  description = "Name prefix for resources"
  type        = string
  default     = "tenant-management"
}

variable "tags" {
  description = "Map of tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "operator_namespace" {
  description = "Kubernetes namespace for the tenant operator"
  type        = string
  default     = "tenant-system"
}

variable "create_operator_namespace" {
  description = "Whether to create the operator namespace"
  type        = bool
  default     = true
}

variable "operator_image_repository" {
  description = "Repository for the tenant operator image"
  type        = string
  default     = "architrave/tenant-operator"
}

variable "operator_image_tag" {
  description = "Tag for the tenant operator image"
  type        = string
  default     = "latest"
}

variable "operator_replicas" {
  description = "Number of replicas for the tenant operator"
  type        = number
  default     = 1
}

variable "operator_log_level" {
  description = "Log level for the tenant operator"
  type        = string
  default     = "info"
}

variable "operator_resources" {
  description = "Resource requests and limits for the tenant operator"
  type = object({
    requests = object({
      cpu    = string
      memory = string
    })
    limits = object({
      cpu    = string
      memory = string
    })
  })
  default = {
    requests = {
      cpu    = "100m"
      memory = "128Mi"
    }
    limits = {
      cpu    = "200m"
      memory = "256Mi"
    }
  }
}

variable "operator_role_arn" {
  description = "ARN of the IAM role for the tenant operator"
  type        = string
  default     = ""
}

variable "eks_oidc_provider_arn" {
  description = "ARN of the EKS OIDC provider for IAM roles for service accounts"
  type        = string
}

variable "eks_oidc_provider_url" {
  description = "URL of the EKS OIDC provider for IAM roles for service accounts"
  type        = string
}

variable "eks_oidc_provider" {
  description = "EKS OIDC provider ID for IAM roles for service accounts"
  type        = string
  default     = ""
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "account_id" {
  description = "AWS account ID"
  type        = string
}

variable "tenant_kms_key_arns" {
  description = "Map of tenant IDs to KMS key ARNs for tenant-specific encryption"
  type        = map(string)
  default     = {}
}

variable "database_secret_arn" {
  description = "ARN of the Secrets Manager secret containing database credentials"
  type        = string
  default     = ""
}

variable "s3_bucket_name" {
  description = "Name of the S3 bucket for tenant storage"
  type        = string
  default     = ""
}

variable "default_resource_quota_cpu" {
  description = "Default CPU resource quota for tenants"
  type        = string
  default     = "1000m"
}

variable "default_resource_quota_memory" {
  description = "Default memory resource quota for tenants"
  type        = string
  default     = "2Gi"
}

variable "default_resource_quota_storage" {
  description = "Default storage resource quota for tenants"
  type        = string
  default     = "10Gi"
}

variable "default_resource_quota_pods" {
  description = "Default pod count resource quota for tenants"
  type        = string
  default     = "10"
}

variable "enable_network_policies" {
  description = "Whether to enable network policies for tenants"
  type        = bool
  default     = true
}

variable "enable_pod_security_policies" {
  description = "Whether to enable pod security policies for tenants"
  type        = bool
  default     = true
}

variable "enable_istio_mtls" {
  description = "Whether to enable Istio mTLS for tenants"
  type        = bool
  default     = true
}

variable "enable_monitoring" {
  description = "Whether to enable monitoring for tenants"
  type        = bool
  default     = true
}

variable "prometheus_namespace" {
  description = "Namespace where Prometheus is deployed"
  type        = string
  default     = "monitoring"
}

variable "grafana_namespace" {
  description = "Namespace where Grafana is deployed"
  type        = string
  default     = "monitoring"
}

variable "skip_k8s_connection" {
  description = "Skip Kubernetes connection in CI/CD environments"
  type        = bool
  default     = false
}

variable "skip_tenant_resources" {
  description = "Skip creating tenant custom resources (useful for initial CRD deployment)"
  type        = bool
  default     = false
}

variable "tenants" {
  description = "Map of tenant configurations"
  type = map(object({
    # Basic tenant info
    id               = string
    namespace_prefix = string
    display_name     = string
    description      = string

    # WAF and Rate Limiting
    rate_limit         = number
    security_threshold = number

    # Database
    db_role     = string
    db_password = string

    # Backup
    rds_arn               = string
    efs_arn               = string
    backup_retention_days = number

    # Disaster Recovery
    dr_team_role_arn = string
    dr_test_schedule = string
    backup_vault_arn = string
    rds_instance_arn = string

    # Infrastructure
    ec2_instance_id       = string
    rds_instance_id       = string
    efs_id                = string
    ec2_cpu_threshold     = number
    rds_cpu_threshold     = number
    alb_arn_suffix        = string
    db_cpu_threshold      = number
    db_instance           = string
    waf_blocked_threshold = number

    # Compliance
    compliance_rules = list(string)

    # Contact information
    contact = object({
      name  = string
      email = string
      phone = string
    })

    # Resource quota configuration
    resource_quota = object({
      cpu      = string
      memory   = string
      storage  = string
      pods     = string
      services = string
    })

    # Network policy configuration
    network_policy = object({
      enabled            = bool
      allowed_namespaces = list(string)
    })

    # Database configuration
    database = object({
      enabled      = bool
      type         = string
      name         = string
      storage_size = string
      importFromS3 = bool
      s3Bucket     = string
      s3Key        = string
    })

    # Storage configuration
    storage = object({
      enabled    = bool
      type       = string
      size       = string
      bucketName = string
      encryption = bool
    })

    # Monitoring configuration
    monitoring = object({
      enabled    = bool
      grafana    = bool
      prometheus = bool
      alerting   = bool
      loki       = bool
      jaeger     = bool
    })

    # Ingress configuration
    ingress = object({
      enabled      = bool
      domain       = string
      tls          = bool
      istioGateway = string
      mtls         = bool
    })
  }))
}

variable "db_name" {
  description = "Name of the database"
  type        = string
}

variable "db_host" {
  description = "Database host address"
  type        = string
}

variable "db_username" {
  description = "Database admin username"
  type        = string
}

variable "db_password" {
  description = "Database admin password"
  type        = string
  sensitive   = true
}

variable "eks_cluster_endpoint" {
  description = "The endpoint for the EKS cluster"
  type        = string
}

variable "eks_cluster_ca_certificate" {
  description = "The CA certificate for the EKS cluster"
  type        = string
  default     = ""
}

variable "eks_cluster_name" {
  description = "The name of the EKS cluster"
  type        = string
}
