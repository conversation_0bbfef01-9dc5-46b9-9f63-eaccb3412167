# CloudWatch Dashboard for each tenant
resource "aws_cloudwatch_dashboard" "tenant" {
  for_each = var.tenants

  dashboard_name = "${var.environment}-tenant-${each.key}-dashboard"

  dashboard_body = jsonencode({
    widgets = [
      {
        type   = "metric"
        x      = 0
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/WAF", "BlockedRequests", "Tenant", each.key],
            ["AWS/WAF", "AllowedRequests", "Tenant", each.key]
          ]
          period = 300
          stat   = "Sum"
          region = data.aws_region.current.name
          title  = "WAF Requests"
        }
      },
      {
        type   = "metric"
        x      = 12
        y      = 0
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/RDS", "CPUUtilization", "DBInstanceIdentifier", each.value.db_instance],
            ["AWS/RDS", "FreeableMemory", "DBInstanceIdentifier", each.value.db_instance]
          ]
          period = 300
          stat   = "Average"
          region = data.aws_region.current.name
          title  = "Database Metrics"
        }
      },
      {
        type   = "metric"
        x      = 0
        y      = 6
        width  = 12
        height = 6
        properties = {
          metrics = [
            ["AWS/ApplicationELB", "RequestCount", "LoadBalancer", each.value.alb_arn_suffix],
            ["AWS/ApplicationELB", "TargetResponseTime", "LoadBalancer", each.value.alb_arn_suffix]
          ]
          period = 300
          stat   = "Sum"
          region = data.aws_region.current.name
          title  = "ALB Metrics"
        }
      }
    ]
  })
}

# CloudWatch Alarms for each tenant
resource "aws_cloudwatch_metric_alarm" "waf_blocked_requests" {
  for_each = var.tenants

  alarm_name          = "${var.environment}-tenant-${each.key}-waf-blocked-requests"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "1"
  metric_name         = "BlockedRequests"
  namespace           = "AWS/WAF"
  period              = "300"
  statistic           = "Sum"
  threshold           = each.value.waf_blocked_threshold
  alarm_description   = "This metric monitors WAF blocked requests for tenant ${each.key}"
  alarm_actions       = [aws_sns_topic.tenant_alerts[each.key].arn]

  dimensions = {
    Tenant = each.key
  }
}

resource "aws_cloudwatch_metric_alarm" "db_cpu_utilization" {
  for_each = var.tenants

  alarm_name          = "${var.environment}-tenant-${each.key}-db-cpu-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "3"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/RDS"
  period              = "300"
  statistic           = "Average"
  threshold           = each.value.db_cpu_threshold
  alarm_description   = "This metric monitors RDS CPU utilization for tenant ${each.key}"
  alarm_actions       = [aws_sns_topic.tenant_alerts[each.key].arn]

  dimensions = {
    DBInstanceIdentifier = each.value.db_instance
  }
}

# SNS Topics for tenant alerts
resource "aws_sns_topic" "tenant_alerts" {
  for_each = var.tenants

  name = "tenant-alerts-${each.key}"
  kms_master_key_id = var.sns_kms_key_arn # <-- Set this variable to your KMS key ARN

  tags = merge(var.tags, {
    Name        = "${var.environment}-tenant-${each.key}-alerts"
    Tenant      = each.key
    Environment = var.environment
  })
}

# SNS Topic Policy
resource "aws_sns_topic_policy" "tenant_alerts" {
  for_each = var.tenants

  arn = aws_sns_topic.tenant_alerts[each.key].arn

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "cloudwatch.amazonaws.com"
        }
        Action   = "SNS:Publish"
        Resource = aws_sns_topic.tenant_alerts[each.key].arn
      }
    ]
  })
}

# Data source for current region
data "aws_region" "current" {} 