variable "environment" {
  description = "Environment name (e.g., dev, prod)"
  type        = string
}

variable "tenants" {
  description = "Map of tenant configurations"
  type = map(object({
    db_instance           = string
    alb_arn_suffix        = string
    waf_blocked_threshold = number
    db_cpu_threshold      = number
  }))
}

variable "tags" {
  description = "Map of tags to apply to all resources"
  type        = map(string)
  default     = {}
}

variable "sns_kms_key_arn" {
  description = "KMS key ARN for encrypting SNS topics. Must be set for compliance."
  type        = string
  default     = ""
} 