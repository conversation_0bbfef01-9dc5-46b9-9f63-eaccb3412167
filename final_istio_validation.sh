#!/bin/bash
# Final Istio Remediation Validation Script
set -e

echo "🧪 FINAL ISTIO REMEDIATION VALIDATION"
echo "====================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "TASK 1: TESTING REAL EXTERNAL CONNECTIVITY"
echo "=========================================="
echo ""

log_info "Step 1.1: Checking Istio system namespace..."
if kubectl get namespace istio-system >/dev/null 2>&1; then
    log_success "istio-system namespace exists"
else
    log_error "istio-system namespace not found"
    exit 1
fi

log_info "Step 1.2: Checking Istio ingress gateway service..."
if kubectl get svc istio-ingressgateway -n istio-system >/dev/null 2>&1; then
    log_success "istio-ingressgateway service exists"
    
    # Get external endpoint
    EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || echo "")
    if [ -z "$EXTERNAL_IP" ]; then
        EXTERNAL_IP=$(kubectl get svc istio-ingressgateway -n istio-system -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "")
    fi
    
    if [ -n "$EXTERNAL_IP" ]; then
        log_success "Istio Gateway external endpoint: $EXTERNAL_IP"
        echo "ISTIO_GATEWAY_ENDPOINT=$EXTERNAL_IP" > /tmp/istio_endpoint.env
    else
        log_warning "No external endpoint found - LoadBalancer may be provisioning"
    fi
else
    log_error "istio-ingressgateway service not found"
fi

log_info "Step 1.3: Applying Istio Gateway configuration..."
if kubectl apply -f kubernetes/istio/tenant-gateway.yaml >/dev/null 2>&1; then
    log_success "Istio Gateway configuration applied"
else
    log_warning "Failed to apply Gateway configuration - may already exist"
fi

log_info "Step 1.4: Checking tenant-gateway status..."
if kubectl get gateway tenant-gateway -n istio-system >/dev/null 2>&1; then
    log_success "tenant-gateway exists in istio-system"
else
    log_error "tenant-gateway not found"
fi

log_info "Step 1.5: Testing updated Go script with new tenant..."
echo "Creating test tenant: istio-validation-test"
echo ""

# Test the Go script with timeout
timeout 120 go run advanced_tenant_onboard.go --tenant-id=istio-validation-test --enable-auto-fix --enable-production-audit || {
    log_warning "Go script execution timed out or failed - checking what was created..."
}

log_info "Step 1.6: Verifying VirtualService creation..."
if kubectl get virtualservice -n tenant-istio-validation-test >/dev/null 2>&1; then
    log_success "VirtualService created for istio-validation-test tenant"
    kubectl get virtualservice -n tenant-istio-validation-test -o yaml | grep -A 5 "spec:" || true
else
    log_warning "VirtualService not found for istio-validation-test tenant"
fi

echo ""
echo "TASK 2: DNS RECORDS UPDATE"
echo "========================="
echo ""

log_info "Step 2.1: Retrieving Istio Gateway endpoint..."
if [ -f /tmp/istio_endpoint.env ]; then
    source /tmp/istio_endpoint.env
    log_success "Istio Gateway endpoint: $ISTIO_GATEWAY_ENDPOINT"
else
    log_error "Istio Gateway endpoint not found"
    exit 1
fi

log_info "Step 2.2: Checking existing tenant namespaces..."
TENANT_NAMESPACES=$(kubectl get namespaces | grep "tenant-" | awk '{print $1}' | sed 's/tenant-//' || echo "")
if [ -n "$TENANT_NAMESPACES" ]; then
    log_success "Found tenant namespaces:"
    for tenant in $TENANT_NAMESPACES; do
        echo "  - $tenant"
    done
else
    log_warning "No tenant namespaces found"
fi

log_info "Step 2.3: DNS configuration required..."
echo "Manual DNS updates needed:"
for tenant in $TENANT_NAMESPACES; do
    echo "  $tenant.architrave-assets.com -> $ISTIO_GATEWAY_ENDPOINT"
done

echo ""
echo "TASK 3: END-TO-END CONNECTIVITY VALIDATION"
echo "=========================================="
echo ""

log_info "Step 3.1: Testing direct connectivity to Istio Gateway..."
if [ -n "$ISTIO_GATEWAY_ENDPOINT" ]; then
    if curl -s --connect-timeout 5 "http://$ISTIO_GATEWAY_ENDPOINT" >/dev/null 2>&1; then
        log_success "Direct connectivity to Istio Gateway working"
    else
        log_warning "Direct connectivity test failed - may need DNS resolution"
    fi
else
    log_error "No Istio Gateway endpoint to test"
fi

log_info "Step 3.2: Checking VirtualService resources..."
VS_COUNT=$(kubectl get virtualservice --all-namespaces --no-headers 2>/dev/null | wc -l || echo "0")
log_info "Total VirtualService resources: $VS_COUNT"

if [ "$VS_COUNT" -gt 0 ]; then
    log_success "VirtualService resources found"
    kubectl get virtualservice --all-namespaces --no-headers | head -5
else
    log_warning "No VirtualService resources found"
fi

log_info "Step 3.3: Architecture validation summary..."
echo ""
echo "🏗️  ARCHITECTURE VALIDATION SUMMARY"
echo "=================================="
echo ""

# Check if we're using Istio or ALB
INGRESS_COUNT=$(kubectl get ingress --all-namespaces --no-headers 2>/dev/null | wc -l || echo "0")
VS_COUNT=$(kubectl get virtualservice --all-namespaces --no-headers 2>/dev/null | wc -l || echo "0")

if [ "$VS_COUNT" -gt "$INGRESS_COUNT" ]; then
    log_success "✅ ISTIO ARCHITECTURE: Using VirtualService resources ($VS_COUNT vs $INGRESS_COUNT Ingress)"
    echo "   External Traffic Flow: Internet → Istio Gateway → VirtualService → Service → Pod"
else
    log_warning "⚠️  MIXED ARCHITECTURE: Both Ingress ($INGRESS_COUNT) and VirtualService ($VS_COUNT) found"
    echo "   May need cleanup of conflicting resources"
fi

echo ""
echo "🎯 REMEDIATION STATUS SUMMARY"
echo "============================"
echo ""
echo "✅ Phase 1: Infrastructure Assessment - COMPLETED"
echo "✅ Phase 2: Cleanup Script - COMPLETED" 
echo "✅ Phase 3: Istio VirtualService Implementation - COMPLETED"
echo "🔄 Phase 4: Final Validation - IN PROGRESS"
echo ""

if [ -n "$ISTIO_GATEWAY_ENDPOINT" ]; then
    echo "🌐 NEXT STEPS:"
    echo "1. Update DNS records to point to: $ISTIO_GATEWAY_ENDPOINT"
    echo "2. Test external access: https://{tenant-id}.architrave-assets.com"
    echo "3. Verify DNS_PROBE_FINISHED_NXDOMAIN errors are resolved"
else
    echo "❌ CRITICAL: No Istio Gateway endpoint found"
fi

echo ""
echo "🏁 FINAL VALIDATION COMPLETE"
echo "============================"
