#!/bin/bash

# Exit on error
set -e

echo "🚀 Deploying monitoring components..."

# Create monitoring namespace if it doesn't exist
kubectl create namespace monitoring --dry-run=client -o yaml | kubectl apply -f -

# Apply backup monitoring dashboard
echo "📊 Applying backup monitoring dashboard..."
kubectl apply -f monitoring/backup-dashboard.yaml

# Apply performance monitoring dashboard
echo "⚡ Applying performance monitoring dashboard..."
kubectl apply -f monitoring/performance-dashboard.yaml

# Create ServiceMonitor for Velero
echo "🔍 Creating ServiceMonitor for Velero..."
cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: velero
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: velero
  endpoints:
  - port: metrics
    interval: 15s
EOF

# Create ServiceMonitor for k6
echo "🔍 Creating ServiceMonitor for k6..."
cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: k6
  namespace: monitoring
spec:
  selector:
    matchLabels:
      app: k6
  endpoints:
  - port: metrics
    interval: 15s
EOF

# Create Prometheus rules for backup monitoring
echo "🔔 Creating Prometheus rules for backup monitoring..."
cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: backup-monitoring-rules
  namespace: monitoring
spec:
  groups:
  - name: backup.rules
    rules:
    - alert: BackupFailed
      expr: velero_backup_failed_total > 0
      for: 1h
      labels:
        severity: critical
      annotations:
        summary: Backup has failed
        description: "Backup {{ $labels.backup }} has failed"
    - alert: BackupVerificationFailed
      expr: velero_backup_verification_failed_total > 0
      for: 1h
      labels:
        severity: critical
      annotations:
        summary: Backup verification has failed
        description: "Backup verification for {{ $labels.backup }} has failed"
    - alert: HighBackupDuration
      expr: velero_backup_duration_seconds > 3600
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: Backup duration is too high
        description: "Backup {{ $labels.backup }} took more than 1 hour"
    - alert: HighBackupStorageUsage
      expr: aws_s3_bucket_size_bytes{bucket="architrave-backups"} > 1e12
      for: 1h
      labels:
        severity: warning
      annotations:
        summary: Backup storage usage is high
        description: "Backup storage usage is above 1TB"
EOF

# Create Prometheus rules for performance monitoring
echo "🔔 Creating Prometheus rules for performance monitoring..."
cat <<EOF | kubectl apply -f -
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: performance-monitoring-rules
  namespace: monitoring
spec:
  groups:
  - name: performance.rules
    rules:
    - alert: HighLatency
      expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]) > 0.5
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High latency detected
        description: "Average response time is above 500ms"
    - alert: HighErrorRate
      expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: High error rate detected
        description: "Error rate is above 10%"
    - alert: HighRequestRate
      expr: rate(http_requests_total[5m]) > 1000
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High request rate detected
        description: "Request rate is above 1000 req/s"
EOF

# Wait for components to be ready
echo "⏳ Waiting for components to be ready..."
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=velero -n velero --timeout=300s
kubectl wait --for=condition=ready pod -l app=k6 -n testing --timeout=300s

# Verify installations
echo "✅ Verifying installations..."

# Check dashboards
kubectl get configmap -n monitoring -l grafana_dashboard=true

# Check ServiceMonitors
kubectl get servicemonitor -n monitoring

# Check PrometheusRules
kubectl get prometheusrules -n monitoring

echo "🎉 Monitoring components deployment completed!"
echo "Next steps:"
echo "1. Access Grafana dashboards at http://grafana.monitoring:3000"
echo "2. Verify backup monitoring is working"
echo "3. Run a performance test and verify monitoring"
echo "4. Check alert configurations" 