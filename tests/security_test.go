package tests

import (
	"advanced-tenant-onboard/security"
	"testing"
)

func TestTenantIDValidation(t *testing.T) {
	validator := &security.SecurityValidator{}
	
	tests := []struct {
		name        string
		tenantID    string
		expectError bool
	}{
		{"valid tenant id", "test-tenant-123", false},
		{"empty tenant id", "", true},
		{"too short", "ab", true},
		{"too long", "a-very-long-tenant-id-that-exceeds-the-maximum-allowed-length", true},
		{"invalid characters", "test@tenant", true},
		{"starts with hyphen", "-test-tenant", true},
		{"ends with hyphen", "test-tenant-", true},
		{"consecutive hyphens", "test--tenant", true},
		{"reserved name", "admin", true},
		{"sql injection attempt", "test'; DROP TABLE users; --", true},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := validator.ValidateTenantID(tt.tenantID)
			if tt.expectError && err == nil {
				t.Errorf("expected error for tenant ID '%s', but got none", tt.tenantID)
			}
			if !tt.expectError && err != nil {
				t.Errorf("unexpected error for tenant ID '%s': %v", tt.tenantID, err)
			}
		})
	}
}

func TestDatabaseNameValidation(t *testing.T) {
	validator := &security.SecurityValidator{}
	
	tests := []struct {
		name        string
		dbName      string
		expectError bool
	}{
		{"valid database name", "test_database", false},
		{"empty database name", "", true},
		{"too long", "a_very_long_database_name_that_exceeds_the_maximum_allowed_length_of_sixty_four_characters", true},
		{"starts with number", "123database", true},
		{"invalid characters", "test-database", true},
		{"sql injection attempt", "test'; DROP TABLE users; --", true},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := validator.ValidateDatabaseName(tt.dbName)
			if tt.expectError && err == nil {
				t.Errorf("expected error for database name '%s', but got none", tt.dbName)
			}
			if !tt.expectError && err != nil {
				t.Errorf("unexpected error for database name '%s': %v", tt.dbName, err)
			}
		})
	}
}

func TestSQLInputSanitization(t *testing.T) {
	validator := &security.SecurityValidator{}
	
	tests := []struct {
		name        string
		input       string
		expectError bool
	}{
		{"valid input", "normal text", false},
		{"empty input", "", true},
		{"sql injection", "'; DROP TABLE users; --", true},
		{"sql injection 2", "1 OR 1=1", true},
		{"sql injection 3", "admin'--", true},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := validator.SanitizeSQLInput(tt.input)
			if tt.expectError && err == nil {
				t.Errorf("expected error for input '%s', but got none", tt.input)
			}
			if !tt.expectError && err != nil {
				t.Errorf("unexpected error for input '%s': %v", tt.input, err)
			}
		})
	}
} 