# Grafana Dashboards

This directory contains custom Grafana dashboard templates for monitoring various aspects of the infrastructure.

## Available Dashboards

### 1. Kubernetes Dashboard

The `kubernetes-dashboard.json` provides a comprehensive view of your Kubernetes cluster, including:

- Cluster overview (nodes, pods, deployments)
- Node resource usage (CPU, memory, disk)
- Pod resource usage and status
- Control plane metrics

### 2. RDS Dashboard

The `rds-dashboard.json` monitors your RDS database instances, including:

- CPU and memory utilization
- Database connections
- Query performance
- Storage metrics
- Backup status

### 3. Tenant Monitoring Dashboard

The `tenant-monitoring-dashboard.json` provides tenant-specific monitoring, including:

- Kubernetes resources (CPU, memory, pods)
- Database metrics (connections, queries, storage)
- S3 metrics (storage, objects)
- API performance (request rate, latency)

## Dashboard Features

All dashboards include:

- **Variable Support**: Dashboards use template variables for filtering
- **Multi-Datasource**: Metrics from Prometheus, CloudWatch, and MySQL
- **Alerting**: Pre-configured alert thresholds
- **Responsive Layout**: Optimized for different screen sizes

## Using the Dashboards

### Importing Dashboards

Dashboards can be imported into Grafana in several ways:

1. **Automatic Import**: Dashboards are automatically imported when Grafana is deployed using the Helm chart.

2. **Manual Import**: To import a dashboard manually:
   - Go to Grafana UI
   - Click on "+" > "Import"
   - Upload the JSON file or paste its contents

### Customizing Dashboards

After importing, you can customize the dashboards:

1. Click the settings icon (gear) in the top right
2. Make your changes
3. Save the dashboard with a new name to avoid overwriting the template

## Creating New Dashboards

To create a new dashboard:

1. Use an existing dashboard as a template
2. Save it with a new name
3. Customize panels and queries
4. Add the dashboard to the `dashboards` directory
5. Update the Grafana values file to include the new dashboard

## Dashboard Best Practices

1. **Use Template Variables**: Make dashboards reusable with variables
2. **Consistent Naming**: Use consistent naming for metrics and labels
3. **Appropriate Time Ranges**: Set default time ranges appropriate for the metrics
4. **Helpful Documentation**: Add documentation to panels and dashboards
5. **Optimize Queries**: Ensure queries are efficient and don't overload data sources

## Tenant-Specific Dashboards

For tenant-specific monitoring:

1. Use the `tenant-monitoring-dashboard.json` as a template
2. Set the `tenant_id` variable to the specific tenant ID
3. Save the dashboard with a tenant-specific name

## Troubleshooting

If dashboards are not displaying data:

1. **Check Data Sources**: Ensure data sources are properly configured
2. **Verify Metrics Collection**: Check that metrics are being collected
3. **Check Permissions**: Ensure Grafana has permissions to access the data sources
4. **Inspect Queries**: Use the query inspector to debug panel queries
