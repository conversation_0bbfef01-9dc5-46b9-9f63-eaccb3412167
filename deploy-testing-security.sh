#!/bin/bash

# Exit on error
set -e

echo "🚀 Deploying Comprehensive Testing and Security Infrastructure"

# Create necessary namespaces
kubectl create namespace testing --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace security --dry-run=client -o yaml | kubectl apply -f -

# Deploy testing infrastructure
echo "📊 Deploying Testing Infrastructure..."

# Deploy SonarQube for static analysis
helm repo add sonarqube https://SonarSource.github.io/helm-chart-sonarqube
helm repo update
helm upgrade --install sonarqube sonarqube/sonarqube \
  --namespace testing \
  --set postgresql.enabled=true \
  --set postgresql.postgresqlUsername=sonar \
  --set postgresql.postgresqlPassword=sonar \
  --set postgresql.postgresqlDatabase=sonar

# Deploy Trivy for container scanning
helm repo add aquasecurity https://aquasecurity.github.io/helm-charts/
helm repo update
helm upgrade --install trivy aquasecurity/trivy \
  --namespace security \
  --set trivy.ignoreUnfixed=true \
  --set trivy.severity=CRITICAL,HIGH

# Deploy Falco for runtime security
helm repo add falcosecurity https://falcosecurity.github.io/charts
helm repo update
helm upgrade --install falco falcosecurity/falco \
  --namespace security \
  --set falco.programOutput.enabled=true \
  --set falco.programOutput.program="curl -d @- -X POST http://falco-webhook:8080"

# Deploy Wazuh for security monitoring
helm repo add wazuh https://packages.wazuh.com/helm
helm repo update
helm upgrade --install wazuh wazuh/wazuh \
  --namespace security \
  --set wazuh.manager.replicas=3 \
  --set wazuh.indexer.replicas=3

# Deploy Vault for secrets management
helm repo add hashicorp https://helm.releases.hashicorp.com
helm repo update
helm upgrade --install vault hashicorp/vault \
  --namespace security \
  --set server.dev.enabled=true \
  --set server.standalone.enabled=true

# Deploy testing tools
echo "🧪 Deploying Testing Tools..."

# Deploy k6 for performance testing
helm repo add k6 https://grafana.github.io/helm-charts
helm repo update
helm upgrade --install k6 k6/k6 \
  --namespace testing \
  --set service.type=ClusterIP

# Deploy Cypress for E2E testing
kubectl apply -f - <<EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cypress
  namespace: testing
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cypress
  template:
    metadata:
      labels:
        app: cypress
    spec:
      containers:
      - name: cypress
        image: cypress/included:12.3.0
        command: ["cypress", "run"]
EOF

# Deploy Chaos Mesh for chaos testing
helm repo add chaos-mesh https://charts.chaos-mesh.org
helm repo update
helm upgrade --install chaos-mesh chaos-mesh/chaos-mesh \
  --namespace testing \
  --set chaosDaemon.runtime=containerd \
  --set chaosDaemon.socketPath=/run/containerd/containerd.sock

# Apply security configurations
echo "🔒 Applying Security Configurations..."

# Apply network policies
kubectl apply -f security/network-policies.yaml

# Apply pod security standards
kubectl apply -f security/pod-security-standards.yaml

# Apply comprehensive security scanning configuration
kubectl apply -f security/comprehensive-security-scanning.yaml

# Apply test strategy configuration
kubectl apply -f testing/test-strategy.yaml

# Set up monitoring and alerting
echo "📈 Setting up Monitoring and Alerting..."

# Deploy Prometheus for metrics
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring \
  --set grafana.enabled=true \
  --set alertmanager.enabled=true

# Configure alerting rules
kubectl apply -f - <<EOF
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: security-alerts
  namespace: monitoring
spec:
  groups:
  - name: security
    rules:
    - alert: HighSeverityVulnerability
      expr: trivy_vulnerabilities{severity="CRITICAL"} > 0
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: High severity vulnerability detected
        description: "{{ \$value }} critical vulnerabilities found"
    - alert: FailedSecurityScan
      expr: security_scan_failures > 0
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: Security scan failed
        description: "{{ \$value }} security scans failed"
EOF

echo "✅ Testing and Security Infrastructure Deployment Complete!"

# Print access information
echo "
🔍 Access Information:
-------------------
SonarQube: http://sonarqube.testing.svc.cluster.local:9000
Trivy Scanner: http://trivy.security.svc.cluster.local:8080
Wazuh Dashboard: http://wazuh.security.svc.cluster.local:5601
Vault UI: http://vault.security.svc.cluster.local:8200
Grafana: http://grafana.monitoring.svc.cluster.local:3000
"

echo "📝 Next Steps:"
echo "1. Configure SonarQube quality gates"
echo "2. Set up Vault secrets and policies"
echo "3. Configure Wazuh rules and alerts"
echo "4. Set up CI/CD pipeline integration"
echo "5. Configure security scanning schedules" 