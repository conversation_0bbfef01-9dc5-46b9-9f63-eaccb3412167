apiVersion: testing.strategy/v1
kind: TestStrategy
metadata:
  name: comprehensive-test-strategy
spec:
  unitTests:
    enabled: true
    frameworks:
      - name: pytest
        version: "7.4.0"
      - name: jest
        version: "29.5.0"
    coverage:
      minimum: 80
      exclude:
        - "**/migrations/**"
        - "**/tests/**"
        - "**/vendor/**"
  
  integrationTests:
    enabled: true
    types:
      - api
      - database
      - service
    environments:
      - name: staging
        replicas: 1
      - name: pre-prod
        replicas: 2
  
  e2eTests:
    enabled: true
    framework: cypress
    version: "12.3.0"
    scenarios:
      - tenant-onboarding
      - tenant-offboarding
      - resource-scaling
      - cost-management
      - security-compliance
  
  performanceTests:
    enabled: true
    tools:
      - name: k6
        version: "0.45.0"
      - name: locust
        version: "2.15.1"
    scenarios:
      - name: load-test
        users: 1000
        duration: 1h
      - name: stress-test
        users: 5000
        duration: 2h
      - name: spike-test
        users: 10000
        duration: 30m
  
  securityTests:
    enabled: true
    tools:
      - name: sonarqube
        version: "9.9"
      - name: trivy
        version: "0.40.0"
      - name: zap
        version: "2.12.0"
    scanTypes:
      - static-analysis
      - dependency-check
      - container-scanning
      - network-scanning
      - api-security
  
  chaosTests:
    enabled: true
    tools:
      - name: chaos-mesh
        version: "2.5.0"
    scenarios:
      - pod-failure
      - network-latency
      - node-failure
      - resource-exhaustion
  
  testEnvironments:
    - name: local
      type: minikube
    - name: staging
      type: eks
    - name: pre-prod
      type: eks
    - name: prod
      type: eks
  
  reporting:
    enabled: true
    tools:
      - name: allure
        version: "2.24.0"
      - name: junit
        version: "1.0.0"
    destinations:
      - type: s3
        bucket: test-reports
      - type: elasticsearch
        index: test-results 