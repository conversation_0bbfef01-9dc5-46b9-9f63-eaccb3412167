apiVersion: k6.io/v1alpha1
kind: K6
metadata:
  name: performance-test
  namespace: testing
spec:
  parallelism: 3
  script:
    configMap:
      name: k6-test-script
      file: test.js
  runner:
    image: loadimpact/k6:latest
    resources:
      limits:
        cpu: "1"
        memory: "1Gi"
      requests:
        cpu: "500m"
        memory: "512Mi"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: k6-test-script
  namespace: testing
data:
  test.js: |
    import http from 'k6/http';
    import { check, sleep } from 'k6';
    import { Rate } from 'k6/metrics';

    const errorRate = new Rate('errors');

    export const options = {
      stages: [
        { duration: '1m', target: 50 },  // Ramp up to 50 users
        { duration: '3m', target: 50 },  // Stay at 50 users
        { duration: '1m', target: 100 }, // Ramp up to 100 users
        { duration: '3m', target: 100 }, // Stay at 100 users
        { duration: '1m', target: 0 },   // Ramp down to 0 users
      ],
      thresholds: {
        'http_req_duration': ['p(95)<500'], // 95% of requests should be below 500ms
        'errors': ['rate<0.1'],             // Error rate should be below 10%
      },
    };

    export default function() {
      const response = http.get('http://api.architrave.com/health');
      check(response, {
        'is status 200': (r) => r.status === 200,
        'response time < 500ms': (r) => r.timings.duration < 500,
      });
      errorRate.add(response.status !== 200);
      sleep(1);
    }
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: performance-alerts
  namespace: monitoring
spec:
  groups:
  - name: performance.rules
    rules:
    - alert: HighLatency
      expr: http_request_duration_seconds{quantile="0.95"} > 0.5
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: High latency detected
        description: "95th percentile latency is above 500ms"
    - alert: HighErrorRate
      expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
      for: 5m
      labels:
        severity: critical
      annotations:
        summary: High error rate detected
        description: "Error rate is above 10%"
    - alert: SlowResponseTime
      expr: rate(http_request_duration_seconds_sum[5m]) / rate(http_request_duration_seconds_count[5m]) > 1
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: Slow response time detected
        description: "Average response time is above 1 second" 