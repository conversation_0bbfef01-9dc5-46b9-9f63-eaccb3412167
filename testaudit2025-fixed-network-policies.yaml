---
# Allow Istio ingress traffic - CORRECTED VERSION
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: testaudit2025-istio-ingress-fixed
  namespace: tenant-testaudit2025
  labels:
    tenant: testaudit2025
    policy-type: ingress
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  ingress:
  # Allow traffic from Istio system namespace
  - from:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-system
  # Allow traffic from load balancer (external)
  - from: []
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 15021

---
# Allow internal communication within tenant namespace
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: testaudit2025-internal-allow-fixed
  namespace: tenant-testaudit2025
  labels:
    tenant: testaudit2025
    policy-type: internal
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: tenant-testaudit2025
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: tenant-testaudit2025
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  # Allow database access
  - to: []
    ports:
    - protocol: TCP
      port: 3306
    - protocol: TCP
      port: 443

---
# Allow monitoring and health checks
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: testaudit2025-monitoring-fixed
  namespace: tenant-testaudit2025
  labels:
    tenant: testaudit2025
    policy-type: monitoring
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: kube-system
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: keda
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-system
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 15672
    - protocol: TCP
      port: 15021
