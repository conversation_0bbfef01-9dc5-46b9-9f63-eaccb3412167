/**
 * # Advanced Features Integration
 *
 * This file shows how to integrate the advanced features into your main Terraform configuration.
 * Add this content to your main.tf or create a new file in your root module.
 */

# Deploy advanced monitoring - DISABLED due to configuration conflicts
# module "advanced_monitoring" {
#   source = "./modules/advanced_monitoring"
#   # ... configuration disabled
# }

# Update autoscaling module to include advanced features
# This is commented out to avoid duplicate module definition with main.tf
# module "autoscaling" {
#   source = "./modules/autoscaling"
#   ...
# }

# Deploy Kubernetes manifests using kubectl provider - DISABLED due to missing YAML files
# resource "kubectl_manifest" "service_mesh_mtls" {
#   # ... configuration disabled
# }
# resource "kubectl_manifest" "service_mesh_authorization" {
#   # ... configuration disabled
# }
# resource "kubectl_manifest" "service_mesh_gateway" {
#   # ... configuration disabled
# }
# resource "kubectl_manifest" "tenant_scaling_policies" {
#   # ... configuration disabled
# }

# Commented out due to missing files
# resource "kubectl_manifest" "tenant_resource_quotas" {
#   count     = var.skip_k8s_connection ? 0 : 1
#   yaml_body = file("${path.module}/kubernetes/tenant-essentials/tenant-resource-quotas.yaml")
#
#   depends_on = [
#     module.eks # Assuming you have an EKS module
#   ]
# }
#
# resource "kubectl_manifest" "tenant_audit_logging" {
#   count     = var.skip_k8s_connection ? 0 : 1
#   yaml_body = file("${path.module}/kubernetes/tenant-essentials/tenant-audit-logging.yaml")
#
#   depends_on = [
#     module.eks # Assuming you have an EKS module
#   ]
# }

# kubectl provider configuration is now in provider.tf

# Add the helm provider if not already present
# This is commented out to avoid duplicate provider definition with provider.tf
# provider "helm" {
#   kubernetes {
#     host                   = data.aws_eks_cluster.cluster.endpoint
#     cluster_ca_certificate = base64decode(data.aws_eks_cluster.cluster.certificate_authority[0].data)
#     token                  = data.aws_eks_cluster_auth.cluster.token
#   }
# }
