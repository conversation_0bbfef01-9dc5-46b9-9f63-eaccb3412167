# Local variable to control whether to look up resources
locals {
  # Always look up resources
  lookup_resources = true

  # Check if EKS cluster name is provided
  eks_cluster_name_provided = var.cluster_name != null && var.cluster_name != ""

  # Bastion instance details with improved lookups
  bastion_public_ip   = try(data.aws_instance.bastion_instance[0].public_ip, "")
  bastion_private_ip  = try(data.aws_instance.bastion_instance[0].private_ip, "")
  bastion_instance_id = try(data.aws_instance.bastion_instance[0].id, "")

  # EKS cluster details using module outputs
  eks_cluster_endpoint = try(module.eks.cluster_endpoint, "")
  eks_cluster_id       = try(module.eks.cluster_id, "")
  eks_cluster_ca_cert  = try(module.eks.cluster_ca_certificate, "")

  # Certificate details with the actual certificate ARN as fallback
  domain_certificate_arn = try(data.aws_acm_certificate.domain_certificate[0].arn, "arn:aws:acm:eu-central-1:545009857703:certificate/b1cd7c63-d434-4e29-b766-c0fec0253a16")
}

# Improved bastion instance lookup
data "aws_instances" "bastion" {
  filter {
    name   = "tag:Name"
    values = ["${var.environment}-eks-bastion"]
  }

  filter {
    name   = "instance-state-name"
    values = ["running"]
  }
}

data "aws_instance" "bastion_instance" {
  count       = length(data.aws_instances.bastion.ids) > 0 ? 1 : 0
  instance_id = data.aws_instances.bastion.ids[0]
}

# EKS cluster lookup - only if cluster name is provided and exists
# Use module.eks outputs for dynamic referencing
# Remove hardcoded cluster name references
data "aws_eks_cluster" "eks_cluster" {
  count = local.eks_cluster_name_provided && var.eks_cluster_exists ? 1 : 0
  name  = try(module.eks.cluster_name, var.cluster_name)
}

data "aws_eks_cluster_auth" "cluster_auth" {
  count = local.eks_cluster_name_provided && var.eks_cluster_exists ? 1 : 0
  name  = try(module.eks.cluster_name, var.cluster_name)
}

# ACM certificate lookup
data "aws_acm_certificate" "domain_certificate" {
  count       = var.domain_name != "" ? 1 : 0
  domain      = "*.${var.domain_name}"
  statuses    = ["ISSUED"]
  most_recent = true
}

# Note: We're using the certificate ARN from the locals block if the lookup fails

# Output for bastion details
output "bastion_private_ip" {
  value       = local.bastion_private_ip
  description = "Private IP of the bastion host"
}

output "bastion_instance_id" {
  value       = local.bastion_instance_id
  description = "Instance ID of the bastion host"
}

output "cluster_endpoint" {
  value       = local.eks_cluster_endpoint
  description = "Endpoint for the EKS cluster"
}

output "cluster_id" {
  value       = local.eks_cluster_id
  description = "ID of the EKS cluster"
}

output "domain_certificate_arn" {
  value       = local.domain_certificate_arn
  description = "ARN of the domain certificate"
}

output "eks_cluster_endpoint" {
  value       = local.eks_cluster_endpoint
  description = "Endpoint for the EKS cluster"
}

output "eks_cluster_ca_cert" {
  value       = local.eks_cluster_ca_cert
  description = "Certificate authority data for the EKS cluster"
  sensitive   = true
}

output "eks_cluster_auth_token" {
  value       = try(module.eks.cluster_auth_token, "")
  description = "Authentication token for the EKS cluster"
  sensitive   = true
}
