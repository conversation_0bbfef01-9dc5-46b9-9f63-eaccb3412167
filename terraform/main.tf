# Modified main.tf with updated module configurations

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

module "vpc" {
  source              = "./modules/vpc"
  region              = var.aws_region
  environment         = var.environment
  vpc_cidr            = var.vpc_cidr
  public_subnet_cidrs = var.public_subnets
  private_subnets     = var.private_subnets
  azs                 = ["${var.aws_region}a", "${var.aws_region}b", "${var.aws_region}c"]
  tags                = merge(var.tags, local.vpc_settings.tags)

  # Pass EKS cluster information - will be populated after EKS cluster creation
  cluster_name       = var.cluster_name
  cluster_endpoint   = try(module.eks.cluster_endpoint, "")
  cluster_ca_cert    = try(module.eks.cluster_ca_certificate, "")
  cluster_auth_token = try(module.eks.cluster_auth_token, "")

  # DNS settings are hardcoded in the VPC module
}

# IAM roles for EKS cluster are now created in the EKS module

# Create EKS cluster
module "eks" {
  source = "./modules/eks"

  vpc_cidr                  = var.vpc_cidr
  cluster_name              = var.cluster_name
  subnet_ids                = module.vpc.private_subnet_ids
  vpc_id                    = module.vpc.vpc_id
  environment               = var.environment
  kubernetes_version        = "1.33"
  tags                      = var.tags
  bastion_public_ip         = local.bastion_public_ip != "" ? local.bastion_public_ip : var.bastion_public_ip
  bastion_security_group_id = module.bastion.security_group_id

  # Security improvements
  kms_key_arn             = module.kms.primary_key_arn # Use KMS key for EKS encryption
  enable_network_policies = true                       # Enable network policies for pod-to-pod communication
  endpoint_public_access  = true                       # Enable public access to EKS API for bastion host
  endpoint_private_access = true                       # Enable private access to EKS API

  # Node configuration - unlimited tenant capacity
  instance_types     = ["t3a.large", "t3a.xlarge", "m5.large", "m5.xlarge", "m5.2xlarge", "m5.4xlarge"] # Multiple instance types for flexibility
  node_instance_type = "t3a.large"   # Use larger instance type for more pods
  desired_size       = 5             # Start with 5 nodes for stability
  max_size           = 200           # Increased to 200 nodes for higher autoscaling
  min_size           = 3             # Minimum 3 nodes for high availability

  # Backend pod role configuration
  create_backend_pod_role = false # Disable backend pod role creation to avoid count dependency issues

  # Unlimited spot instances for unlimited tenant capacity
  enable_spot_instances = true        # Enable spot instances for non-critical workloads
  spot_instance_type    = "t3a.large" # Use t3a.large for spot instances
  spot_desired_size     = 2           # Start with 2 spot instances for immediate capacity
  spot_max_size         = 200         # Increased to 200 for higher autoscaling
  spot_min_size         = 1           # Minimum 1 for cost optimization

  # Performance optimization
  enable_cluster_autoscaler = true # Enable cluster autoscaler for automatic scaling

  # Create new EKS cluster and resources
  create_eks                     = true # Create new EKS cluster
  create_iam_role                = true # Create new IAM roles
  create_security_groups         = true # Create new security groups
  create_node_groups             = true # Create new node groups
  create_iam_policies            = true # Create new IAM policies
  create_eks_log_group           = true # Create new CloudWatch log group
  eks_cluster_iam_role_arn       = local.eks_cluster_role_arn
  validate_network               = false                                                                         # Disable network validation for now
  validate_subnet_cidrs          = var.private_subnets                                                           # Use the predefined subnets
  create_endpoints               = false                                                                         # Control VPC endpoint creation
  enable_vpc_endpoint_validation = false                                                                         # Disable VPC endpoint validation
  skip_eks_connectivity_check    = true                                                                          # Skip EKS connectivity check
  route_table_ids                = concat(module.vpc.private_route_table_ids, module.vpc.public_route_table_ids) # Route tables for VPC endpoints
  create_s3_vpc_endpoint         = false                                                                         # Let the VPC module create the S3 endpoint
  check_if_cluster_exists        = false                                                                         # Don't check if cluster exists
  create_oidc_provider           = true                                                                          # Create new OIDC provider
  skip_kubernetes_resources      = true                                                                          # Skip Kubernetes resources

  # Connect to the bastion host
  bastion_role_arn = "arn:aws:iam::545009857703:role/production-eks-bastion-role"

  # Unlimited disk size for unlimited pods per node
  node_disk_size     = 100  # Increased disk for unlimited pods per node

  # No explicit dependency needed - references to VPC outputs create implicit dependency
}


# Create a temporary IAM role for backend pods
# This role is used by the KMS module since we're not creating the backend pod role in the EKS module
resource "aws_iam_role" "temp_backend_pod_role" {
  name = "${var.environment}-temp-backend-pod-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "eks.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

# Configure KMS module
module "kms" {
  source      = "./modules/kms"
  environment = var.environment
  tags        = var.tags

  # Use the temporary role since EKS module doesn't provide one yet
  backend_pod_role_arn = aws_iam_role.temp_backend_pod_role.arn

  tenants            = var.tenants != null ? var.tenants : {}
  administrator_arns = var.kms_administrator_arns != null ? var.kms_administrator_arns : []
  user_arns          = var.kms_user_arns != null ? var.kms_user_arns : []

  key_deletion_window_in_days = var.kms_key_deletion_window_in_days != null ? var.kms_key_deletion_window_in_days : 7
}

module "security" {
  source      = "./modules/security"
  environment = var.environment
  vpc_id      = module.vpc.vpc_id
  vpc_cidr    = var.vpc_cidr
  tags        = var.tags
}

module "rds" {
  source              = "./modules/rds"
  environment         = var.environment
  vpc_id              = module.vpc.vpc_id
  subnet_ids          = module.vpc.private_subnet_ids
  tenant_kms_key_arns = {} # Empty map since KMS keys have been disabled
  security_group_id   = module.security.rds_security_group_id
  tags                = var.tags
  kms_key_arn         = null # KMS keys removed to avoid deletion protection
  enable_proxy        = true # Enable RDS Proxy for connection pooling and additional security
  skip_final_snapshot = true # Skip final snapshot for easier development and testing

  # RDS configuration from terraform.tfvars
  instance_class          = var.rds_instance_class
  engine_version          = var.rds_engine_version
  backup_retention_period = var.db_backup_retention_period
  multi_az                = var.db_multi_az
  deletion_protection     = var.db_deletion_protection

  # Aurora Serverless v2 configuration
  use_aurora_serverless = true # Enable Aurora Serverless v2
  aurora_min_capacity   = 0.5  # Minimum capacity in ACUs
  aurora_max_capacity   = 16   # Maximum capacity in ACUs
  aurora_instance_count = 2    # 1 writer + 1 reader
  availability_zones    = ["eu-central-1a", "eu-central-1b", "eu-central-1c"]

  # Set import_existing_resources to false to create new resources
  import_existing_resources = false
}

# Fixed Route53 module configuration
module "route53" {
  source      = "./modules/route53"
  environment = var.environment
  domain_name = var.domain_name
  create_zone = true # Create a new zone
  vpc_id      = var.private_zone ? module.vpc.vpc_id : ""
  tags        = var.tags
}

# ACM Certificate module
module "acm" {
  source          = "./modules/acm"
  domain_name     = var.domain_name
  route53_zone_id = module.route53.zone_id
  environment     = var.environment
  tags            = var.tags
}

# Application Load Balancer module
module "alb" {
  source             = "./modules/alb"
  environment        = var.environment
  vpc_id             = module.vpc.vpc_id
  public_subnet_ids  = module.vpc.public_subnet_ids
  private_subnet_ids = module.vpc.private_subnet_ids
  vpc_subnet_ids     = module.vpc.public_subnet_ids # Use public subnets as fallback
  certificate_arn    = var.certificate_arn != "" ? var.certificate_arn : module.acm.certificate_arn
  domain_name        = var.domain_name
  route53_zone_id    = module.route53.zone_id
  tags               = var.tags
  alb_exists         = false # Set to false since we've deleted the ALB
  vpc_cidr           = var.vpc_cidr
  enable_access_logs = var.enable_access_logs # Pass the enable_access_logs variable to the ALB module

  # Security enhancements - restrict access to specific CIDR blocks
  # Use allowed_cidr_blocks if defined, otherwise use an empty list (which will default to VPC CIDR)
  allowed_http_cidr_blocks  = var.allowed_cidr_blocks
  allowed_https_cidr_blocks = var.allowed_cidr_blocks
}

# Specific module has been permanently removed
# Certain services are not permitted in this infrastructure

module "dynamodb" {
  source                = "./modules/dynamodb"
  environment           = var.environment
  eks_oidc_provider_url = try(module.eks.oidc_provider_url, "")
  namespace             = "default"
  serviceaccount        = "tenant-app-sa"
  kms_key_arn           = null # KMS keys removed to avoid deletion protection
  tags                  = var.tags
}

# Security tools module for Trivy, Falco, Fluentd, and Istio
# Security tools module for Trivy, Falco, Fluentd, and Istio
module "security_tools" {
  source         = "./modules/security_tools"
  cluster_name   = var.cluster_name
  environment    = var.environment
  enable_trivy   = true
  enable_falco   = true
  enable_fluentd = true
  enable_istio   = true # Enabled with optimized resources
  kms_key_arn    = module.kms.primary_key_arn

  # Configure ECR repositories for scanning
  # These repositories already exist, so we'll just update their scanning configuration
  ecr_repositories = {
    "nginx_dev"        = {},
    "webapp_dev"       = {},
    "rabbitmq_dev"     = {},
    "webapp_queue_dev" = {}
  }

  # Skip creating new repositories since they already exist
  skip_existing_resources = true

  # Add alert emails for notifications
  alert_emails = var.alert_emails

  tags = var.tags
}

# AWS Security Hub module
module "security_hub" {
  source                     = "./modules/security_hub"
  environment                = var.environment
  skip_security_hub_creation = true # Skip creation since it already exists
  alert_emails               = var.alert_emails
  tags                       = var.tags
  kms_key_arn                = module.kms.primary_key_arn
}

# AWS GuardDuty module
module "guardduty" {
  source                      = "./modules/guardduty"
  environment                 = var.environment
  alert_emails                = var.alert_emails
  tags                        = var.tags
  skip_publishing_destination = true # Skip creating the publishing destination to avoid permission issues
  kms_key_arn                 = module.kms.primary_key_arn
}

# AWS CloudTrail module - DISABLED due to CloudWatch Logs permissions issue
# module "cloudtrail" {
#   count              = var.enable_cloudtrail ? 1 : 0
#   source             = "./modules/cloudtrail"
#   environment        = var.environment
#   alert_emails       = var.alert_emails
#   log_retention_days = 90
#   force_destroy      = true # Enable force destroy for easier cleanup during development
#   tags               = var.tags
#   kms_key_arn        = module.kms.primary_key_arn
# }

# AWS Inspector module
module "inspector" {
  count        = var.enable_inspector ? 1 : 0
  source       = "./modules/inspector"
  environment  = var.environment
  alert_emails = var.alert_emails
  kms_key_arn  = module.kms.primary_key_arn
  tags         = var.tags
}

# AWS Config module
module "aws_config" {
  source                    = "./modules/aws_config"
  environment               = var.environment
  tags                      = var.tags
  skip_config_creation      = false # Always create AWS Config to comply with security best practices
  import_existing_resources = var.import_existing_resources
  kms_key_arn               = module.kms.primary_key_arn
}

# Bastion host module
module "bastion" {
  source      = "./modules/bastion"
  environment = var.environment

  # Required parameters
  subnet_id = module.vpc.public_subnet_ids[0] # Choose first public subnet
  vpc_id    = module.vpc.vpc_id
  vpc_cidr  = var.vpc_cidr
  region    = var.aws_region

  # EKS cluster information
  eks_cluster_name = var.cluster_name

  # Security settings - allow SSH access from anywhere for bastion host
  allowed_ssh_cidr_blocks = ["0.0.0.0/0"] # Allow SSH access from anywhere
  enable_ssh_access       = true

  # Set a monitoring CIDR - adjust as needed
  monitoring_bastion_cidr = var.vpc_cidr

  # Pass through the feature flag variables from root
  enable_vpc_endpoints         = false # Use existing VPC endpoints
  enable_cloudwatch_encryption = var.enable_cloudwatch_encryption
  enable_container_insights    = false # Disabled to avoid IAM role issues
  skip_eks_connectivity_check  = true # Skip EKS connectivity check

  # Other settings
  create_ssm_endpoint   = false # Use existing SSM endpoint
  existing_ssm_endpoint = true

  # Use existing key pair
  ssh_key_name = "production-bastion-key"

  # Use existing resources
  create_ssh_key            = false
  import_existing_resources = true

  tags = var.tags
}

# Keep your existing locals block
locals {
  eks_ready                = fileexists("${path.module}/.eks_ready")
  skip_k8s                 = var.is_ci_cd ? false : true
  tenant_state             = "onboard"
  tenant_id                = var.customer_id
  fqdn                     = var.domain_name
  app_tenant               = "tenant-app"
  app_tenant_email         = "<EMAIL>"
  app_tenant_support_email = "<EMAIL>"
  app_sysadmin_name        = "admin"
  app_sysadmin_email       = "<EMAIL>"
  app_support_email        = "<EMAIL>"
  tenant_details_directory = "./tenant-details"
  image_tag                = "latest"
  # Removed duplicate bastion_public_ip local value - already defined in data.tf

  # Define module names being used for validation
  used_modules = ["vpc", "eks", "kms", "security", "rds", "route53", "dynamodb", "security_tools"]

  # vpc_settings is defined in vpc_config.tf
}

# Autoscaling module for HPA, VPA, and PDB - only include when not in CI/CD mode
module "autoscaling" {
  count                 = var.is_ci_cd ? 0 : 1
  source                = "./modules/autoscaling"
  cluster_name          = var.cluster_name
  environment           = var.environment
  eks_oidc_provider_url = module.eks.oidc_provider_url
  cluster_endpoint      = module.eks.cluster_endpoint

  # Enhanced Cluster Autoscaler configuration for unlimited tenant capacity
  deploy_cluster_autoscaler        = true
  cluster_autoscaler_namespace     = "kube-system"
  cluster_autoscaler_role_arn      = module.eks.cluster_autoscaler_role_arn
  cluster_autoscaler_version       = "9.21.0"

  # Ultra-aggressive scaling configuration for unlimited tenant onboarding
  scale_down_delay_after_add       = "3m"    # Ultra-fast scale-down evaluation
  scale_down_unneeded_time         = "3m"    # Ultra-fast node removal
  scan_interval                    = "5s"    # Ultra-frequent evaluation
  max_node_provision_time          = "8m"    # Faster node provisioning timeout
  scale_down_utilization_threshold = "0.3"   # Ultra-aggressive scale-down
  expander_strategy                = "priority" # Priority-based expansion

  # Unlimited capacity limits for unlimited tenant support (400 total nodes)
  max_nodes_total                  = "400"    # 200 main + 200 spot = 400 total nodes
  cores_total                      = "10000:100000"  # Higher core capacity
  memory_total                     = "20000Gi:400000Gi"  # Higher memory capacity

  # Enhanced resource allocation for cluster autoscaler
  cluster_autoscaler_cpu_request   = "100m"
  cluster_autoscaler_memory_request = "600Mi"
  cluster_autoscaler_cpu_limit     = "200m"
  cluster_autoscaler_memory_limit  = "1Gi"

  # Unlimited Karpenter configuration for unlimited tenant capacity
  install_karpenter       = true
  karpenter_chart_version = "0.16.3"
  instance_types          = ["t3a.large", "t3a.xlarge", "t3a.2xlarge", "m5.large", "m5.xlarge", "m5.2xlarge", "m5.4xlarge", "m5.8xlarge", "m5.12xlarge", "m5.16xlarge", "m5.24xlarge"]
  max_cpu                 = "50000"  # Unlimited CPU capacity
  max_memory              = "200000Gi" # Unlimited memory capacity

  # VPA configuration
  install_vpa       = true
  vpa_chart_version = "1.4.0"

  # KEDA configuration
  install_keda       = true
  keda_chart_version = "2.8.2"

  # Goldilocks configuration
  install_goldilocks       = true
  goldilocks_chart_version = "4.8.1"

  # PDB configuration
  create_default_pdbs   = true
  min_available_percent = 50
  tenant_namespaces     = ["tenant-system"]

  # Tenant-specific scaling configuration (disabled for initial deployment)
  tenant_namespaces_set = toset([])  # Disabled for initial deployment
  tenant_scaling_enabled = false     # Disabled for initial deployment
  max_tenants_per_node   = 8         # Increased for higher density
  tenant_cpu_limit       = "2"
  tenant_memory_limit    = "4Gi"

  # Add dependency on EKS module
  depends_on = [module.eks]
}

# Create tenant-specific dashboards for each tenant - DISABLED due to configuration conflicts
# module "tenant_dashboards" {
#   # ... configuration disabled
# }

# Service Mesh Implementation
module "service_mesh" {
  count  = var.is_ci_cd ? 0 : 1
  source = "./modules/service_mesh"

  # Required arguments
  environment                            = var.environment
  name                                   = "istio"
  region                                 = var.region
  eks_cluster_id                         = module.eks.cluster_id
  eks_cluster_endpoint                   = module.eks.cluster_endpoint
  eks_cluster_certificate_authority_data = module.eks.cluster_ca_certificate
  eks_oidc_provider_arn                  = module.eks.oidc_provider_arn

  namespace        = "istio-system"
  create_namespace = true

  # Skip Kubernetes connection due to connectivity issues
  skip_k8s_connection = true

  # Istio configuration
  istio_version = "1.16.1"

  # Ensure no deletion protection is enabled for testing

  # Resource configuration - highly optimized for pod limit constraints
  pilot_resources = {
    requests = {
      cpu    = "50m"
      memory = "64Mi"
    }
    limits = {
      cpu    = "200m"
      memory = "256Mi"
    }
  }

  proxy_resources = {
    requests = {
      cpu    = "5m"
      memory = "32Mi"
    }
    limits = {
      cpu    = "50m"
      memory = "64Mi"
    }
  }

  # Gateway configuration
  deploy_ingress_gateway       = true
  ingress_gateway_service_type = "LoadBalancer"
  ingress_gateway_internal     = true

  # Optimize ingress gateway resources
  ingress_gateway_resources = {
    requests = {
      cpu    = "50m"
      memory = "64Mi"
    }
    limits = {
      cpu    = "200m"
      memory = "256Mi"
    }
  }

  # Visualization and monitoring
  enable_kiali      = true
  kiali_version     = "1.57.1"
  kiali_hostname    = "kiali.${var.domain_name}"
  prometheus_url    = "http://prometheus-server.monitoring.svc.cluster.local:80"
  grafana_url       = "http://prometheus-grafana.monitoring.svc.cluster.local:80"
  enable_prometheus = true
  enable_grafana    = true

  # Tracing configuration
  enable_tracing  = true
  enable_jaeger   = true
  jaeger_version  = "0.47.0"
  jaeger_hostname = "jaeger.${var.domain_name}"

  # Application namespaces
  app_namespaces = [
    "default",
    "tenant-app"
  ]

  # Namespaces to inject Istio sidecar
  namespaces_to_inject = [
    "default",
    "tenant-app"
  ]

  # Security configuration
  enable_mtls = true
  mtls_mode   = "PERMISSIVE"

  # Gateway configuration
  create_gateway = true
  gateway_name   = "default-gateway"
  gateway_hosts  = ["*"]

  # Grafana dashboards
  create_grafana_dashboard = true
  grafana_namespace        = "monitoring"

  # Skip existing resources to avoid errors
  skip_existing_resources = true
}

# KEDA (Kubernetes Event-Driven Autoscaling) module - DISABLED due to Kubernetes connectivity issues
# module "keda" {
#   count  = var.is_ci_cd ? 0 : 1
#   source = "./modules/keda"

#   environment      = var.environment
#   namespace        = "keda"
#   create_namespace = true
#
#   # Enable Kubernetes connection
#   skip_k8s_connection = false
#
#   # KEDA configuration
#   keda_version = "2.10.2"
#
#   # Configure KEDA scaled objects
#   cpu_scaled_objects = {
#     "tenant-api" = {
#       namespace        = "tenant-system"
#       min_replicas     = 2
#       max_replicas     = 10
#       polling_interval = 15
#       cooldown_period  = 300
#       cpu_threshold    = 70
#     }
#   }
#
#   memory_scaled_objects = {
#     "tenant-api" = {
#       namespace        = "tenant-system"
#       min_replicas     = 2
#       max_replicas     = 10
#       polling_interval = 15
#       cooldown_period  = 300
#       memory_threshold = 70
#     }
#   }
#
#   prometheus_scaled_objects = {
#     "tenant-queue-processor" = {
#       namespace         = "tenant-system"
#       min_replicas      = 1
#       max_replicas      = 20
#       polling_interval  = 15
#       cooldown_period   = 300
#       prometheus_server = "http://prometheus-server.monitoring.svc.cluster.local:9090"
#       metric_name       = "rabbitmq_queue_messages"
#       query             = "sum(rabbitmq_queue_messages{queue=\"tenant_tasks\"})"
#       threshold         = 10
#     }
#   }
#
#   # Add tags
#   tags = var.tags
# }
