# Kubernetes Monitoring Stack with Prometheus and Grafana

# Only deploy Helm charts when skip_k8s_connection is false
resource "helm_release" "prometheus_operator" {
  count            = 0 # Disable Prometheus Operator
  name             = "prometheus-operator"
  repository       = "https://prometheus-community.github.io/helm-charts"
  chart            = "kube-prometheus-stack"
  namespace        = "monitoring"
  create_namespace = true
  version          = "45.7.1" # Specify a stable version

  # Use the safe provider alias that won't try to connect during initial deployment
  provider = helm.safe

  set {
    name  = "grafana.enabled"
    value = "true"
  }

  set {
    name  = "prometheus.enabled"
    value = "true"
  }

  set {
    name  = "alertmanager.enabled"
    value = "true"
  }

  # Configure persistent storage for Prometheus
  set {
    name  = "prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.accessModes[0]"
    value = "ReadWriteOnce"
  }

  set {
    name  = "prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage"
    value = "50Gi"
  }

  # Configure persistent storage for Grafana
  set {
    name  = "grafana.persistence.enabled"
    value = "true"
  }

  set {
    name  = "grafana.persistence.size"
    value = "10Gi"
  }

  # Configure Grafana admin credentials
  set {
    name  = "grafana.adminPassword"
    value = var.grafana_admin_password
  }

  # Enable RDS monitoring via CloudWatch exporter
  set {
    name  = "prometheus.prometheusSpec.additionalScrapeConfigs[0].job_name"
    value = "cloudwatch"
  }

  # Configure Grafana dashboards
  set {
    name  = "grafana.dashboardProviders.dashboardproviders\\.yaml.apiVersion"
    value = "1"
  }

  set {
    name  = "grafana.dashboardProviders.dashboardproviders\\.yaml.providers[0].name"
    value = "default"
  }

  set {
    name  = "grafana.dashboardProviders.dashboardproviders\\.yaml.providers[0].orgId"
    value = "1"
  }

  set {
    name  = "grafana.dashboardProviders.dashboardproviders\\.yaml.providers[0].folder"
    value = "Kubernetes"
  }

  set {
    name  = "grafana.dashboardProviders.dashboardproviders\\.yaml.providers[0].type"
    value = "file"
  }

  set {
    name  = "grafana.dashboardProviders.dashboardproviders\\.yaml.providers[0].disableDeletion"
    value = "false"
  }

  set {
    name  = "grafana.dashboardProviders.dashboardproviders\\.yaml.providers[0].options.path"
    value = "/var/lib/grafana/dashboards/kubernetes"
  }

  depends_on = [
    module.eks,
    aws_iam_role.cloudwatch_exporter_role
  ]
}

# IAM role for CloudWatch exporter
resource "aws_iam_role" "cloudwatch_exporter_role" {
  name = "${var.environment}-cloudwatch-exporter-role"

  # Use a simple assume role policy that will be updated later
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags

  # Prevent Terraform from trying to update this resource
  lifecycle {
    ignore_changes = [assume_role_policy]
  }
}

# IAM policy for CloudWatch exporter
resource "aws_iam_policy" "cloudwatch_exporter_policy" {
  name        = "${var.environment}-cloudwatch-exporter-policy"
  description = "Policy for CloudWatch exporter to access CloudWatch metrics"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "cloudwatch:ListMetrics",
          "cloudwatch:GetMetricStatistics",
          "cloudwatch:GetMetricData"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "cloudwatch_exporter_policy_attachment" {
  role       = aws_iam_role.cloudwatch_exporter_role.name
  policy_arn = aws_iam_policy.cloudwatch_exporter_policy.arn
}

# Deploy CloudWatch exporter for RDS metrics
resource "helm_release" "cloudwatch_exporter" {
  count      = 0 # Disable CloudWatch exporter
  name       = "cloudwatch-exporter"
  repository = "https://prometheus-community.github.io/helm-charts"
  chart      = "prometheus-cloudwatch-exporter"
  namespace  = "monitoring"

  # Use the safe provider alias that won't try to connect during initial deployment
  provider = helm.safe

  set {
    name  = "aws.role"
    value = aws_iam_role.cloudwatch_exporter_role.arn
  }

  set {
    name  = "serviceAccount.create"
    value = "true"
  }

  set {
    name  = "serviceAccount.name"
    value = "cloudwatch-exporter"
  }

  set {
    name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = aws_iam_role.cloudwatch_exporter_role.arn
  }

  set {
    name  = "serviceMonitor.enabled"
    value = "true"
  }

  set {
    name  = "serviceMonitor.namespace"
    value = "monitoring"
  }

  set {
    name  = "config"
    value = <<-EOT
      region: eu-central-1
      metrics:
        - aws_namespace: AWS/RDS
          aws_metric_name: CPUUtilization
          aws_dimensions: [DBInstanceIdentifier]
          aws_statistics: [Average]
        - aws_namespace: AWS/RDS
          aws_metric_name: FreeStorageSpace
          aws_dimensions: [DBInstanceIdentifier]
          aws_statistics: [Average]
        - aws_namespace: AWS/RDS
          aws_metric_name: DatabaseConnections
          aws_dimensions: [DBInstanceIdentifier]
          aws_statistics: [Average]
        - aws_namespace: AWS/RDS
          aws_metric_name: ReadLatency
          aws_dimensions: [DBInstanceIdentifier]
          aws_statistics: [Average]
        - aws_namespace: AWS/RDS
          aws_metric_name: WriteLatency
          aws_dimensions: [DBInstanceIdentifier]
          aws_statistics: [Average]
        - aws_namespace: AWS/RDS
          aws_metric_name: ReadIOPS
          aws_dimensions: [DBInstanceIdentifier]
          aws_statistics: [Average]
        - aws_namespace: AWS/RDS
          aws_metric_name: WriteIOPS
          aws_dimensions: [DBInstanceIdentifier]
          aws_statistics: [Average]
        - aws_namespace: AWS/RDS
          aws_metric_name: ReplicaLag
          aws_dimensions: [DBInstanceIdentifier]
          aws_statistics: [Average]
        - aws_namespace: AWS/RDS
          aws_metric_name: EngineUptime
          aws_dimensions: [DBInstanceIdentifier]
          aws_statistics: [Average]
    EOT
  }

  # Add dependency on module.eks to ensure the cluster exists
  depends_on = [
    module.eks,
    aws_iam_role.cloudwatch_exporter_role
  ]
}

# Deploy Loki for log aggregation
resource "helm_release" "loki" {
  count      = 1 # Enable Loki
  name       = "loki"
  repository = "https://grafana.github.io/helm-charts"
  chart      = "loki-stack"
  namespace  = "monitoring"
  version    = "2.9.10" # Specify a stable version

  # Use the safe provider alias that won't try to connect during initial deployment
  provider = helm.safe

  set {
    name  = "loki.enabled"
    value = "true"
  }

  set {
    name  = "promtail.enabled"
    value = "true"
  }

  set {
    name  = "grafana.enabled"
    value = "false" # We already have Grafana from prometheus-operator
  }

  set {
    name  = "loki.persistence.enabled"
    value = "true"
  }

  set {
    name  = "loki.persistence.size"
    value = "50Gi"
  }

  set {
    name  = "loki.config.storage_config.aws.s3"
    value = "s3://${var.aws_region}/${var.environment}-loki-logs"
  }

  set {
    name  = "serviceMonitor.enabled"
    value = "true"
  }

  depends_on = [
    module.eks,
    helm_release.prometheus_operator
  ]
}

# Deploy Jaeger for distributed tracing
resource "helm_release" "jaeger" {
  count      = 1 # Enable Jaeger
  name       = "jaeger"
  repository = "https://jaegertracing.github.io/helm-charts"
  chart      = "jaeger"
  namespace  = "monitoring"
  version    = "0.71.1" # Specify a stable version

  # Use the safe provider alias that won't try to connect during initial deployment
  provider = helm.safe

  set {
    name  = "provisionDataStore.cassandra"
    value = "false"
  }

  set {
    name  = "provisionDataStore.elasticsearch"
    value = "true"
  }

  set {
    name  = "storage.type"
    value = "elasticsearch"
  }

  set {
    name  = "elasticsearch.replicas"
    value = "1"
  }

  set {
    name  = "elasticsearch.minimumMasterNodes"
    value = "1"
  }

  set {
    name  = "elasticsearch.persistence.enabled"
    value = "true"
  }

  set {
    name  = "elasticsearch.persistence.size"
    value = "50Gi"
  }

  set {
    name  = "collector.service.zipkin.port"
    value = "9411"
  }

  depends_on = [
    module.eks,
    helm_release.prometheus_operator
  ]
}

# Dashboards are now managed through Kubernetes manifests
# See kubernetes/monitoring/grafana-dashboards-combined.yaml for the dashboard configurations
# and kubernetes/monitoring/deploy-enhanced-monitoring.sh for the deployment script