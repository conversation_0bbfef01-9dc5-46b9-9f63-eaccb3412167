/**
 * # S3 Mount Points Module
 *
 * This module implements S3 mount points for tenant asset storage.
 * It creates the necessary resources to mount S3 buckets in Kubernetes pods.
 */

terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Data sources
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

# Local variables
locals {
  name_prefix = "${var.environment}-${var.name}"
  tags = merge(
    var.tags,
    {
      "Environment" = var.environment
      "ManagedBy"   = "terraform"
      "Name"        = local.name_prefix
    }
  )
}

# Create KMS key for S3 bucket encryption
resource "aws_kms_key" "s3_key" {
  description             = "${var.environment} S3 bucket encryption key"
  deletion_window_in_days = 30
  enable_key_rotation     = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "Allow S3 to use the key"
        Effect = "Allow"
        Principal = {
          Service = "s3.amazonaws.com"
        }
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = "*"
      }
    ]
  })

  tags = local.tags
}

# Create KMS alias for S3 bucket encryption key
resource "aws_kms_alias" "s3_key_alias" {
  name          = "alias/${var.environment}-s3-key"
  target_key_id = aws_kms_key.s3_key.key_id
}

# Create S3 bucket for tenant assets
resource "aws_s3_bucket" "tenant_assets" {
  for_each = var.tenants

  bucket = "tenant-${each.key}-assets"

  tags = merge(local.tags, {
    TenantId = each.key
  })
}

# Configure S3 bucket encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "tenant_assets" {
  for_each = var.tenants

  bucket = aws_s3_bucket.tenant_assets[each.key].id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.s3_key.arn
      sse_algorithm     = "aws:kms"
    }
  }
}

# Block public access to S3 bucket
resource "aws_s3_bucket_public_access_block" "tenant_assets" {
  for_each = var.tenants

  bucket = aws_s3_bucket.tenant_assets[each.key].id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Create IAM role for S3 access
resource "aws_iam_role" "s3_access" {
  name = "${var.environment}-s3-access-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRoleWithWebIdentity"
        Effect = "Allow"
        Principal = {
          Federated = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${var.eks_oidc_provider}"
        }
        Condition = {
          StringEquals = {
            "${var.eks_oidc_provider}:sub" = "system:serviceaccount:${var.namespace}:s3-mount-sa"
          }
        }
      }
    ]
  })

  tags = local.tags
}

# Create IAM policy for S3 access
resource "aws_iam_policy" "s3_access" {
  name        = "${var.environment}-s3-access-policy"
  description = "Policy for S3 access"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "S3Access"
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ]
        Resource = concat(
          [for bucket in aws_s3_bucket.tenant_assets : bucket.arn],
          [for bucket in aws_s3_bucket.tenant_assets : "${bucket.arn}/*"]
        )
      },
      {
        Sid    = "KMSAccess"
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = [
          aws_kms_key.s3_key.arn
        ]
      }
    ]
  })
}

# Attach IAM policy to S3 access role
resource "aws_iam_role_policy_attachment" "s3_access" {
  role       = aws_iam_role.s3_access.name
  policy_arn = aws_iam_policy.s3_access.arn
}

# Create Kubernetes namespace for S3 mount
resource "kubernetes_namespace" "s3_mount" {
  count = var.create_namespace ? 1 : 0

  metadata {
    name = var.namespace

    labels = {
      "app.kubernetes.io/name"       = "s3-mount"
      "app.kubernetes.io/part-of"    = "s3-mount"
      "app.kubernetes.io/managed-by" = "terraform"
    }
  }
}

# Create Kubernetes service account for S3 mount
resource "kubernetes_service_account" "s3_mount" {
  metadata {
    name      = "s3-mount-sa"
    namespace = var.namespace

    annotations = {
      "eks.amazonaws.com/role-arn" = aws_iam_role.s3_access.arn
    }

    labels = {
      "app.kubernetes.io/name"       = "s3-mount"
      "app.kubernetes.io/part-of"    = "s3-mount"
      "app.kubernetes.io/managed-by" = "terraform"
    }
  }

  depends_on = [kubernetes_namespace.s3_mount]
}

# Create Kubernetes config map for S3 mount
resource "kubernetes_config_map" "s3_mount" {
  metadata {
    name      = "s3-mount-config"
    namespace = var.namespace

    labels = {
      "app.kubernetes.io/name"       = "s3-mount"
      "app.kubernetes.io/part-of"    = "s3-mount"
      "app.kubernetes.io/managed-by" = "terraform"
    }
  }

  data = {
    "s3-mount.sh" = <<-EOT
      #!/bin/bash
      set -e
      
      # Mount S3 bucket using s3fs
      s3fs $S3_BUCKET $MOUNT_POINT -o iam_role=auto -o url=https://s3-$AWS_REGION.amazonaws.com -o use_cache=/tmp -o allow_other -o umask=0022
      
      # Keep container running
      tail -f /dev/null
    EOT
  }

  depends_on = [kubernetes_namespace.s3_mount]
}

# Create Kubernetes deployment for S3 mount
resource "kubernetes_deployment" "s3_mount" {
  for_each = var.tenants

  metadata {
    name      = "s3-mount-${each.key}"
    namespace = var.namespace

    labels = {
      "app.kubernetes.io/name"         = "s3-mount"
      "app.kubernetes.io/part-of"      = "s3-mount"
      "app.kubernetes.io/managed-by"   = "terraform"
      "tenant.architrave.io/tenant-id" = each.key
    }
  }

  spec {
    replicas = 1

    selector {
      match_labels = {
        "app.kubernetes.io/name" = "s3-mount-${each.key}"
      }
    }

    template {
      metadata {
        labels = {
          "app.kubernetes.io/name"         = "s3-mount-${each.key}"
          "tenant.architrave.io/tenant-id" = each.key
        }
      }

      spec {
        service_account_name = kubernetes_service_account.s3_mount.metadata[0].name

        container {
          name  = "s3-mount"
          image = "amazonlinux:2"

          command = ["/bin/bash", "-c"]
          args    = ["yum install -y s3fs-fuse && /scripts/s3-mount.sh"]

          env {
            name  = "S3_BUCKET"
            value = aws_s3_bucket.tenant_assets[each.key].bucket
          }

          env {
            name  = "MOUNT_POINT"
            value = "/mnt/s3"
          }

          env {
            name  = "AWS_REGION"
            value = data.aws_region.current.name
          }

          volume_mount {
            name       = "s3-mount-point"
            mount_path = "/mnt/s3"
          }

          volume_mount {
            name       = "scripts"
            mount_path = "/scripts"
          }

          security_context {
            privileged = true
          }
        }

        volume {
          name = "s3-mount-point"
          empty_dir {}
        }

        volume {
          name = "scripts"
          config_map {
            name         = kubernetes_config_map.s3_mount.metadata[0].name
            default_mode = "0755"
          }
        }
      }
    }
  }

  depends_on = [
    kubernetes_namespace.s3_mount,
    kubernetes_service_account.s3_mount,
    kubernetes_config_map.s3_mount
  ]
}
