# Comprehensive Monitoring Module for EKS
# This module implements Promet<PERSON>, <PERSON><PERSON>, Loki, and <PERSON><PERSON><PERSON> for monitoring

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

locals {
  account_id = data.aws_caller_identity.current.account_id
  region     = data.aws_region.current.name
}

# Create namespace for monitoring
resource "kubernetes_namespace" "monitoring" {
  count = var.create_namespace ? 1 : 0
  metadata {
    name = var.namespace
    labels = {
      name              = var.namespace
      "istio-injection" = "enabled"
    }
  }
}

# Create Prometheus Operator using Helm
resource "helm_release" "prometheus_operator" {
  count            = var.install_prometheus ? 1 : 0
  name             = "prometheus-operator"
  repository       = "https://prometheus-community.github.io/helm-charts"
  chart            = "kube-prometheus-stack"
  namespace        = var.namespace
  create_namespace = false
  version          = var.prometheus_chart_version
  timeout          = 600

  values = [
    templatefile("${path.module}/templates/prometheus-values.yaml", {
      grafana_admin_password = var.grafana_admin_password
      storage_class          = var.storage_class
      prometheus_retention   = var.prometheus_retention
      prometheus_storage     = var.prometheus_storage
      alertmanager_retention = var.alertmanager_retention
      alertmanager_storage   = var.alertmanager_storage
      environment            = var.environment
      cluster_name           = var.cluster_name
    })
  ]

  depends_on = [kubernetes_namespace.monitoring]
}

# Create Loki using Helm
resource "helm_release" "loki" {
  count            = var.install_loki ? 1 : 0
  name             = "loki"
  repository       = "https://grafana.github.io/helm-charts"
  chart            = "loki-stack"
  namespace        = var.namespace
  create_namespace = false
  version          = var.loki_chart_version
  timeout          = 300

  values = [
    templatefile("${path.module}/templates/loki-values.yaml", {
      storage_class   = var.storage_class
      loki_storage    = var.loki_storage
      environment     = var.environment
      cluster_name    = var.cluster_name
      enable_promtail = var.enable_promtail
      grafana_enabled = false
    })
  ]

  depends_on = [kubernetes_namespace.monitoring, helm_release.prometheus_operator]
}

# Create Jaeger using Helm
resource "helm_release" "jaeger" {
  count            = var.install_jaeger ? 1 : 0
  name             = "jaeger"
  repository       = "https://jaegertracing.github.io/helm-charts"
  chart            = "jaeger"
  namespace        = var.namespace
  create_namespace = false
  version          = var.jaeger_chart_version
  timeout          = 300

  values = [
    templatefile("${path.module}/templates/jaeger-values.yaml", {
      storage_class     = var.storage_class
      environment       = var.environment
      cluster_name      = var.cluster_name
      storage_type      = var.jaeger_storage_type
      memory_max_traces = var.jaeger_memory_max_traces
    })
  ]

  depends_on = [kubernetes_namespace.monitoring, helm_release.prometheus_operator]
}

# Configure Grafana datasources
resource "kubernetes_config_map" "grafana_datasources" {
  count = var.install_prometheus && var.install_loki && var.install_jaeger ? 1 : 0
  metadata {
    name      = "grafana-datasources"
    namespace = var.namespace
    labels = {
      grafana_datasource = "1"
    }
  }

  data = {
    "datasources.yaml" = templatefile("${path.module}/templates/grafana-datasources.yaml", {
      prometheus_url = "http://prometheus-operated:9090"
      loki_url       = "http://loki:3100"
      jaeger_url     = "http://jaeger-query:16686"
    })
  }

  depends_on = [
    helm_release.prometheus_operator,
    helm_release.loki,
    helm_release.jaeger
  ]
}

# Create custom dashboards for Grafana
resource "kubernetes_config_map" "grafana_dashboards" {
  count = var.install_prometheus ? 1 : 0
  metadata {
    name      = "grafana-dashboards"
    namespace = var.namespace
    labels = {
      grafana_dashboard = "1"
    }
  }

  data = {
    "tenant-overview.json"     = file("${path.module}/dashboards/tenant-overview.json")
    "kubernetes-overview.json" = file("${path.module}/dashboards/kubernetes-overview.json")
    "istio-overview.json"      = file("${path.module}/dashboards/istio-overview.json")
    "node-exporter.json"       = file("${path.module}/dashboards/node-exporter.json")
  }

  depends_on = [helm_release.prometheus_operator]
}

# ServiceMonitor for tenant monitoring will be created manually
# or through a separate Kubernetes manifest after Terraform apply