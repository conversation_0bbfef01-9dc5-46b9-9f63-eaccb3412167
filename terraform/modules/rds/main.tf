resource "aws_db_subnet_group" "main" {
  name       = "${var.environment}-db-subnet-group"
  subnet_ids = var.subnet_ids

  tags = merge(var.tags, {
    Name = "${var.environment}-db-subnet-group"
  })
}

# Using random_password from data.tf
# Using aws_secretsmanager_secret from data.tf
# Using aws_secretsmanager_secret_version from data.tf

resource "aws_db_parameter_group" "main" {
  name   = "${var.environment}-db-params"
  family = "mysql8.0"

  parameter {
    name  = "slow_query_log"
    value = "1"
  }

  parameter {
    name  = "long_query_time"
    value = "2"
  }

  parameter {
    name  = "require_secure_transport"
    value = "ON"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-db-params"
  })
}

resource "aws_db_instance" "main" {
  count                     = var.import_existing_resources ? 0 : 1
  identifier                = "${var.environment}-architrave-db-new"
  allocated_storage         = var.allocated_storage
  storage_type              = "gp3"
  engine                    = "mysql"
  engine_version            = var.engine_version
  instance_class            = var.instance_class
  db_name                   = var.db_name
  username                  = var.master_username
  password                  = var.master_password != "" ? var.master_password : try(random_password.master_password[0].result, "dummy-password")
  skip_final_snapshot       = true # Always skip final snapshot for development
  final_snapshot_identifier = null # No final snapshot identifier needed when skipping

  backup_retention_period = var.backup_retention_period
  deletion_protection     = false # Always disable deletion protection for development
  multi_az                = var.multi_az
  apply_immediately       = true # Always apply changes immediately for development
  storage_encrypted       = true
  kms_key_id              = var.kms_key_arn
  parameter_group_name    = aws_db_parameter_group.main.name

  # Enhanced security settings
  iam_database_authentication_enabled   = true
  copy_tags_to_snapshot                 = true
  enabled_cloudwatch_logs_exports       = ["audit", "error", "general", "slowquery"]
  performance_insights_enabled          = false  # Disabled for t3.small compatibility
  monitoring_interval                   = 60
  monitoring_role_arn                   = try(aws_iam_role.rds_monitoring_role[0].arn, "arn:aws:iam::123456789012:role/dummy-role")
  auto_minor_version_upgrade            = true

  vpc_security_group_ids = [var.security_group_id]
  db_subnet_group_name   = aws_db_subnet_group.main.name

  tags = merge(var.tags, { Name = "${var.environment}-architrave-db" })
}

# IAM role for RDS enhanced monitoring is defined in monitoring.tf

# Create a custom policy for RDS monitoring
resource "aws_iam_policy" "rds_monitoring_policy" {
  name        = "${var.environment}-rds-monitoring-policy"
  description = "Custom policy for RDS monitoring"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:PutRetentionPolicy"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:RDS-${var.environment}*"
      },
      {
        Action = [
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:RDS-${var.environment}*:log-stream:*"
      }
    ]
  })
}

# Attach the custom policy to the role
resource "aws_iam_role_policy_attachment" "rds_monitoring_custom_policy_attachment" {
  count      = var.import_existing_resources ? 0 : 1
  role       = aws_iam_role.rds_monitoring_role[0].name
  policy_arn = aws_iam_policy.rds_monitoring_policy.arn
}

# Using data sources from data.tf
# data "aws_region" "current" {}
# data "aws_caller_identity" "current" {}
