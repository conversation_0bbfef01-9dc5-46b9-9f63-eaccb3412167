# Data sources for RDS module

data "aws_region" "current" {}

data "aws_caller_identity" "current" {}

# Generate a random password for the RDS master user
resource "random_password" "master_password" {
  count = var.master_password == "" && !var.import_existing_resources ? 1 : 0

  length           = 16
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"
}

# Create a secret in AWS Secrets Manager to store the RDS master credentials
resource "aws_secretsmanager_secret" "rds_master" {
  name        = "${var.environment}/rds/master-${formatdate("YYYYMMDD-HHmmss", timestamp())}"
  description = "RDS master credentials for ${var.environment}"

  tags = merge(var.tags, {
    Name = "${var.environment}-rds-master-secret"
  })
}

# Store the RDS master credentials in the secret
resource "aws_secretsmanager_secret_version" "rds_master" {
  secret_id = aws_secretsmanager_secret.rds_master.id

  secret_string = jsonencode({
    username = var.master_username
    password = var.master_password != "" ? var.master_password : try(random_password.master_password[0].result, "dummy-password")
    engine   = "mysql"
    host     = var.use_aurora_serverless ? try(aws_rds_cluster.aurora_serverless[0].endpoint, "production-aurora-serverless.cluster-cpmagwki2kv8.${data.aws_region.current.name}.rds.amazonaws.com") : try(aws_db_instance.main[0].endpoint, "${var.environment}-architrave-db-new.cpmagwki2kv8.${data.aws_region.current.name}.rds.amazonaws.com")
    port     = 3306
    dbname   = var.db_name
  })
}
