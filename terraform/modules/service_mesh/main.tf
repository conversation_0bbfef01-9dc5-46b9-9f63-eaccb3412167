terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.0"
    }
    aws = {
      source  = "hashicorp/aws"
      version = ">= 4.0.0"
    }
  }
}

# Data sources
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

# Local variables
locals {
  namespace = var.skip_k8s_connection ? var.namespace : (var.create_namespace ? kubernetes_namespace.istio_system[0].metadata[0].name : var.namespace)

  # Istio configuration
  istio_version = var.istio_version

  # Monitoring configuration
  enable_kiali      = var.enable_kiali
  enable_jaeger     = var.enable_jaeger
  enable_prometheus = var.enable_prometheus
  enable_grafana    = var.enable_grafana

  # mTLS configuration
  mtls_mode = var.mtls_mode

  # Istio gateway configuration
  create_gateway = var.create_gateway
  gateway_name   = var.gateway_name
  gateway_hosts  = var.gateway_hosts
}

# Create namespace for Istio if needed
resource "kubernetes_namespace" "istio_system" {
  count = var.create_namespace && !var.skip_k8s_connection ? 1 : 0

  metadata {
    name = var.namespace
    labels = merge({
      "app.kubernetes.io/managed-by" = "terraform"
      "istio-injection"              = "disabled" # Disable istio-injection for the istio-system namespace
    }, var.namespace_labels)
  }
}

# Deploy Istio base components
resource "helm_release" "istio_base" {
  count      = !var.skip_k8s_connection ? 1 : 0
  name       = "istio-base"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "base"
  version    = local.istio_version
  namespace  = local.namespace

  set {
    name  = "global.istioNamespace"
    value = local.namespace
  }

  depends_on = [kubernetes_namespace.istio_system]
}

# Deploy Istio discovery (istiod)
resource "helm_release" "istiod" {
  count      = !var.skip_k8s_connection ? 1 : 0
  name       = "istiod"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "istiod"
  version    = local.istio_version
  namespace  = local.namespace

  set {
    name  = "global.hub"
    value = "docker.io/istio"
  }

  set {
    name  = "global.tag"
    value = local.istio_version
  }

  set {
    name  = "global.istioNamespace"
    value = local.namespace
  }

  set {
    name  = "pilot.resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "pilot.resources.requests.memory"
    value = "128Mi"
  }

  set {
    name  = "pilot.resources.limits.cpu"
    value = "500m"
  }

  set {
    name  = "pilot.resources.limits.memory"
    value = "512Mi"
  }

  set {
    name  = "global.proxy.resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "global.proxy.resources.requests.memory"
    value = "128Mi"
  }

  set {
    name  = "global.proxy.resources.limits.cpu"
    value = "200m"
  }

  set {
    name  = "global.proxy.resources.limits.memory"
    value = "256Mi"
  }

  # Configure mTLS settings
  set {
    name  = "meshConfig.enableAutoMtls"
    value = true
  }

  set {
    name  = "meshConfig.defaultConfig.proxyMetadata.ISTIO_META_AUTO_MTLS_ENABLED"
    value = true
  }

  depends_on = [helm_release.istio_base]
}

# Deploy Istio ingress gateway
resource "helm_release" "istio_ingress" {
  count      = !var.skip_k8s_connection ? 1 : 0
  name       = "istio-ingressgateway"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "gateway"
  version    = local.istio_version
  namespace  = local.namespace

  set {
    name  = "service.type"
    value = "LoadBalancer"
  }

  set {
    name  = "service.ports[0].name"
    value = "status-port"
  }

  set {
    name  = "service.ports[0].port"
    value = "15021"
  }

  set {
    name  = "service.ports[0].targetPort"
    value = "15021"
  }

  set {
    name  = "service.ports[1].name"
    value = "http2"
  }

  set {
    name  = "service.ports[1].port"
    value = "80"
  }

  set {
    name  = "service.ports[1].targetPort"
    value = "8080"
  }

  set {
    name  = "service.ports[2].name"
    value = "https"
  }

  set {
    name  = "service.ports[2].port"
    value = "443"
  }

  set {
    name  = "service.ports[2].targetPort"
    value = "8443"
  }

  set {
    name  = "resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "resources.requests.memory"
    value = "128Mi"
  }

  set {
    name  = "resources.limits.cpu"
    value = "200m"
  }

  set {
    name  = "resources.limits.memory"
    value = "256Mi"
  }

  depends_on = [helm_release.istiod]
}

# Create default Istio Gateway resource
resource "kubernetes_manifest" "istio_gateway" {
  count = local.create_gateway && !var.skip_k8s_connection ? 1 : 0
  manifest = {
    apiVersion = "networking.istio.io/v1beta1"
    kind       = "Gateway"
    metadata = {
      name      = local.gateway_name
      namespace = local.namespace
    }
    spec = {
      selector = {
        istio = "ingressgateway"
      }
      servers = [
        {
          port = {
            number   = 80
            name     = "http"
            protocol = "HTTP"
          }
          hosts = local.gateway_hosts
        }
      ]
    }
  }

  depends_on = [helm_release.istio_ingress]
}

# Create default mTLS policy
resource "kubernetes_manifest" "default_peer_authentication" {
  count = !var.skip_k8s_connection ? 1 : 0
  manifest = {
    apiVersion = "security.istio.io/v1beta1"
    kind       = "PeerAuthentication"
    metadata = {
      name      = "default"
      namespace = local.namespace
    }
    spec = {
      mtls = {
        mode = local.mtls_mode
      }
    }
  }

  depends_on = [helm_release.istiod]
}

# Create namespace-specific mTLS policies for each tenant namespace
resource "kubernetes_manifest" "tenant_peer_authentication" {
  for_each = var.enable_istio_mtls && !var.skip_k8s_connection ? toset(var.tenant_namespaces) : []

  manifest = {
    apiVersion = "security.istio.io/v1beta1"
    kind       = "PeerAuthentication"
    metadata = {
      name      = "default"
      namespace = each.key
    }
    spec = {
      mtls = {
        mode = var.istio_mtls_mode
      }
    }
  }

  depends_on = [helm_release.istiod]
}

# Create default destination rule for mTLS
resource "kubernetes_manifest" "default_destination_rule" {
  count = var.enable_istio_mtls && !var.skip_k8s_connection ? 1 : 0

  manifest = {
    apiVersion = "networking.istio.io/v1beta1"
    kind       = "DestinationRule"
    metadata = {
      name      = "default"
      namespace = local.namespace
    }
    spec = {
      host = "*.local"
      trafficPolicy = {
        tls = {
          mode = var.istio_mtls_mode == "STRICT" ? "ISTIO_MUTUAL" : "DISABLE"
        }
      }
    }
  }

  depends_on = [helm_release.istiod]
}

# Create canary deployment virtual services
resource "kubernetes_manifest" "canary_virtual_services" {
  for_each = var.skip_k8s_connection ? {} : var.canary_deployments

  manifest = yamldecode(templatefile("${path.module}/templates/canary-virtualservice.yaml", {
    service_name                          = each.key
    namespace                             = each.value.namespace
    app_name                              = each.value.app_name
    host_name                             = lookup(each.value, "host_name", "")
    gateway_name                          = lookup(each.value, "gateway_name", "")
    primary_weight                        = each.value.primary_weight
    canary_weight                         = each.value.canary_weight
    enable_fault_injection                = lookup(each.value, "enable_fault_injection", false)
    fault_delay_percentage                = lookup(each.value, "fault_delay_percentage", 0)
    fault_delay_duration                  = lookup(each.value, "fault_delay_duration", "1s")
    fault_abort_percentage                = lookup(each.value, "fault_abort_percentage", 0)
    fault_abort_http_status               = lookup(each.value, "fault_abort_http_status", 500)
    enable_retry                          = lookup(each.value, "enable_retry", false)
    retry_attempts                        = lookup(each.value, "retry_attempts", 3)
    retry_per_try_timeout                 = lookup(each.value, "retry_per_try_timeout", "2s")
    retry_on                              = lookup(each.value, "retry_on", "gateway-error,connect-failure,refused-stream")
    enable_timeout                        = lookup(each.value, "enable_timeout", false)
    timeout                               = lookup(each.value, "timeout", "10s")
    enable_circuit_breaking               = lookup(each.value, "enable_circuit_breaking", false)
    circuit_breaking_consecutive_errors   = lookup(each.value, "circuit_breaking_consecutive_errors", 5)
    circuit_breaking_interval             = lookup(each.value, "circuit_breaking_interval", "1m")
    circuit_breaking_base_ejection_time   = lookup(each.value, "circuit_breaking_base_ejection_time", "30s")
    circuit_breaking_max_ejection_percent = lookup(each.value, "circuit_breaking_max_ejection_percent", 100)
  }))

  depends_on = [helm_release.istiod]
}

# Create tenant-specific virtual services
resource "kubernetes_manifest" "tenant_virtual_services" {
  for_each = var.skip_k8s_connection ? {} : var.tenant_virtual_services

  manifest = yamldecode(templatefile("${path.module}/templates/tenant-virtual-service.yaml", {
    service_name    = each.key
    namespace       = each.value.namespace
    tenant_id       = each.value.tenant_id
    host            = each.value.host
    gateway         = lookup(each.value, "gateway", "")
    uri_prefix      = lookup(each.value, "uri_prefix", "/")
    port            = lookup(each.value, "port", 80)
    timeout         = lookup(each.value, "timeout", "30s")
    retries         = lookup(each.value, "retries", null)
    fault_injection = lookup(each.value, "fault_injection", null)
    api_routes      = lookup(each.value, "api_routes", null)
  }))

  depends_on = [helm_release.istiod]
}

# Create destination rules for services
resource "kubernetes_manifest" "destination_rules" {
  for_each = var.skip_k8s_connection ? {} : var.destination_rules

  manifest = yamldecode(templatefile("${path.module}/templates/destination-rule.yaml", {
    service_name                = each.key
    namespace                   = each.value.namespace
    app_name                    = each.value.app_name
    load_balancer_type          = lookup(each.value, "load_balancer_type", "ROUND_ROBIN")
    max_connections             = lookup(each.value, "max_connections", 1024)
    connect_timeout             = lookup(each.value, "connect_timeout", "1s")
    http1_max_pending_requests  = lookup(each.value, "http1_max_pending_requests", 1024)
    http2_max_requests          = lookup(each.value, "http2_max_requests", 1024)
    max_requests_per_connection = lookup(each.value, "max_requests_per_connection", 0)
    max_retries                 = lookup(each.value, "max_retries", 3)
    consecutive_errors          = lookup(each.value, "consecutive_errors", 5)
    interval                    = lookup(each.value, "interval", "30s")
    base_ejection_time          = lookup(each.value, "base_ejection_time", "30s")
    max_ejection_percent        = lookup(each.value, "max_ejection_percent", 100)
    enable_tls                  = lookup(each.value, "enable_tls", true)
    tls_mode                    = lookup(each.value, "tls_mode", "ISTIO_MUTUAL")
    primary_version             = each.value.primary_version
    canary_version              = each.value.canary_version
    canary_load_balancer_type   = lookup(each.value, "canary_load_balancer_type", "ROUND_ROBIN")
  }))

  depends_on = [helm_release.istiod]
}

# Create tenant-specific destination rules
resource "kubernetes_manifest" "tenant_destination_rules" {
  for_each = var.skip_k8s_connection ? {} : var.tenant_destination_rules

  manifest = yamldecode(templatefile("${path.module}/templates/tenant-destination-rule.yaml", {
    service_name                = each.key
    namespace                   = each.value.namespace
    tenant_id                   = each.value.tenant_id
    max_connections             = lookup(each.value, "max_connections", 1024)
    connect_timeout             = lookup(each.value, "connect_timeout", "1s")
    http1_max_pending_requests  = lookup(each.value, "http1_max_pending_requests", 1024)
    http2_max_requests          = lookup(each.value, "http2_max_requests", 1024)
    max_requests_per_connection = lookup(each.value, "max_requests_per_connection", 0)
    max_retries                 = lookup(each.value, "max_retries", 3)
    consecutive_errors          = lookup(each.value, "consecutive_errors", 5)
    interval                    = lookup(each.value, "interval", "30s")
    base_ejection_time          = lookup(each.value, "base_ejection_time", "30s")
    max_ejection_percent        = lookup(each.value, "max_ejection_percent", 100)
    subsets                     = lookup(each.value, "subsets", null)
  }))

  depends_on = [helm_release.istiod]
}

# Create gateways
resource "kubernetes_manifest" "gateways" {
  for_each = var.skip_k8s_connection ? {} : var.gateways

  manifest = yamldecode(templatefile("${path.module}/templates/gateway.yaml", {
    gateway_name           = each.key
    namespace              = each.value.namespace
    enable_http            = lookup(each.value, "enable_http", true)
    enable_https           = lookup(each.value, "enable_https", false)
    redirect_http_to_https = lookup(each.value, "redirect_http_to_https", false)
    hosts                  = each.value.hosts
    tls_mode               = lookup(each.value, "tls_mode", "SIMPLE")
    credential_name        = lookup(each.value, "credential_name", "")
  }))

  depends_on = [helm_release.istiod]
}

# Create service entries for external services
resource "kubernetes_manifest" "service_entries" {
  for_each = var.skip_k8s_connection ? {} : var.service_entries

  manifest = yamldecode(templatefile("${path.module}/templates/service-entry.yaml", {
    service_entry_name = each.key
    namespace          = each.value.namespace
    hosts              = each.value.hosts
    ports              = each.value.ports
    location           = each.value.location
    resolution         = each.value.resolution
    endpoints          = lookup(each.value, "endpoints", null)
  }))

  depends_on = [helm_release.istiod]
}

# Create authorization policies
resource "kubernetes_manifest" "authorization_policies" {
  for_each = var.skip_k8s_connection ? {} : var.authorization_policies

  manifest = yamldecode(templatefile("${path.module}/templates/authorization-policy.yaml", {
    policy_name = each.key
    namespace   = each.value.namespace
    app_name    = each.value.app_name
    action      = lookup(each.value, "action", "")
    rules       = each.value.rules
  }))

  depends_on = [helm_release.istiod]
}

# Create tenant-specific authorization policies
resource "kubernetes_manifest" "tenant_authorization_policies" {
  for_each = var.skip_k8s_connection ? {} : var.tenant_authorization_policies

  manifest = yamldecode(templatefile("${path.module}/templates/tenant-authorization-policy.yaml", {
    policy_name        = each.key
    namespace          = each.value.namespace
    tenant_id          = each.value.tenant_id
    app_name           = each.value.app_name
    action             = lookup(each.value, "action", "ALLOW")
    allowed_namespaces = lookup(each.value, "allowed_namespaces", [])
    rules              = lookup(each.value, "rules", [])
  }))

  depends_on = [helm_release.istiod]
}

# Deploy Kiali for service mesh visualization
resource "helm_release" "kiali" {
  count      = var.enable_kiali && !var.skip_k8s_connection ? 1 : 0
  name       = "kiali"
  repository = "https://kiali.org/helm-charts"
  chart      = "kiali-operator"
  version    = var.kiali_version
  namespace  = local.namespace

  set {
    name  = "cr.create"
    value = "true"
  }

  set {
    name  = "cr.namespace"
    value = local.namespace
  }

  set {
    name  = "cr.spec.deployment.resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "cr.spec.deployment.resources.requests.memory"
    value = "128Mi"
  }

  set {
    name  = "cr.spec.deployment.resources.limits.cpu"
    value = "200m"
  }

  set {
    name  = "cr.spec.deployment.resources.limits.memory"
    value = "256Mi"
  }

  set {
    name  = "cr.spec.external_services.prometheus.url"
    value = "http://prometheus-server.monitoring:80"
  }

  set {
    name  = "cr.spec.external_services.tracing.in_cluster_url"
    value = "http://jaeger-query.${local.namespace}:16686"
  }

  set {
    name  = "cr.spec.external_services.grafana.in_cluster_url"
    value = "http://prometheus-grafana.monitoring:80"
  }

  depends_on = [
    helm_release.istiod,
    helm_release.istio_ingress
  ]
}

# Deploy Jaeger for distributed tracing
resource "helm_release" "jaeger" {
  count      = var.enable_jaeger && !var.skip_k8s_connection ? 1 : 0
  name       = "jaeger"
  repository = "https://jaegertracing.github.io/helm-charts"
  chart      = "jaeger"
  version    = var.jaeger_version
  namespace  = local.namespace

  set {
    name  = "provisionDataStore.cassandra"
    value = "false"
  }

  set {
    name  = "provisionDataStore.elasticsearch"
    value = "false"
  }

  set {
    name  = "storage.type"
    value = "memory"
  }

  set {
    name  = "allInOne.resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "allInOne.resources.requests.memory"
    value = "128Mi"
  }

  set {
    name  = "allInOne.resources.limits.cpu"
    value = "200m"
  }

  set {
    name  = "allInOne.resources.limits.memory"
    value = "256Mi"
  }

  depends_on = [
    helm_release.istiod,
    helm_release.istio_ingress
  ]
}

# Create ConfigMap for Istio sidecar injection in application namespaces
resource "kubernetes_config_map" "istio_sidecar_injector" {
  count = !var.skip_k8s_connection ? 1 : 0
  metadata {
    name      = "istio-sidecar-injector"
    namespace = local.namespace
  }

  data = {
    "values" = <<-EOT
      {
        "global": {
          "proxy": {
            "resources": {
              "requests": {
                "cpu": "100m",
                "memory": "128Mi"
              },
              "limits": {
                "cpu": "200m",
                "memory": "256Mi"
              }
            }
          }
        }
      }
    EOT
  }

  depends_on = [helm_release.istiod]
}

# Label namespaces for Istio injection
resource "null_resource" "label_namespaces" {
  count = length(var.namespaces_to_inject) > 0 && !var.skip_k8s_connection ? 1 : 0

  provisioner "local-exec" {
    command = <<-EOT
      for ns in ${join(" ", var.namespaces_to_inject)}; do
        kubectl label namespace $ns istio-injection=enabled --overwrite
      done
    EOT
  }

  depends_on = [
    helm_release.istiod,
    kubernetes_config_map.istio_sidecar_injector
  ]
}





# Deploy Istio egress gateway
resource "helm_release" "istio_egress" {
  count      = var.deploy_egress_gateway && !var.skip_k8s_connection ? 1 : 0
  name       = "istio-egressgateway"
  repository = "https://istio-release.storage.googleapis.com/charts"
  chart      = "gateway"
  version    = var.istio_version
  namespace  = local.namespace

  set {
    name  = "global.hub"
    value = var.istio_hub
  }

  set {
    name  = "global.tag"
    value = var.istio_tag
  }

  set {
    name  = "service.type"
    value = "ClusterIP"
  }

  set {
    name  = "resources.requests.cpu"
    value = var.egress_gateway_resources.requests.cpu
  }

  set {
    name  = "resources.requests.memory"
    value = var.egress_gateway_resources.requests.memory
  }

  set {
    name  = "resources.limits.cpu"
    value = var.egress_gateway_resources.limits.cpu
  }

  set {
    name  = "resources.limits.memory"
    value = var.egress_gateway_resources.limits.memory
  }

  depends_on = [helm_release.istiod[0]]
}





# Create namespace for application with istio-injection enabled
resource "kubernetes_namespace" "app_namespaces" {
  for_each = var.skip_k8s_connection ? toset([]) : toset(var.app_namespaces)

  metadata {
    name = each.key
    labels = {
      "istio-injection" = "enabled"
    }
  }

  depends_on = [helm_release.istiod]
}



# Create destination rule for mTLS
resource "kubernetes_manifest" "destination_rule_mtls" {
  count = var.enable_mtls && !var.skip_k8s_connection ? 1 : 0
  manifest = {
    apiVersion = "networking.istio.io/v1alpha3"
    kind       = "DestinationRule"
    metadata = {
      name      = "default"
      namespace = local.namespace
    }
    spec = {
      host = "*.svc.cluster.local"
      trafficPolicy = {
        tls = {
          mode = "ISTIO_MUTUAL"
        }
      }
    }
  }

  depends_on = [helm_release.istiod]
}



# Create Grafana dashboard for Istio
resource "kubernetes_config_map" "istio_dashboard" {
  count = var.create_grafana_dashboard && !var.skip_k8s_connection ? 1 : 0
  metadata {
    name      = "istio-dashboard"
    namespace = var.grafana_namespace
    labels = {
      grafana_dashboard = "1"
    }
  }

  data = {
    "istio-dashboard.json" = file("${path.module}/dashboards/istio-dashboard.json")
  }

  depends_on = [helm_release.istiod]
}

# Create Grafana dashboard for Istio Service Dashboard
resource "kubernetes_config_map" "istio_service_dashboard" {
  count = var.create_grafana_dashboard && !var.skip_k8s_connection ? 1 : 0
  metadata {
    name      = "istio-service-dashboard"
    namespace = var.grafana_namespace
    labels = {
      grafana_dashboard = "1"
    }
  }

  data = {
    "istio-service-dashboard.json" = file("${path.module}/dashboards/istio-service-dashboard.json")
  }

  depends_on = [helm_release.istiod]
}

# Create Grafana dashboard for Istio Workload Dashboard
resource "kubernetes_config_map" "istio_workload_dashboard" {
  count = var.create_grafana_dashboard && !var.skip_k8s_connection ? 1 : 0
  metadata {
    name      = "istio-workload-dashboard"
    namespace = var.grafana_namespace
    labels = {
      grafana_dashboard = "1"
    }
  }

  data = {
    "istio-workload-dashboard.json" = file("${path.module}/dashboards/istio-workload-dashboard.json")
  }

  depends_on = [helm_release.istiod]
}

# Create Grafana dashboard for Istio Control Plane Dashboard
resource "kubernetes_config_map" "istio_control_plane_dashboard" {
  count = var.create_grafana_dashboard && !var.skip_k8s_connection ? 1 : 0
  metadata {
    name      = "istio-control-plane-dashboard"
    namespace = var.grafana_namespace
    labels = {
      grafana_dashboard = "1"
    }
  }

  data = {
    "istio-control-plane-dashboard.json" = file("${path.module}/dashboards/istio-control-plane-dashboard.json")
  }

  depends_on = [helm_release.istiod]
}
