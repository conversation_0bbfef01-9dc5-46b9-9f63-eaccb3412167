# Tenant-Specific Autoscaling Configuration
# This file contains KEDA ScaledObjects and HPA configurations specifically for tenant workloads

# Create namespace-based KEDA ScaledObjects for tenant workloads
resource "kubernetes_manifest" "tenant_scaled_object" {
  for_each = var.deploy_keda && !var.skip_k8s_connection ? var.tenant_namespaces_set : toset([])

  manifest = {
    apiVersion = "keda.sh/v1alpha1"
    kind       = "ScaledObject"
    metadata = {
      name      = "tenant-backend-scaler"
      namespace = each.key
    }
    spec = {
      scaleTargetRef = {
        name = "${replace(each.key, "tenant-", "")}-backend"
      }
      minReplicaCount = 1
      maxReplicaCount = 10  # Allow up to 10 replicas per tenant
      pollingInterval = 15  # Check every 15 seconds
      cooldownPeriod  = 60  # 1 minute cooldown
      triggers = [
        {
          type = "cpu"
          metadata = {
            type  = "Utilization"
            value = "70"  # Scale at 70% CPU
          }
        },
        {
          type = "memory"
          metadata = {
            type  = "Utilization"
            value = "80"  # Scale at 80% memory
          }
        }
      ]
    }
  }
}

# Create tenant-specific HPA for RabbitMQ workloads
resource "kubernetes_manifest" "tenant_rabbitmq_scaled_object" {
  for_each = var.deploy_keda && !var.skip_k8s_connection ? var.tenant_namespaces_set : toset([])

  manifest = {
    apiVersion = "keda.sh/v1alpha1"
    kind       = "ScaledObject"
    metadata = {
      name      = "tenant-rabbitmq-scaler"
      namespace = each.key
    }
    spec = {
      scaleTargetRef = {
        name = "${replace(each.key, "tenant-", "")}-rabbitmq"
      }
      minReplicaCount = 1
      maxReplicaCount = 5   # RabbitMQ doesn't need as many replicas
      pollingInterval = 30  # Check every 30 seconds
      cooldownPeriod  = 120 # 2 minute cooldown for RabbitMQ
      triggers = [
        {
          type = "rabbitmq"
          metadata = {
            protocol    = "amqp"
            host        = "${replace(each.key, "tenant-", "")}-rabbitmq-service.${each.key}.svc.cluster.local:5672"
            queueName   = "default"
            queueLength = "10"  # Scale when queue has 10+ messages
          }
        }
      ]
    }
  }
}

# Create cluster-wide resource quotas for tenant namespaces
resource "kubernetes_resource_quota" "tenant_quota" {
  for_each = var.deploy_keda && !var.skip_k8s_connection ? var.tenant_namespaces_set : toset([])

  metadata {
    name      = "tenant-resource-quota"
    namespace = each.key
  }

  spec {
    hard = {
      "requests.cpu"    = "2"      # 2 CPU cores per tenant
      "requests.memory" = "4Gi"    # 4GB memory per tenant
      "limits.cpu"      = "4"      # Max 4 CPU cores per tenant
      "limits.memory"   = "8Gi"    # Max 8GB memory per tenant
      "pods"            = "20"     # Max 20 pods per tenant
      "services"        = "10"     # Max 10 services per tenant
      "persistentvolumeclaims" = "5"  # Max 5 PVCs per tenant
    }
  }
}

# Create limit ranges for tenant pods
resource "kubernetes_limit_range" "tenant_limits" {
  for_each = var.deploy_keda && !var.skip_k8s_connection ? var.tenant_namespaces_set : toset([])

  metadata {
    name      = "tenant-limit-range"
    namespace = each.key
  }

  spec {
    limit {
      type = "Pod"
      max = {
        cpu    = "2"
        memory = "4Gi"
      }
      min = {
        cpu    = "100m"
        memory = "128Mi"
      }
    }
    limit {
      type = "Container"
      default = {
        cpu    = "500m"
        memory = "512Mi"
      }
      default_request = {
        cpu    = "200m"
        memory = "256Mi"
      }
      max = {
        cpu    = "1"
        memory = "2Gi"
      }
      min = {
        cpu    = "100m"
        memory = "128Mi"
      }
    }
  }
}

# Create network policies for tenant isolation
resource "kubernetes_network_policy" "tenant_isolation" {
  for_each = var.deploy_keda && !var.skip_k8s_connection ? var.tenant_namespaces_set : toset([])

  metadata {
    name      = "tenant-isolation"
    namespace = each.key
  }

  spec {
    pod_selector {}
    
    policy_types = ["Ingress", "Egress"]
    
    ingress {
      from {
        namespace_selector {
          match_labels = {
            name = each.key
          }
        }
      }
      from {
        namespace_selector {
          match_labels = {
            name = "istio-system"
          }
        }
      }
      from {
        namespace_selector {
          match_labels = {
            name = "kube-system"
          }
        }
      }
    }
    
    egress {
      to {
        namespace_selector {
          match_labels = {
            name = each.key
          }
        }
      }
      to {
        namespace_selector {
          match_labels = {
            name = "istio-system"
          }
        }
      }
      to {
        namespace_selector {
          match_labels = {
            name = "kube-system"
          }
        }
      }
      # Allow egress to external services (RDS, S3, etc.)
      to {}
      ports {
        protocol = "TCP"
        port     = "443"  # HTTPS
      }
      ports {
        protocol = "TCP"
        port     = "3306" # MySQL
      }
      ports {
        protocol = "TCP"
        port     = "5432" # PostgreSQL
      }
    }
  }
}

# Create priority classes for tenant workloads
resource "kubernetes_priority_class" "tenant_priority" {
  count = var.deploy_keda && !var.skip_k8s_connection ? 1 : 0

  metadata {
    name = "tenant-priority"
  }

  value          = 100
  global_default = false
  description    = "Priority class for tenant workloads"
}

resource "kubernetes_priority_class" "tenant_high_priority" {
  count = var.deploy_keda && !var.skip_k8s_connection ? 1 : 0

  metadata {
    name = "tenant-high-priority"
  }

  value          = 200
  global_default = false
  description    = "High priority class for critical tenant workloads"
}
