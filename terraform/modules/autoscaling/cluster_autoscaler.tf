/**
 * # Cluster Autoscaler Configuration
 *
 * This file contains the configuration for the Kubernetes Cluster Autoscaler,
 * which automatically adjusts the size of the Kubernetes cluster based on resource needs.
 * Enhanced for unlimited tenant onboarding capacity with proper ASG tag management.
 */

# Data source to get existing ASGs for tag management
data "aws_autoscaling_groups" "cluster_asgs" {
  count = var.deploy_cluster_autoscaler && !var.skip_k8s_connection ? 1 : 0

  filter {
    name   = "tag:kubernetes.io/cluster/${var.cluster_name}"
    values = ["owned"]
  }
}

# Add cluster autoscaler tags to main node group ASG - DISABLED due to for_each unknown values
# resource "aws_autoscaling_group_tag" "cluster_autoscaler_enabled_main" {
#   # ... configuration disabled
# }

# Add cluster-specific autoscaler tag to main node group ASG - DISABLED due to for_each unknown values
# resource "aws_autoscaling_group_tag" "cluster_autoscaler_cluster_main" {
#   # ... configuration disabled
# }

# Deploy the Cluster Autoscaler with enhanced configuration for unlimited capacity
resource "helm_release" "cluster_autoscaler" {
  count      = var.deploy_cluster_autoscaler && !var.skip_k8s_connection ? 1 : 0
  name       = "cluster-autoscaler"
  repository = "https://kubernetes.github.io/autoscaler"
  chart      = "cluster-autoscaler"
  namespace  = var.cluster_autoscaler_namespace
  version    = var.cluster_autoscaler_version

  set {
    name  = "autoDiscovery.clusterName"
    value = var.cluster_name
  }

  set {
    name  = "awsRegion"
    value = var.aws_region
  }

  set {
    name  = "rbac.serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = var.cluster_autoscaler_role_arn
  }

  # Enhanced scaling configuration for unlimited tenant capacity
  set {
    name  = "extraArgs.scale-down-delay-after-add"
    value = var.scale_down_delay_after_add
  }

  set {
    name  = "extraArgs.scale-down-unneeded-time"
    value = var.scale_down_unneeded_time
  }

  set {
    name  = "extraArgs.scan-interval"
    value = var.scan_interval
  }

  set {
    name  = "extraArgs.balance-similar-node-groups"
    value = "true"
  }

  set {
    name  = "extraArgs.skip-nodes-with-system-pods"
    value = "false"
  }

  # Enhanced resource configuration for high-load scenarios
  set {
    name  = "resources.requests.cpu"
    value = var.cluster_autoscaler_cpu_request
  }

  set {
    name  = "resources.requests.memory"
    value = var.cluster_autoscaler_memory_request
  }

  set {
    name  = "resources.limits.cpu"
    value = var.cluster_autoscaler_cpu_limit
  }

  set {
    name  = "resources.limits.memory"
    value = var.cluster_autoscaler_memory_limit
  }

  # Enhanced scaling configuration for tenant onboarding bursts
  set {
    name  = "extraArgs.scale-down-utilization-threshold"
    value = var.scale_down_utilization_threshold
  }

  set {
    name  = "extraArgs.expander"
    value = var.expander_strategy
  }

  # Add aggressive scaling for tenant onboarding scenarios
  set {
    name  = "extraArgs.max-node-provision-time"
    value = var.max_node_provision_time
  }

  set {
    name  = "extraArgs.scale-down-enabled"
    value = var.scale_down_enabled
  }

  set {
    name  = "extraArgs.max-nodes-total"
    value = var.max_nodes_total
  }

  set {
    name  = "extraArgs.cores-total"
    value = var.cores_total
  }

  set {
    name  = "extraArgs.memory-total"
    value = var.memory_total
  }

  # Enable node group auto-discovery with proper tags
  set {
    name  = "extraArgs.node-group-auto-discovery"
    value = "asg:tag=k8s.io/cluster-autoscaler/enabled,k8s.io/cluster-autoscaler/${var.cluster_name}"
  }

  # Cost optimization parameters
  set {
    name  = "extraArgs.scale-down-delay-after-delete"
    value = "10s"  # Quick evaluation after node deletion
  }

  set {
    name  = "extraArgs.scale-down-delay-after-failure"
    value = "3m"   # Short delay after scale-down failure
  }

  set {
    name  = "extraArgs.skip-nodes-with-local-storage"
    value = "false"  # Allow scaling down nodes with local storage
  }

  set {
    name  = "extraArgs.new-pod-scale-up-delay"
    value = "0s"     # Immediate response to new pods
  }

  set {
    name  = "extraArgs.max-empty-bulk-delete"
    value = "10"     # Allow bulk deletion of empty nodes
  }

  depends_on = [
    helm_release.metrics_server
  ]
}
