# Variables for the autoscaling module

variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "eks_oidc_provider_url" {
  description = "URL of the EKS OIDC provider"
  type        = string
}

variable "cluster_endpoint" {
  description = "Endpoint of the EKS cluster"
  type        = string
}

# Namespace variable
variable "namespace" {
  description = "Kubernetes namespace to deploy resources into"
  type        = string
  default     = "kube-system"
}

# Skip Kubernetes connection
variable "skip_k8s_connection" {
  description = "Whether to skip Kubernetes connection"
  type        = bool
  default     = false
}

# Metrics Server variables
variable "deploy_metrics_server" {
  description = "Whether to deploy Metrics Server"
  type        = bool
  default     = true
}

variable "metrics_server_version" {
  description = "Version of the Metrics Server Helm chart"
  type        = string
  default     = "3.8.2"
}

# VPA variables
variable "deploy_vpa" {
  description = "Whether to deploy Vertical Pod Autoscaler"
  type        = bool
  default     = true
}

variable "vpa_version" {
  description = "Version of the VPA Helm chart"
  type        = string
  default     = "1.4.0"
}

variable "vpa_updater_enabled" {
  description = "Whether to enable VPA updater"
  type        = bool
  default     = true
}

variable "install_vpa" {
  description = "Whether to install Vertical Pod Autoscaler"
  type        = bool
  default     = true
}

variable "vpa_chart_version" {
  description = "Version of the VPA Helm chart"
  type        = string
  default     = "1.4.0"
}

# Cluster Autoscaler variables
variable "deploy_cluster_autoscaler" {
  description = "Whether to deploy Cluster Autoscaler"
  type        = bool
  default     = true
}

variable "cluster_autoscaler_version" {
  description = "Version of the Cluster Autoscaler Helm chart"
  type        = string
  default     = "9.21.0"
}

variable "cluster_autoscaler_role_arn" {
  description = "ARN of the IAM role for Cluster Autoscaler"
  type        = string
  default     = ""
}

# Karpenter variables
variable "deploy_karpenter" {
  description = "Whether to deploy Karpenter"
  type        = bool
  default     = true
}

variable "karpenter_version" {
  description = "Version of the Karpenter Helm chart"
  type        = string
  default     = "0.16.3"
}

variable "install_karpenter" {
  description = "Whether to install Karpenter"
  type        = bool
  default     = true
}

variable "karpenter_chart_version" {
  description = "Version of the Karpenter Helm chart"
  type        = string
  default     = "0.16.3"
}

variable "instance_types" {
  description = "List of instance types to use for Karpenter"
  type        = list(string)
  default     = ["t3.medium", "t3.large", "m5.large", "m5.xlarge"]
}

variable "max_cpu" {
  description = "Maximum CPU resources for Karpenter"
  type        = string
  default     = "50000"  # Unlimited CPU capacity
}

variable "max_memory" {
  description = "Maximum memory resources for Karpenter"
  type        = string
  default     = "200000Gi"  # Unlimited memory capacity
}

# KEDA variables
variable "deploy_keda" {
  description = "Whether to deploy KEDA"
  type        = bool
  default     = true
}

variable "keda_version" {
  description = "Version of the KEDA Helm chart"
  type        = string
  default     = "2.8.2"
}

variable "keda_namespace" {
  description = "Namespace for KEDA"
  type        = string
  default     = "keda"
}

variable "keda_role_arn" {
  description = "ARN of the IAM role for KEDA"
  type        = string
  default     = ""
}

variable "install_keda" {
  description = "Whether to install KEDA"
  type        = bool
  default     = true
}

variable "keda_chart_version" {
  description = "Version of the KEDA Helm chart"
  type        = string
  default     = "2.8.2"
}

# Goldilocks variables
variable "deploy_goldilocks" {
  description = "Whether to deploy Goldilocks"
  type        = bool
  default     = true
}

variable "goldilocks_version" {
  description = "Version of the Goldilocks Helm chart"
  type        = string
  default     = "4.4.2"
}

variable "goldilocks_namespace" {
  description = "Namespace for Goldilocks"
  type        = string
  default     = "goldilocks"
}

variable "install_goldilocks" {
  description = "Whether to install Goldilocks"
  type        = bool
  default     = true
}

variable "goldilocks_chart_version" {
  description = "Version of the Goldilocks Helm chart"
  type        = string
  default     = "4.8.1"
}

# PDB variables
variable "deploy_pdbs" {
  description = "Whether to deploy PodDisruptionBudgets"
  type        = bool
  default     = true
}

variable "create_default_pdbs" {
  description = "Whether to create default PodDisruptionBudgets"
  type        = bool
  default     = true
}

variable "min_available_percent" {
  description = "Minimum available percentage for PDBs"
  type        = number
  default     = 50
}

# Namespace variables
variable "tenant_namespaces" {
  description = "List of tenant namespaces to create PDBs for"
  type        = list(string)
  default     = []
}

# Deployment variables
variable "deployments" {
  description = "Map of deployments to configure autoscaling for"
  type        = map(any)
  default     = {}
}

# Scaled objects variables
variable "cpu_scaled_objects" {
  description = "Map of CPU-based scaled objects"
  type        = map(any)
  default     = {}
}

variable "memory_scaled_objects" {
  description = "Map of memory-based scaled objects"
  type        = map(any)
  default     = {}
}

variable "prometheus_scaled_objects" {
  description = "Map of Prometheus-based scaled objects"
  type        = map(any)
  default     = {}
}

# PDB variables
variable "pdbs" {
  description = "Map of PodDisruptionBudgets to create"
  type        = map(any)
  default     = {}
}

# AWS region
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "eu-central-1"
}

# HPA variables
variable "deploy_hpa" {
  description = "Whether to deploy HorizontalPodAutoscaler"
  type        = bool
  default     = true
}

variable "hpas" {
  description = "Map of HorizontalPodAutoscalers to create"
  type        = map(any)
  default     = {}
}

# VPA map
variable "vpas" {
  description = "Map of VerticalPodAutoscalers to create"
  type        = map(any)
  default     = {}
}

# Enhanced Cluster Autoscaler variables for unlimited tenant capacity
variable "cluster_autoscaler_namespace" {
  description = "Kubernetes namespace for Cluster Autoscaler deployment"
  type        = string
  default     = "kube-system"
}

variable "scale_down_utilization_threshold" {
  description = "Node utilization level, defined as sum of requested resources divided by capacity, below which a node can be considered for scale down"
  type        = string
  default     = "0.3"  # More aggressive scale-down for cost optimization
}

variable "scale_down_delay_after_add" {
  description = "How long after scale up that scale down evaluation resumes"
  type        = string
  default     = "3m"  # Shorter delay for faster cost optimization
}

variable "scale_down_unneeded_time" {
  description = "How long a node should be unneeded before it is eligible for scale down"
  type        = string
  default     = "2m"  # Aggressive scale-down for cost savings
}

variable "scan_interval" {
  description = "How often cluster is reevaluated for scale up or down"
  type        = string
  default     = "10s"
}

variable "max_node_provision_time" {
  description = "Maximum time CA waits for node to be provisioned"
  type        = string
  default     = "15m"
}

variable "scale_down_enabled" {
  description = "Should CA scale down the cluster"
  type        = string
  default     = "true"
}

variable "expander_strategy" {
  description = "Strategy for selecting node groups to expand"
  type        = string
  default     = "least-waste"
}

variable "max_nodes_total" {
  description = "Maximum number of nodes in all node groups"
  type        = string
  default     = "400"  # Higher capacity for more autoscaling
}

variable "cores_total" {
  description = "Maximum number of cores in the cluster"
  type        = string
  default     = "10000:100000"  # Higher core capacity (min:max)
}

variable "memory_total" {
  description = "Maximum amount of memory in the cluster"
  type        = string
  default     = "20000Gi:400000Gi"  # Higher memory capacity (min:max)
}

variable "cluster_autoscaler_cpu_request" {
  description = "CPU request for cluster autoscaler pod"
  type        = string
  default     = "100m"
}

variable "cluster_autoscaler_memory_request" {
  description = "Memory request for cluster autoscaler pod"
  type        = string
  default     = "300Mi"
}

variable "cluster_autoscaler_cpu_limit" {
  description = "CPU limit for cluster autoscaler pod"
  type        = string
  default     = "200m"
}

variable "cluster_autoscaler_memory_limit" {
  description = "Memory limit for cluster autoscaler pod"
  type        = string
  default     = "600Mi"
}

# Tenant-specific scaling variables
variable "tenant_namespaces_set" {
  description = "Set of tenant namespaces for scaling configuration"
  type        = set(string)
  default     = []
}

variable "max_tenants_per_node" {
  description = "Maximum number of tenants per node"
  type        = number
  default     = 5
}

variable "tenant_cpu_limit" {
  description = "CPU limit per tenant"
  type        = string
  default     = "2"
}

variable "tenant_memory_limit" {
  description = "Memory limit per tenant"
  type        = string
  default     = "4Gi"
}

variable "tenant_scaling_enabled" {
  description = "Enable tenant-specific scaling configurations"
  type        = bool
  default     = true
}
