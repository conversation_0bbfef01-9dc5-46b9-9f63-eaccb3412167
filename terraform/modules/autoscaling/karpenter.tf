# Karpenter implementation for node autoscaling

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

# Create IAM role for Karpenter
resource "aws_iam_role" "karpenter_controller" {
  name = "${var.environment}-karpenter-controller"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Federated = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${replace(var.eks_oidc_provider_url, "https://", "")}"
        }
        Action = "sts:AssumeRoleWithWebIdentity"
        Condition = {
          StringEquals = {
            "${replace(var.eks_oidc_provider_url, "https://", "")}:sub" = "system:serviceaccount:karpenter:karpenter"
          }
        }
      }
    ]
  })
}

# Create IAM policy for Karpenter
resource "aws_iam_policy" "karpenter_controller" {
  name        = "${var.environment}-karpenter-controller"
  description = "IAM policy for Karpenter controller"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ec2:CreateLaunchTemplate",
          "ec2:CreateFleet",
          "ec2:RunInstances",
          "ec2:CreateTags",
          "ec2:TerminateInstances",
          "ec2:DescribeLaunchTemplates",
          "ec2:DescribeInstances",
          "ec2:DescribeSecurityGroups",
          "ec2:DescribeSubnets",
          "ec2:DescribeInstanceTypes",
          "ec2:DescribeInstanceTypeOfferings",
          "ec2:DescribeAvailabilityZones",
          "ec2:DescribeSpotPriceHistory",
          "pricing:GetProducts",
          "ssm:GetParameter"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "iam:PassRole"
        ]
        Resource = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/${var.environment}-karpenter-node-role"
      }
    ]
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "karpenter_controller" {
  role       = aws_iam_role.karpenter_controller.name
  policy_arn = aws_iam_policy.karpenter_controller.arn
}

# Create IAM role for Karpenter nodes
resource "aws_iam_role" "karpenter_node" {
  name = "${var.environment}-karpenter-node-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# Attach policies to node role
resource "aws_iam_role_policy_attachment" "karpenter_node_eks_worker" {
  role       = aws_iam_role.karpenter_node.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy"
}

resource "aws_iam_role_policy_attachment" "karpenter_node_eks_cni" {
  role       = aws_iam_role.karpenter_node.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"
}

resource "aws_iam_role_policy_attachment" "karpenter_node_ecr" {
  role       = aws_iam_role.karpenter_node.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
}

# Create instance profile for Karpenter nodes
resource "aws_iam_instance_profile" "karpenter_node" {
  name = "${var.environment}-karpenter-node-profile"
  role = aws_iam_role.karpenter_node.name
}

# Install Karpenter using Helm
resource "helm_release" "karpenter" {
  count            = var.install_karpenter ? 1 : 0
  name             = "karpenter"
  repository       = "https://charts.karpenter.sh"
  chart            = "karpenter"
  namespace        = "karpenter"
  create_namespace = true
  version          = var.karpenter_chart_version

  set {
    name  = "serviceAccount.annotations.eks\\.amazonaws\\.com/role-arn"
    value = aws_iam_role.karpenter_controller.arn
  }

  set {
    name  = "clusterName"
    value = var.cluster_name
  }

  set {
    name  = "clusterEndpoint"
    value = var.cluster_endpoint
  }

  set {
    name  = "aws.defaultInstanceProfile"
    value = aws_iam_instance_profile.karpenter_node.name
  }
}

# Create Karpenter provisioner for general workloads
resource "kubectl_manifest" "karpenter_provisioner" {
  count     = var.install_karpenter ? 1 : 0
  yaml_body = <<-YAML
apiVersion: karpenter.sh/v1alpha5
kind: Provisioner
metadata:
  name: default
spec:
  requirements:
    - key: karpenter.sh/capacity-type
      operator: In
      values: ["on-demand"]
    - key: kubernetes.io/arch
      operator: In
      values: ["amd64"]
    - key: node.kubernetes.io/instance-type
      operator: In
      values: ${jsonencode(var.instance_types)}
  limits:
    resources:
      cpu: ${var.max_cpu}
      memory: ${var.max_memory}
  provider:
    subnetSelector:
      kubernetes.io/cluster/${var.cluster_name}: owned
    securityGroupSelector:
      kubernetes.io/cluster/${var.cluster_name}: owned
    tags:
      environment: ${var.environment}
      provisioner: default
  ttlSecondsAfterEmpty: 30
  ttlSecondsUntilExpired: 2592000
YAML

  depends_on = [helm_release.karpenter]
}

# Create Karpenter provisioner for tenant workloads with spot instances
resource "kubectl_manifest" "karpenter_tenant_provisioner" {
  count     = var.install_karpenter ? 1 : 0
  yaml_body = <<-YAML
apiVersion: karpenter.sh/v1alpha5
kind: Provisioner
metadata:
  name: tenant-workloads
spec:
  requirements:
    - key: karpenter.sh/capacity-type
      operator: In
      values: ["spot", "on-demand"]
    - key: kubernetes.io/arch
      operator: In
      values: ["amd64"]
    - key: node.kubernetes.io/instance-type
      operator: In
      values: ${jsonencode(var.instance_types)}
  limits:
    resources:
      cpu: ${var.max_cpu}
      memory: ${var.max_memory}
  provider:
    subnetSelector:
      kubernetes.io/cluster/${var.cluster_name}: owned
    securityGroupSelector:
      kubernetes.io/cluster/${var.cluster_name}: owned
    tags:
      environment: ${var.environment}
      provisioner: tenant-workloads
      workload-type: tenant
  ttlSecondsAfterEmpty: 60  # Faster cleanup for tenant workloads
  ttlSecondsUntilExpired: 1800  # 30 minutes for tenant workloads
  taints:
    - key: tenant-workload
      value: "true"
      effect: NoSchedule
YAML

  depends_on = [helm_release.karpenter]
}

# Create Karpenter provisioner for high-priority tenant workloads
resource "kubectl_manifest" "karpenter_priority_provisioner" {
  count     = var.install_karpenter ? 1 : 0
  yaml_body = <<-YAML
apiVersion: karpenter.sh/v1alpha5
kind: Provisioner
metadata:
  name: priority-tenants
spec:
  requirements:
    - key: karpenter.sh/capacity-type
      operator: In
      values: ["on-demand"]
    - key: kubernetes.io/arch
      operator: In
      values: ["amd64"]
    - key: node.kubernetes.io/instance-type
      operator: In
      values: ["m5.large", "m5.xlarge", "m5.2xlarge", "m5.4xlarge"]
  limits:
    resources:
      cpu: 10000
      memory: 40000Gi
  provider:
    subnetSelector:
      kubernetes.io/cluster/${var.cluster_name}: owned
    securityGroupSelector:
      kubernetes.io/cluster/${var.cluster_name}: owned
    tags:
      environment: ${var.environment}
      provisioner: priority-tenants
      workload-type: priority
  ttlSecondsAfterEmpty: 300  # 5 minutes for priority workloads
  ttlSecondsUntilExpired: 3600  # 1 hour for priority workloads
YAML

  depends_on = [helm_release.karpenter]
}