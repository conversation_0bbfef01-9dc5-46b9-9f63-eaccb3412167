# PodDisruptionBudget implementation for tenant workloads

# Create PDBs for tenant deployments
resource "kubectl_manifest" "tenant_pdb" {
  for_each = var.create_default_pdbs ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: ${each.key}-pdb
  namespace: ${each.key}
spec:
  minAvailable: ${var.min_available_percent}%
  selector:
    matchLabels:
      app: ${each.key}
YAML
}
