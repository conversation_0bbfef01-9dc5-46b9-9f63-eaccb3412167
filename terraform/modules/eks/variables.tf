# EKS Cluster Configuration
variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "environment" {
  description = "Environment name (e.g., dev, staging, prod)"
  type        = string
}

variable "kubernetes_version" {
  description = "Kubernetes version to use for the EKS cluster"
  type        = string
  default     = "1.33"
}

variable "subnet_ids" {
  description = "List of subnet IDs for the EKS cluster"
  type        = list(string)
}

variable "vpc_id" {
  description = "ID of the VPC where the EKS cluster will be created"
  type        = string
}

variable "vpc_cidr" {
  description = "CIDR block of the VPC"
  type        = string
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}

# EKS API Access Configuration
variable "endpoint_private_access" {
  description = "Whether to enable private access to the EKS API endpoint"
  type        = bool
  default     = true
}

variable "endpoint_public_access" {
  description = "Whether to enable public access to the EKS API endpoint"
  type        = bool
  default     = false
}

variable "public_access_cidrs" {
  description = "CIDR blocks that can access the EKS API endpoint publicly"
  type        = list(string)
  default     = [] # Empty by default, will use current IP if include_current_ip is true
}

variable "include_current_ip" {
  description = "Whether to include the current IP in the allowed CIDRs"
  type        = bool
  default     = true # Changed to true to automatically include current IP for security
}

# Logging Configuration
variable "create_eks_log_group" {
  description = "Whether to create CloudWatch log group for EKS cluster logs"
  type        = bool
  default     = true
}

variable "log_retention_days" {
  description = "Number of days to retain EKS cluster logs"
  type        = number
  default     = 90
}

# Bastion Host Configuration
variable "bastion_cidr" {
  description = "CIDR block for bastion host"
  type        = string
  default     = ""
}

variable "bastion_security_group_id" {
  description = "Security group ID for bastion host"
  type        = string
  default     = ""
}

variable "bastion_host_iam_role_arn" {
  description = "IAM role ARN for bastion host"
  type        = string
  default     = ""
}

variable "bastion_host_instance_profile" {
  description = "IAM instance profile for bastion host"
  type        = string
  default     = ""
}

variable "bastion_public_ip" {
  description = "Public IP of the bastion host for SSH access to nodes"
  type        = string
  default     = "0.0.0.0"
}

variable "bastion_private_ip" {
  description = "Private IP of the bastion host"
  type        = string
  default     = "0.0.0.0"
}

variable "bastion_instance_id" {
  description = "Instance ID of the bastion host"
  type        = string
  default     = ""
}

variable "bastion_role_arn" {
  description = "ARN of the IAM role for the bastion host"
  type        = string
  default     = ""
}

# EKS Cluster Control
variable "cluster_id" {
  description = "ID of the EKS cluster"
  type        = string
  default     = ""
}

variable "skip_kubernetes_resources" {
  description = "Whether to skip creation of Kubernetes resources"
  type        = bool
  default     = true # Changed to true to avoid Kubernetes connectivity issues
}

# Node Group Configuration - Optimized for minimal resources
variable "desired_size" {
  description = "Desired number of nodes in EKS node groups"
  type        = number
  default     = 1 # Reduced from 2 to 1 for minimal setup
}

variable "max_size" {
  description = "Maximum number of nodes in EKS node groups"
  type        = number
  default     = 2 # Reduced from 3 to 2
}

variable "min_size" {
  description = "Minimum number of nodes in EKS node groups"
  type        = number
  default     = 1
}

variable "instance_types" {
  description = "List of instance types for EKS node groups"
  type        = list(string)
  default     = ["t3a.small"] # Changed to t3a.small for cost optimization
}

variable "node_instance_type" {
  description = "Instance type for EKS node groups"
  type        = string
  default     = "t3a.small" # Changed to t3a.small for cost optimization
}

variable "node_disk_size" {
  description = "Disk size in GB for EKS node groups"
  type        = number
  default     = 10 # Reduced from 20 to 10 GB
}

variable "node_groups" {
  description = "Map of EKS node group configurations"
  type        = map(any)
  default     = {}
}

# Spot Instance Configuration
variable "enable_spot_instances" {
  description = "Whether to enable spot instances for cost optimization"
  type        = bool
  default     = true
}

# Cluster Autoscaler Configuration
variable "enable_cluster_autoscaler" {
  description = "Whether to enable cluster autoscaler"
  type        = bool
  default     = true
}

variable "spot_instance_type" {
  description = "Instance type for spot instances"
  type        = string
  default     = "t3a.medium" # Slightly larger instance for batch workloads
}

variable "spot_max_price" {
  description = "Maximum price to pay for spot instances (empty string means on-demand price)"
  type        = string
  default     = "" # Empty string means on-demand price
}

variable "spot_desired_size" {
  description = "Desired number of spot instances"
  type        = number
  default     = 1 # Default to 1 for cost optimization
}

variable "spot_max_size" {
  description = "Maximum number of spot instances"
  type        = number
  default     = 2
}

variable "spot_min_size" {
  description = "Minimum number of spot instances"
  type        = number
  default     = 1
}

# IAM and Security
variable "eks_cluster_iam_role_arn" {
  description = "ARN of IAM role for EKS cluster (if empty, a role will be created)"
  type        = string
  default     = ""
}

# New variable to control IAM role creation more explicitly
variable "create_iam_role" {
  description = "Whether to create an IAM role for the EKS cluster"
  type        = bool
  default     = true
}

variable "create_security_groups" {
  description = "Whether to create security groups for EKS"
  type        = bool
  default     = true
}

variable "create_node_groups" {
  description = "Whether to create EKS node groups"
  type        = bool
  default     = true
}

variable "create_iam_policies" {
  description = "Whether to create IAM policies for EKS"
  type        = bool
  default     = true
}

variable "iam_role_name_prefix" {
  description = "Prefix for IAM role names"
  type        = string
  default     = ""
}

variable "kms_key_arn" {
  description = "ARN of KMS key for EKS cluster encryption"
  type        = string
  default     = ""
}

# Cluster Creation Control
variable "create_eks" {
  description = "Whether to create an EKS cluster or use an existing one"
  type        = bool
  default     = true
}

variable "check_if_cluster_exists" {
  description = "Whether to check if the EKS cluster already exists before attempting to create it"
  type        = bool
  default     = true
}

variable "create_oidc_provider" {
  description = "Whether to create an OIDC provider for the EKS cluster"
  type        = bool
  default     = false
}

# Network and Security Group Configuration
variable "enable_network_policies" {
  description = "Whether to enable network policies in the EKS cluster"
  type        = bool
  default     = false
}

variable "create_endpoints" {
  description = "Whether to create VPC endpoints"
  type        = bool
  default     = false
}

variable "security_group_name" {
  description = "Name of the additional security group for EKS cluster"
  type        = string
  default     = ""
}

variable "security_group_description" {
  description = "Description of the additional security group for EKS cluster"
  type        = string
  default     = ""
}

variable "security_group_ingress" {
  description = "Security group ingress rules for EKS cluster"
  type        = any
  default     = {}
}

variable "security_group_egress" {
  description = "Security group egress rules for EKS cluster"
  type        = any
  default     = {}
}

variable "monitoring_bastion_cidr" {
  description = "CIDR block for monitoring bastion host"
  type        = string
  default     = ""
}

# Region Configuration
variable "aws_region" {
  description = "AWS region for all resources"
  type        = string
  default     = "eu-central-1"
}

# External Cluster Configuration (when create_eks is false)
variable "cluster_endpoint" {
  description = "Endpoint of an existing EKS cluster (when create_eks is false)"
  type        = string
  default     = ""
}

variable "cluster_ca_certificate" {
  description = "CA certificate of an existing EKS cluster (when create_eks is false)"
  type        = string
  default     = ""
}

variable "cluster_auth_token" {
  description = "Auth token of an existing EKS cluster (when create_eks is false)"
  type        = string
  default     = ""
  sensitive   = true
}

# Access Control Configuration
variable "allowed_cidr_blocks" {
  description = "List of CIDR blocks to allow access to the cluster API"
  type        = list(string)
  default     = []
}

# Network Validation Configuration
variable "validate_network" {
  description = "Whether to validate network configuration"
  type        = bool
  default     = false
}

# VPC Endpoint Validation Configuration
variable "enable_vpc_endpoint_validation" {
  description = "Whether to enable VPC endpoint validation (independent of vpc_id)"
  type        = bool
  default     = false
}

# New variable to provide subnet CIDRs directly
variable "validate_subnet_cidrs" {
  description = "List of subnet CIDRs to validate (to avoid for_each with computed values)"
  type        = list(string)
  default     = []
}

variable "private_route_table_ids" {
  description = "List of private route table IDs"
  type        = list(string)
  default     = []
}

variable "route_table_ids" {
  description = "List of route table IDs for VPC endpoints"
  type        = list(string)
  default     = []
}

variable "skip_eks_connectivity_check" {
  description = "Whether to skip EKS cluster connectivity checks"
  type        = bool
  default     = false
}

variable "create_s3_vpc_endpoint" {
  description = "Whether to create an S3 VPC endpoint in the EKS module. Set to false if the VPC module is already creating one."
  type        = bool
  default     = true
}

variable "external_oidc_provider_arn" {
  description = "ARN of an external OIDC provider to use instead of creating one"
  type        = string
  default     = ""
}

variable "create_backend_pod_role" {
  description = "Whether to create the backend pod IAM role for IRSA"
  type        = bool
  default     = false
}

variable "map_roles" {
  description = "Additional IAM roles to add to the aws-auth ConfigMap"
  type        = list(any)
  default     = []
}

variable "map_users" {
  description = "Additional IAM users to add to the aws-auth ConfigMap"
  type        = list(any)
  default     = []
}

variable "map_accounts" {
  description = "Additional AWS account numbers to add to the aws-auth ConfigMap"
  type        = list(string)
  default     = []
}
