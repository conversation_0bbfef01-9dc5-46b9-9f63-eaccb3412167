# Data sources are defined in data.tf

# Create IAM role for EKS node group
resource "aws_iam_role" "eks_node_role" {
  count = 0 # Set to 0 to use the existing role
  name  = "${var.cluster_name}-node-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  # Note: inline_policy is deprecated, using separate policy attachment instead

  tags = var.tags

  # Add lifecycle policy to handle existing resources
  lifecycle {
    create_before_destroy = true
  }
}

# Attach required policies to the node role
resource "aws_iam_role_policy_attachment" "eks_node_AmazonEKSWorkerNodePolicy" {
  count      = 0 # Set to 0 to use the existing role
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy"
  role       = "${var.cluster_name}-node-role"
}

resource "aws_iam_role_policy_attachment" "eks_node_AmazonEKS_CNI_Policy" {
  count      = 0 # Set to 0 to use the existing role
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"
  role       = "${var.cluster_name}-node-role"
}

resource "aws_iam_role_policy_attachment" "eks_node_AmazonEC2ContainerRegistryReadOnly" {
  count      = 0 # Set to 0 to use the existing role
  policy_arn = "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly"
  role       = "${var.cluster_name}-node-role"
}

# Add SSM policy for node access
resource "aws_iam_role_policy_attachment" "eks_node_AmazonSSMManagedInstanceCore" {
  count      = 0 # Set to 0 to use the existing role
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  role       = "${var.cluster_name}-node-role"
}

# Using the EKS AMI data source from data.tf

# Create launch template with minimal configuration
resource "aws_launch_template" "eks_node_template" {
  count         = var.create_eks ? 1 : 0
  name_prefix   = "${var.cluster_name}-node-"
  image_id      = data.aws_ssm_parameter.eks_ami.value
  instance_type = var.node_instance_type

  # Enhanced user data script for unlimited pod capacity
  user_data = base64encode(<<-EOF
    #!/bin/bash
    set -o xtrace
    /etc/eks/bootstrap.sh ${var.cluster_name} \
      --b64-cluster-ca ${local.cluster_ca_certificate} \
      --apiserver-endpoint ${local.cluster_endpoint} \
      --kubelet-extra-args "--max-pods=110 --system-reserved=cpu=100m,memory=100Mi --kube-reserved=cpu=100m,memory=100Mi"
  EOF
  )

  block_device_mappings {
    device_name = "/dev/xvda"
    ebs {
      volume_size           = var.node_disk_size
      volume_type           = "gp3"
      delete_on_termination = true
      encrypted             = true
    }
  }

  tag_specifications {
    resource_type = "instance"
    tags = merge(var.tags, {
      Name = "${var.cluster_name}-node"
    })
  }

  # Simplified metadata options
  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Optimized node group configuration for on-demand instances
resource "aws_eks_node_group" "main" {
  # Create node groups regardless of whether the cluster exists
  count           = var.create_node_groups ? 1 : 0
  cluster_name    = var.cluster_name
  node_group_name = "${var.cluster_name}-node-group"
  node_role_arn   = "arn:aws:iam::545009857703:role/prod-architrave-eks-node-role"
  subnet_ids      = var.subnet_ids

  # Version is managed by the launch template AMI

  scaling_config {
    desired_size = var.desired_size
    max_size     = var.max_size
    min_size     = var.min_size
  }

  # Use launch template with specific version
  launch_template {
    id      = aws_launch_template.eks_node_template[0].id
    version = aws_launch_template.eks_node_template[0].latest_version
  }

  # Optimize update config for faster updates
  update_config {
    max_unavailable = 1
  }

  # Add labels and taints for node affinity
  labels = {
    "node-type" = "on-demand"
    "workload"  = "critical"
  }

  # Simplified tags for better performance with cluster autoscaler support
  tags = merge(var.tags, {
    "kubernetes.io/cluster/${var.cluster_name}" = "owned"
    "eks:cluster-name"                          = var.cluster_name
    "node-type"                                 = "on-demand"
    "k8s.io/cluster-autoscaler/enabled"        = "true"
    "k8s.io/cluster-autoscaler/${var.cluster_name}" = "owned"
  })

  # Ensure IAM role permissions are created before and available during node creation
  # Also ensure the EKS cluster is created before the node group
  depends_on = [
    aws_iam_role_policy_attachment.eks_node_AmazonEKSWorkerNodePolicy,
    aws_iam_role_policy_attachment.eks_node_AmazonEKS_CNI_Policy,
    aws_iam_role_policy_attachment.eks_node_AmazonEC2ContainerRegistryReadOnly,
    aws_iam_role_policy_attachment.eks_node_AmazonSSMManagedInstanceCore,
    aws_eks_cluster.this,
    time_sleep.wait_for_cluster
  ]

  # Allow Terraform to create a new node group before destroying the previous one
  lifecycle {
    create_before_destroy = true
  }
}

# Create launch template for spot instances
resource "aws_launch_template" "eks_spot_template" {
  count         = var.create_eks && var.enable_spot_instances ? 1 : 0
  name_prefix   = "${var.cluster_name}-spot-"
  image_id      = data.aws_ssm_parameter.eks_ami.value
  instance_type = var.spot_instance_type

  # Spot instances are configured at the node group level, not in the launch template
  # Removed instance_market_options as it's not compatible with EKS node groups

  # Enhanced user data script for unlimited pod capacity
  user_data = base64encode(<<-EOF
    #!/bin/bash
    set -o xtrace
    /etc/eks/bootstrap.sh ${var.cluster_name} \
      --b64-cluster-ca ${local.cluster_ca_certificate} \
      --apiserver-endpoint ${local.cluster_endpoint} \
      --kubelet-extra-args "--max-pods=110 --system-reserved=cpu=100m,memory=100Mi --kube-reserved=cpu=100m,memory=100Mi --node-labels=node-type=spot,workload=general"
  EOF
  )

  block_device_mappings {
    device_name = "/dev/xvda"
    ebs {
      volume_size           = var.node_disk_size
      volume_type           = "gp3"
      delete_on_termination = true
      encrypted             = true
    }
  }

  tag_specifications {
    resource_type = "instance"
    tags = merge(var.tags, {
      Name = "${var.cluster_name}-spot-node"
    })
  }

  # Simplified metadata options
  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Spot instance node group for non-critical workloads
resource "aws_eks_node_group" "spot" {
  # Create spot node groups if enabled
  count           = var.create_node_groups && var.enable_spot_instances ? 1 : 0
  cluster_name    = var.cluster_name
  node_group_name = "${var.cluster_name}-spot-node-group"
  node_role_arn   = "arn:aws:iam::545009857703:role/prod-architrave-eks-node-role"
  subnet_ids      = var.subnet_ids
  instance_types  = [var.spot_instance_type]
  capacity_type   = "SPOT" # Use SPOT capacity type instead of launch template

  # Version is managed by the AMI release version
  ami_type        = "AL2_x86_64"
  release_version = null # Use latest compatible version

  scaling_config {
    desired_size = var.spot_desired_size
    max_size     = var.spot_max_size
    min_size     = var.spot_min_size
  }

  # Removed launch template reference as we're using capacity_type instead

  # Optimize update config for faster updates
  update_config {
    max_unavailable = 1
  }

  # Add labels for node affinity
  labels = {
    "node-type" = "spot"
    "workload"  = "general"
  }

  # Simplified tags for better performance with cluster autoscaler support
  tags = merge(var.tags, {
    "kubernetes.io/cluster/${var.cluster_name}" = "owned"
    "eks:cluster-name"                          = var.cluster_name
    "node-type"                                 = "spot"
    "k8s.io/cluster-autoscaler/enabled"        = "true"
    "k8s.io/cluster-autoscaler/${var.cluster_name}" = "owned"
  })

  # Ensure IAM role permissions are created before and available during node creation
  # Also ensure the EKS cluster is created before the node group
  depends_on = [
    aws_iam_role_policy_attachment.eks_node_AmazonEKSWorkerNodePolicy,
    aws_iam_role_policy_attachment.eks_node_AmazonEKS_CNI_Policy,
    aws_iam_role_policy_attachment.eks_node_AmazonEC2ContainerRegistryReadOnly,
    aws_iam_role_policy_attachment.eks_node_AmazonSSMManagedInstanceCore,
    aws_eks_cluster.this,
    time_sleep.wait_for_cluster
  ]

  # Allow Terraform to create a new node group before destroying the previous one
  lifecycle {
    create_before_destroy = true
  }
}


# Add IAM policy for cluster autoscaler
resource "aws_iam_policy" "cluster_autoscaler" {
  count       = var.create_iam_policies ? 1 : 0
  name        = "${var.cluster_name}-cluster-autoscaler"
  description = "Policy for cluster autoscaler"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "autoscaling:DescribeAutoScalingGroups",
          "autoscaling:DescribeAutoScalingInstances",
          "autoscaling:DescribeLaunchConfigurations",
          "autoscaling:DescribeTags",
          "ec2:DescribeLaunchTemplateVersions"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "autoscaling:SetDesiredCapacity",
          "autoscaling:TerminateInstanceInAutoScalingGroup"
        ]
        Resource = "arn:aws:autoscaling:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:autoScalingGroup:*:autoScalingGroupName/eks-${var.cluster_name}-*"
        Effect   = "Allow"
      }
    ]
  })

  # Add lifecycle policy to handle existing resources
  lifecycle {
    create_before_destroy = true
    ignore_changes        = [policy, description]
    # prevent_destroy has been removed to allow updates
  }
}

resource "aws_iam_role_policy_attachment" "cluster_autoscaler" {
  count      = var.create_iam_policies ? 1 : 0
  policy_arn = aws_iam_policy.cluster_autoscaler[0].arn
  role       = "prod-architrave-eks-node-role"
}

# Update the autoscaling policy to be more specific
resource "aws_iam_policy" "node_group_autoscaling" {
  count       = var.create_iam_policies ? 1 : 0
  name        = "${var.cluster_name}-node-group-autoscaling"
  description = "Autoscaling policy for EKS node group"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "autoscaling:SetDesiredCapacity",
          "autoscaling:TerminateInstanceInAutoScalingGroup"
        ]
        Resource = "arn:aws:autoscaling:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:autoScalingGroup:*:autoScalingGroupName/${var.cluster_name}-*"
      }
    ]
  })

  # Add lifecycle policy to handle existing resources
  lifecycle {
    create_before_destroy = true
    ignore_changes        = [policy, description]
    # prevent_destroy has been removed to allow updates
  }
}

# Update the IAM PassRole policy to be more specific
resource "aws_iam_policy" "node_group_pass_role" {
  count       = var.create_iam_policies ? 1 : 0
  name        = "${var.cluster_name}-node-group-pass-role"
  description = "PassRole policy for EKS node group"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect   = "Allow"
        Action   = "iam:PassRole"
        Resource = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/${var.cluster_name}-*"
      }
    ]
  })

  # Add lifecycle policy to handle existing resources
  lifecycle {
    create_before_destroy = true
    ignore_changes        = [policy, description]
    # prevent_destroy has been removed to allow updates
  }
}

# Attach the PassRole policy to the node role
resource "aws_iam_role_policy_attachment" "node_group_pass_role" {
  count      = var.create_iam_policies ? 1 : 0
  policy_arn = aws_iam_policy.node_group_pass_role[0].arn
  role       = "prod-architrave-eks-node-role"
}
