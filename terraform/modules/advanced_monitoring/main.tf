/**
 * # Advanced Monitoring Module
 *
 * This module implements advanced monitoring with Prometheus, Grafana, Loki, and Jaeger.
 * It creates the necessary resources to monitor tenants.
 */

terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.0"
    }
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

# Data sources
data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

# Local variables
locals {
  name_prefix = "${var.environment}-${var.name}"
  tags = merge(
    var.tags,
    {
      "Environment" = var.environment
      "ManagedBy"   = "terraform"
      "Name"        = local.name_prefix
    }
  )

  # Common labels
  common_labels = {
    "app.kubernetes.io/managed-by" = "terraform"
    "app.kubernetes.io/part-of"    = "advanced-monitoring"
  }

  # Monitoring namespace
  monitoring_namespace = var.create_namespace ? kubernetes_namespace.monitoring[0].metadata[0].name : var.namespace
}

# Create KMS key for monitoring encryption
resource "aws_kms_key" "monitoring_key" {
  description             = "${var.environment} monitoring encryption key"
  deletion_window_in_days = 30
  enable_key_rotation     = true

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Enable IAM User Permissions"
        Effect = "Allow"
        Principal = {
          AWS = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:root"
        }
        Action   = "kms:*"
        Resource = "*"
      },
      {
        Sid    = "Allow monitoring to use the key"
        Effect = "Allow"
        Principal = {
          AWS = aws_iam_role.monitoring_role.arn
        }
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ]
        Resource = "*"
      }
    ]
  })

  tags = local.tags
}

# Create KMS alias for monitoring encryption key
resource "aws_kms_alias" "monitoring_key_alias" {
  name          = "alias/${var.environment}-monitoring-key"
  target_key_id = aws_kms_key.monitoring_key.key_id
}

# Create IAM role for monitoring
resource "aws_iam_role" "monitoring_role" {
  name = "${var.environment}-monitoring-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRoleWithWebIdentity"
        Effect = "Allow"
        Principal = {
          Federated = "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/${var.eks_oidc_provider}"
        }
        Condition = {
          StringEquals = {
            "${var.eks_oidc_provider}:sub" = "system:serviceaccount:${local.monitoring_namespace}:monitoring-sa"
          }
        }
      }
    ]
  })

  tags = local.tags
}

# Create IAM policy for monitoring
resource "aws_iam_policy" "monitoring_policy" {
  name        = "${var.environment}-monitoring-policy"
  description = "Policy for monitoring"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "CloudWatchAccess"
        Effect = "Allow"
        Action = [
          "cloudwatch:PutMetricData",
          "cloudwatch:GetMetricData",
          "cloudwatch:PutDashboard",
          "cloudwatch:DeleteDashboards",
          "cloudwatch:PutMetricAlarm",
          "cloudwatch:DeleteAlarms"
        ]
        Resource = "*"
      },
      {
        Sid    = "S3Access"
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObject",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::${var.environment}-monitoring-*",
          "arn:aws:s3:::${var.environment}-monitoring-*/*"
        ]
      },
      {
        Sid    = "KMSAccess"
        Effect = "Allow"
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey"
        ]
        Resource = [
          aws_kms_key.monitoring_key.arn
        ]
      }
    ]
  })
}

# Attach IAM policy to monitoring role
resource "aws_iam_role_policy_attachment" "monitoring_policy_attachment" {
  role       = aws_iam_role.monitoring_role.name
  policy_arn = aws_iam_policy.monitoring_policy.arn
}

# Create S3 bucket for monitoring
resource "aws_s3_bucket" "monitoring" {
  bucket = "${var.environment}-monitoring-${data.aws_caller_identity.current.account_id}"

  tags = local.tags
}

# Configure S3 bucket encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "monitoring" {
  bucket = aws_s3_bucket.monitoring.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = aws_kms_key.monitoring_key.arn
      sse_algorithm     = "aws:kms"
    }
  }
}

# Block public access to S3 bucket
resource "aws_s3_bucket_public_access_block" "monitoring" {
  bucket = aws_s3_bucket.monitoring.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Create Kubernetes namespace for monitoring
resource "kubernetes_namespace" "monitoring" {
  count = var.create_namespace ? 1 : 0

  metadata {
    name = var.namespace

    labels = merge(local.common_labels, {
      name = var.namespace
    })
  }
}

# Create Kubernetes service account for monitoring
resource "kubernetes_service_account" "monitoring" {
  metadata {
    name      = "monitoring-sa"
    namespace = local.monitoring_namespace

    annotations = {
      "eks.amazonaws.com/role-arn" = aws_iam_role.monitoring_role.arn
    }

    labels = local.common_labels
  }

  depends_on = [kubernetes_namespace.monitoring]
}

# Install Prometheus using Helm
resource "helm_release" "prometheus" {
  count = var.enable_prometheus ? 1 : 0

  name       = "prometheus"
  repository = "https://prometheus-community.github.io/helm-charts"
  chart      = "prometheus"
  version    = var.prometheus_version
  namespace  = local.monitoring_namespace

  values = [
    yamlencode({
      server = {
        persistentVolume = {
          enabled = true
          size    = var.prometheus_storage_size
        }
        retention = var.prometheus_retention
        resources = var.prometheus_resources
      }
      alertmanager = {
        persistentVolume = {
          enabled = true
          size    = "10Gi"
        }
        resources = {
          limits = {
            cpu    = "100m"
            memory = "256Mi"
          }
          requests = {
            cpu    = "50m"
            memory = "128Mi"
          }
        }
      }
      serviceAccounts = {
        server = {
          name = kubernetes_service_account.monitoring.metadata[0].name
          annotations = {
            "eks.amazonaws.com/role-arn" = aws_iam_role.monitoring_role.arn
          }
        }
      }
      serverFiles = {
        "prometheus.yml" = {
          scrape_configs = [
            {
              job_name = "kubernetes-pods"
              kubernetes_sd_configs = [
                {
                  role = "pod"
                }
              ]
              relabel_configs = [
                {
                  source_labels = ["__meta_kubernetes_pod_annotation_prometheus_io_scrape"]
                  action        = "keep"
                  regex         = "true"
                },
                {
                  source_labels = ["__meta_kubernetes_pod_annotation_prometheus_io_path"]
                  action        = "replace"
                  target_label  = "__metrics_path__"
                  regex         = "(.+)"
                },
                {
                  source_labels = ["__address__", "__meta_kubernetes_pod_annotation_prometheus_io_port"]
                  action        = "replace"
                  regex         = "([^:]+)(?::\\d+)?;(\\d+)"
                  replacement   = "$1:$2"
                  target_label  = "__address__"
                },
                {
                  source_labels = ["__meta_kubernetes_namespace"]
                  action        = "replace"
                  target_label  = "kubernetes_namespace"
                },
                {
                  source_labels = ["__meta_kubernetes_pod_name"]
                  action        = "replace"
                  target_label  = "kubernetes_pod_name"
                },
                {
                  source_labels = ["__meta_kubernetes_pod_label_tenant_architrave_io_tenant_id"]
                  action        = "replace"
                  target_label  = "tenant_id"
                }
              ]
            }
          ]
        }
      }
    })
  ]

  depends_on = [
    kubernetes_namespace.monitoring,
    kubernetes_service_account.monitoring
  ]
}

# Install Grafana using Helm
resource "helm_release" "grafana" {
  count = var.enable_grafana ? 1 : 0

  name       = "grafana"
  repository = "https://grafana.github.io/helm-charts"
  chart      = "grafana"
  version    = var.grafana_version
  namespace  = local.monitoring_namespace

  values = [
    yamlencode({
      persistence = {
        enabled = true
        size    = var.grafana_storage_size
      }
      resources = var.grafana_resources
      serviceAccount = {
        name = kubernetes_service_account.monitoring.metadata[0].name
        annotations = {
          "eks.amazonaws.com/role-arn" = aws_iam_role.monitoring_role.arn
        }
      }
      datasources = {
        "datasources.yaml" = {
          apiVersion = 1
          datasources = [
            {
              name      = "Prometheus"
              type      = "prometheus"
              url       = "http://prometheus-server.${local.monitoring_namespace}.svc.cluster.local:80"
              access    = "proxy"
              isDefault = true
            },
            {
              name   = "Loki"
              type   = "loki"
              url    = "http://loki.${local.monitoring_namespace}.svc.cluster.local:3100"
              access = "proxy"
            }
          ]
        }
      }
    })
  ]

  depends_on = [
    kubernetes_namespace.monitoring,
    kubernetes_service_account.monitoring,
    helm_release.prometheus
  ]
}

# Install Loki using Helm
resource "helm_release" "loki" {
  count = var.enable_loki ? 1 : 0

  name       = "loki"
  repository = "https://grafana.github.io/helm-charts"
  chart      = "loki"
  version    = var.loki_version
  namespace  = local.monitoring_namespace

  values = [
    yamlencode({
      persistence = {
        enabled = true
        size    = var.loki_storage_size
      }
      resources = var.loki_resources
      serviceAccount = {
        name = kubernetes_service_account.monitoring.metadata[0].name
        annotations = {
          "eks.amazonaws.com/role-arn" = aws_iam_role.monitoring_role.arn
        }
      }
      config = {
        storage_config = {
          aws = {
            s3               = var.loki_s3_enabled ? "s3://${data.aws_region.current.name}/${aws_s3_bucket.monitoring.bucket}/loki" : null
            s3forcepathstyle = true
          }
        }
      }
    })
  ]

  depends_on = [
    kubernetes_namespace.monitoring,
    kubernetes_service_account.monitoring
  ]
}

# Install Jaeger using Helm
resource "helm_release" "jaeger" {
  count = var.enable_jaeger ? 1 : 0

  name       = "jaeger"
  repository = "https://jaegertracing.github.io/helm-charts"
  chart      = "jaeger"
  version    = var.jaeger_version
  namespace  = local.monitoring_namespace

  values = [
    yamlencode({
      persistence = {
        enabled = true
        size    = var.jaeger_storage_size
      }
      resources = var.jaeger_resources
      serviceAccount = {
        name = kubernetes_service_account.monitoring.metadata[0].name
        annotations = {
          "eks.amazonaws.com/role-arn" = aws_iam_role.monitoring_role.arn
        }
      }
      storage = {
        type = "elasticsearch"
        elasticsearch = {
          host = var.elasticsearch_host
          port = var.elasticsearch_port
        }
      }
    })
  ]

  depends_on = [
    kubernetes_namespace.monitoring,
    kubernetes_service_account.monitoring
  ]
}
