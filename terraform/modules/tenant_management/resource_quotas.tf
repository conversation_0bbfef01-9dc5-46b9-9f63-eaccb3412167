# Resource quotas for tenant namespaces

# Create resource quotas for tenant namespaces
resource "kubectl_manifest" "tenant_resource_quota" {
  for_each = var.tenant_resource_quotas ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: v1
kind: ResourceQuota
metadata:
  name: ${each.key}-quota
  namespace: ${each.key}
spec:
  hard:
    requests.cpu: ${var.tenant_default_cpu_request}
    limits.cpu: ${var.tenant_default_cpu_limit}
    requests.memory: ${var.tenant_default_memory_request}
    limits.memory: ${var.tenant_default_memory_limit}
    requests.storage: ${var.tenant_default_storage_request}
    persistentvolumeclaims: "10"
    pods: "20"
    services: "10"
    configmaps: "20"
    secrets: "20"
YAML
}

# Create limit range for tenant namespaces
resource "kubectl_manifest" "tenant_limit_range" {
  for_each = var.tenant_resource_quotas ? toset(var.tenant_namespaces) : []

  yaml_body = <<-YAML
apiVersion: v1
kind: LimitRange
metadata:
  name: ${each.key}-limits
  namespace: ${each.key}
spec:
  limits:
  - default:
      cpu: "500m"
      memory: "512Mi"
    defaultRequest:
      cpu: "100m"
      memory: "256Mi"
    type: Container
YAML
}
