output "operator_namespace" {
  description = "Namespace where the tenant operator is deployed"
  value       = local.operator_namespace
}

output "operator_service_name" {
  description = "Name of the tenant operator service"
  value       = !var.skip_k8s_connection ? kubernetes_service.operator[0].metadata[0].name : "tenant-operator"
}

output "tenant_namespaces" {
  description = "Map of tenant IDs to their namespaces"
  value = !var.skip_k8s_connection ? {
    for tenant_key, tenant in var.tenants : tenant_key => "${tenant.namespace_prefix}-${tenant.id}"
  } : {}
}

output "tenant_count" {
  description = "Number of tenants created"
  value       = length(var.tenants)
}
