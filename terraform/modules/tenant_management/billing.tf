# Tenant billing and chargeback

# Data sources
data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

locals {
  account_id     = data.aws_caller_identity.current.account_id
  region         = data.aws_region.current.region
  billing_bucket = var.billing_report_bucket != "" ? var.billing_report_bucket : "${var.environment}-tenant-billing-${local.account_id}"
}

# Create S3 bucket for billing reports
resource "aws_s3_bucket" "tenant_billing" {
  count  = var.enable_tenant_billing ? 1 : 0
  bucket = local.billing_bucket

  tags = {
    Name        = "${var.environment}-tenant-billing"
    Environment = var.environment
  }
}

# Enable versioning for billing reports
resource "aws_s3_bucket_versioning" "tenant_billing" {
  count  = var.enable_tenant_billing ? 1 : 0
  bucket = aws_s3_bucket.tenant_billing[0].id

  versioning_configuration {
    status = "Enabled"
  }
}

# Enable encryption for billing reports
resource "aws_s3_bucket_server_side_encryption_configuration" "tenant_billing" {
  count  = var.enable_tenant_billing ? 1 : 0
  bucket = aws_s3_bucket.tenant_billing[0].id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# Block public access to billing reports
resource "aws_s3_bucket_public_access_block" "tenant_billing" {
  count  = var.enable_tenant_billing ? 1 : 0
  bucket = aws_s3_bucket.tenant_billing[0].id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Create IAM role for billing exporter
resource "aws_iam_role" "tenant_billing_exporter" {
  count = var.enable_tenant_billing ? 1 : 0
  name  = "${var.environment}-tenant-billing-exporter"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "eks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# Create IAM policy for billing exporter
resource "aws_iam_policy" "tenant_billing_exporter" {
  count       = var.enable_tenant_billing ? 1 : 0
  name        = "${var.environment}-tenant-billing-exporter"
  description = "IAM policy for tenant billing exporter"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObject",
          "s3:ListBucket"
        ]
        Resource = [
          "arn:aws:s3:::${local.billing_bucket}",
          "arn:aws:s3:::${local.billing_bucket}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "ce:GetCostAndUsage",
          "ce:GetDimensionValues",
          "ce:GetTags"
        ]
        Resource = "*"
      }
    ]
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "tenant_billing_exporter" {
  count      = var.enable_tenant_billing ? 1 : 0
  role       = aws_iam_role.tenant_billing_exporter[0].name
  policy_arn = aws_iam_policy.tenant_billing_exporter[0].arn
}

# Create CronJob for billing export
resource "kubectl_manifest" "tenant_billing_cronjob" {
  count     = var.enable_tenant_billing ? 1 : 0
  yaml_body = <<-YAML
apiVersion: batch/v1
kind: CronJob
metadata:
  name: tenant-billing-exporter
  namespace: kube-system
spec:
  schedule: "0 0 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: tenant-billing-exporter
          containers:
          - name: billing-exporter
            image: amazon/aws-cli:latest
            command:
            - /bin/sh
            - -c
            - |
              aws ce get-cost-and-usage \
                --time-period Start=$(date -d "yesterday" +%Y-%m-%d),End=$(date +%Y-%m-%d) \
                --granularity DAILY \
                --metrics "BlendedCost" "UnblendedCost" "UsageQuantity" \
                --group-by Type=DIMENSION,Key=SERVICE Type=TAG,Key=tenant \
                --output json > /tmp/billing-report.json && \
              aws s3 cp /tmp/billing-report.json s3://${local.billing_bucket}/${var.billing_report_prefix}/$(date +%Y-%m-%d).json
            env:
            - name: AWS_REGION
              value: ${local.region}
          restartPolicy: OnFailure
YAML

  depends_on = [aws_s3_bucket.tenant_billing]
}

# Create ServiceAccount for billing exporter
resource "kubectl_manifest" "tenant_billing_service_account" {
  count     = var.enable_tenant_billing ? 1 : 0
  yaml_body = <<-YAML
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-billing-exporter
  namespace: kube-system
  annotations:
    eks.amazonaws.com/role-arn: ${aws_iam_role.tenant_billing_exporter[0].arn}
YAML

  depends_on = [aws_iam_role.tenant_billing_exporter]
}
