resource "aws_instance" "eks_bastion" {
  count                       = var.enable_bastion ? 1 : 0
  associate_public_ip_address = true
  ami                         = data.aws_ami.amazon_linux.id
  instance_type               = var.instance_type
  key_name                    = var.ssh_key_name
  subnet_id                   = var.subnet_id
  vpc_security_group_ids      = [aws_security_group.bastion_sg[0].id]
  iam_instance_profile        = "${var.environment}-eks-bastion-profile"

  user_data = <<-EOF
    #!/bin/bash
    set -e
    exec > >(tee /var/log/user-data.log|logger -t user-data -s 2>/dev/console) 2>&1
    echo "Starting bastion host setup..."
    yum update -y
    yum install -y jq gettext bash-completion unzip git
    sed -i 's/^PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
    sed -i 's/^PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
    systemctl restart sshd

    # Install kubectl globally for all users
    echo "Installing kubectl..."
    curl -sSLo /usr/local/bin/kubectl https://amazon-eks.s3.us-west-2.amazonaws.com/1.32.0/2024-01-04/bin/linux/amd64/kubectl
    chmod +x /usr/local/bin/kubectl
    echo "Kubectl version:"
    /usr/local/bin/kubectl version --client

    # Add kubectl to PATH for all users
    echo 'export PATH=$PATH:/usr/local/bin' > /etc/profile.d/kubectl.sh
    chmod +x /etc/profile.d/kubectl.sh

    # Create symbolic link in common bin directories
    ln -sf /usr/local/bin/kubectl /usr/bin/kubectl
    curl -sSL "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
    unzip -q awscliv2.zip
    ./aws/install
    curl -sSL https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3 | bash
    curl -sSL https://github.com/derailed/k9s/releases/latest/download/k9s_Linux_amd64.tar.gz | tar xz -C /tmp
    mv /tmp/k9s /usr/local/bin
    # Install kubectl
    echo "Installing kubectl..."
    curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
    chmod +x kubectl
    mv kubectl /usr/local/bin/

    # Install eksctl
    echo "Installing eksctl..."
    curl --silent --location "https://github.com/weaveworks/eksctl/releases/latest/download/eksctl_$(uname -s)_amd64.tar.gz" | tar xz -C /tmp
    mv /tmp/eksctl /usr/local/bin

    # Install k9s for better cluster management
    echo "Installing k9s..."
    curl -L https://github.com/derailed/k9s/releases/download/v0.27.4/k9s_Linux_amd64.tar.gz | tar xz -C /tmp
    mv /tmp/k9s /usr/local/bin

    # Always configure kubectl for the EKS cluster
    echo "Configuring kubectl for EKS cluster..."
    mkdir -p /root/.kube
    aws eks update-kubeconfig --name ${var.eks_cluster_name} --region ${var.region} --kubeconfig /root/.kube/config

    # Create kubeconfig for ec2-user
    mkdir -p /home/<USER>/.kube
    aws eks update-kubeconfig --name ${var.eks_cluster_name} --region ${var.region} --kubeconfig /home/<USER>/.kube/config
    chown -R ec2-user:ec2-user /home/<USER>/.kube

    # Create a script to automatically connect to EKS
    cat > /usr/local/bin/connect-eks <<'EKSSCRIPT'
#!/bin/bash
# Script to connect to EKS cluster
aws eks update-kubeconfig --name ${var.eks_cluster_name} --region ${var.region}
echo "Connected to EKS cluster ${var.eks_cluster_name}"
echo "You can now use kubectl to interact with the cluster"
echo "Checking cluster nodes:"
kubectl get nodes
echo ""
echo "Checking cluster pods:"
kubectl get pods --all-namespaces
EKSSCRIPT

    chmod +x /usr/local/bin/connect-eks

    # Create a welcome message with instructions
    cat > /etc/motd <<'MOTD'
=======================================================================
Welcome to the EKS Bastion Host
=======================================================================

This bastion host is configured to connect to the EKS cluster:
  - Cluster Name: ${var.eks_cluster_name}
  - Region: ${var.region}

To connect to the EKS cluster, run:
  $ connect-eks

Common kubectl commands:
  $ kubectl get nodes
  $ kubectl get pods --all-namespaces
  $ kubectl get svc --all-namespaces

For better cluster management, use k9s:
  $ k9s

For more information, visit:
  https://kubernetes.io/docs/reference/kubectl/cheatsheet/
=======================================================================
MOTD

    # Run connect-eks on login
    echo "connect-eks" >> /home/<USER>/.bashrc

    # Add kubectl autocomplete
    echo 'source <(kubectl completion bash)' >> /etc/bashrc
    echo 'alias k=kubectl' >> /etc/bashrc
    echo 'complete -o default -F __start_kubectl k' >> /etc/bashrc

    yum install -y https://s3.amazonaws.com/ec2-downloads-windows/SSMAgent/latest/linux_amd64/amazon-ssm-agent.rpm
    systemctl enable amazon-ssm-agent
    systemctl start amazon-ssm-agent
    mkdir -p /etc/amazon/ssm
    cat > /etc/amazon/ssm/amazon-ssm-agent.json <<'SSMCONFIG'
    {
      "Profile": {
        "KeyId": ""
      }
    }
    SSMCONFIG
    systemctl restart amazon-ssm-agent
    echo "Bastion host setup completed successfully"
    # Install monitoring tools
    curl -LO https://github.com/prometheus/prometheus/releases/download/v2.45.0/prometheus-2.45.0.linux-amd64.tar.gz
    tar xvf prometheus-*.tar.gz
    sudo mv prometheus-*/prometheus /usr/local/bin/

    # Install Grafana CLI
    sudo yum install -y https://dl.grafana.com/enterprise/release/grafana-enterprise-10.0.3-1.x86_64.rpm

    # Configure SSM session preferences for monitoring
    cat > /etc/amazon/ssm/session-manager-plugin.json <<'SSMCONFIG'
    {
      "monitoring": {
        "enabled": true,
        "metrics_collection_interval": 60
      }
    }
    SSMCONFIG

    # Setup CloudWatch monitoring and logging
    # Install CloudWatch agent for EC2 metrics
    yum install -y amazon-cloudwatch-agent

    # Configure CloudWatch agent with basic metrics and log collection
    cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json <<'CWCONFIG'
    {
      "agent": {
        "metrics_collection_interval": 60,
        "run_as_user": "cwagent"
      },
      "metrics": {
        "metrics_collected": {
          "disk": {
            "measurement": ["used_percent"],
            "resources": ["/"],
            "drop_device": true
          },
          "mem": {
            "measurement": ["mem_used_percent"]
          },
          "swap": {
            "measurement": ["swap_used_percent"]
          }
        }
      },
      "logs": {
        "logs_collected": {
          "files": {
            "collect_list": [
              {
                "file_path": "/var/log/messages",
                "log_group_name": "/aws/ec2/bastion/${var.eks_cluster_name}/messages",
                "log_stream_name": "{instance_id}"
              },
              {
                "file_path": "/var/log/secure",
                "log_group_name": "/aws/ec2/bastion/${var.eks_cluster_name}/secure",
                "log_stream_name": "{instance_id}"
              },
              {
                "file_path": "/var/log/user-data.log",
                "log_group_name": "/aws/ec2/bastion/${var.eks_cluster_name}/user-data",
                "log_stream_name": "{instance_id}"
              }
            ]
          }
        }
      }
    }
    CWCONFIG

    # Start CloudWatch agent
    systemctl enable amazon-cloudwatch-agent
    systemctl start amazon-cloudwatch-agent

    # Install CloudWatch Container Insights for EKS (if enabled and not skipping EKS connectivity check)
    if ${var.enable_container_insights} && [ "${var.skip_eks_connectivity_check}" != "true" ]; then
      echo "Setting up CloudWatch Container Insights for EKS cluster ${var.eks_cluster_name}..."
      curl -LO https://raw.githubusercontent.com/aws-samples/amazon-cloudwatch-container-insights/latest/k8s-deployment-manifest-templates/deployment-mode/daemonset/container-insights-monitoring/quickstart/cwagent-fluentd-quickstart.yaml
      sed -i "s/{{cluster_name}}/${var.eks_cluster_name}/g;s/{{region_name}}/${var.region}/g" cwagent-fluentd-quickstart.yaml
      aws eks update-kubeconfig --name ${var.eks_cluster_name} --region ${var.region}
      kubectl apply -f cwagent-fluentd-quickstart.yaml
      echo "Container Insights deployment complete"
    elif ${var.enable_container_insights}; then
      echo "Skipping Container Insights deployment as EKS connectivity check is disabled"
    fi
  EOF

  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
  }

  monitoring = true

  root_block_device {
    encrypted   = true
    volume_size = 20
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-eks-bastion"
  })

  lifecycle {
    create_before_destroy = true
  }
}

# Outputs moved to outputs.tf

resource "aws_security_group" "bastion_sg" {
  count       = var.enable_bastion ? 1 : 0
  name        = "${var.environment}-eks-bastion-sg"
  description = "Security group for EKS bastion host"
  vpc_id      = var.vpc_id

  dynamic "ingress" {
    for_each = var.enable_ssh_access && length(var.allowed_ssh_cidr_blocks) > 0 ? [1] : []
    content {
      from_port   = 22
      to_port     = 22
      protocol    = "tcp"
      cidr_blocks = [var.vpc_cidr] # Restrict SSH access to VPC CIDR only
      description = "SSH access from within VPC only"
    }
  }

  ingress {
    from_port   = 3000
    to_port     = 3000
    protocol    = "tcp"
    cidr_blocks = var.monitoring_bastion_cidr != "" ? [var.monitoring_bastion_cidr] : [var.vpc_cidr]
    description = "Grafana access from specific CIDR"
  }

  ingress {
    from_port   = 9090
    to_port     = 9090
    protocol    = "tcp"
    cidr_blocks = var.monitoring_bastion_cidr != "" ? [var.monitoring_bastion_cidr] : [var.vpc_cidr]
    description = "Prometheus access from specific CIDR"
  }

  # Restrict outbound traffic to VPC and specific services
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [var.vpc_cidr]
    description = "Allow outbound traffic to VPC"
  }

  # Allow HTTPS outbound to AWS services
  egress {
    from_port       = 443
    to_port         = 443
    protocol        = "tcp"
    prefix_list_ids = []
    description     = "Allow HTTPS outbound to S3"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-eks-bastion-sg"
  })
}

locals {
  bastion_sg_id = var.enable_bastion ? aws_security_group.bastion_sg[0].id : "dummy-sg-id"
}

resource "aws_security_group" "vpc_endpoints_sg" {
  count       = var.enable_vpc_endpoints ? 1 : 0
  name        = "${var.environment}-vpc-endpoints-sg"
  description = "Security group for VPC endpoints"
  vpc_id      = var.vpc_id

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [var.vpc_cidr]
    description = "HTTPS from VPC"
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = [var.vpc_cidr]
    description = "All traffic to VPC"
  }

  tags = merge(var.tags, {
    Name = "${var.environment}-vpc-endpoints-sg"
  })

  # Add lifecycle to handle existing security groups
  lifecycle {
    # Create before destroy to ensure smooth replacement
    create_before_destroy = true
    # Ignore changes to name to avoid conflicts with existing security groups
    ignore_changes = [name]
  }
}

resource "aws_vpc_endpoint" "s3" {
  count             = var.enable_bastion && var.enable_vpc_endpoints ? 1 : 0
  vpc_id            = var.vpc_id
  service_name      = "com.amazonaws.${var.region}.s3"
  vpc_endpoint_type = "Gateway"
  route_table_ids   = var.route_table_ids

  tags = merge(var.tags, {
    Name = "${var.environment}-s3-endpoint"
  })
}

resource "aws_vpc_endpoint" "ecr_api" {
  count               = var.enable_bastion && var.enable_vpc_endpoints ? 1 : 0
  vpc_id              = var.vpc_id
  service_name        = "com.amazonaws.${var.region}.ecr.api"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [var.subnet_id]
  security_group_ids  = [aws_security_group.vpc_endpoints_sg[0].id]
  private_dns_enabled = true

  tags = merge(var.tags, {
    Name = "${var.environment}-ecr-api-endpoint"
  })
}

resource "aws_vpc_endpoint" "ecr_dkr" {
  count               = var.enable_bastion && var.enable_vpc_endpoints ? 1 : 0
  vpc_id              = var.vpc_id
  service_name        = "com.amazonaws.${var.region}.ecr.dkr"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [var.subnet_id]
  security_group_ids  = [aws_security_group.vpc_endpoints_sg[0].id]
  private_dns_enabled = true

  tags = merge(var.tags, {
    Name = "${var.environment}-ecr-dkr-endpoint"
  })
}

resource "aws_vpc_endpoint" "eks" {
  count               = var.enable_bastion && var.enable_vpc_endpoints ? 1 : 0
  vpc_id              = var.vpc_id
  service_name        = "com.amazonaws.${var.region}.eks"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [var.subnet_id]
  security_group_ids  = [aws_security_group.vpc_endpoints_sg[0].id]
  private_dns_enabled = true

  tags = merge(var.tags, {
    Name = "${var.environment}-eks-endpoint"
  })
}

resource "aws_vpc_endpoint" "ssm" {
  count               = var.enable_bastion && var.create_ssm_endpoint && !var.existing_ssm_endpoint && var.enable_vpc_endpoints ? 1 : 0
  vpc_id              = var.vpc_id
  service_name        = "com.amazonaws.${var.region}.ssm"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = [var.subnet_id]
  security_group_ids  = [aws_security_group.vpc_endpoints_sg[0].id]
  private_dns_enabled = false

  tags = merge(var.tags, {
    Name = "${var.environment}-ssm-endpoint"
  })
}

resource "aws_iam_role" "bastion_role" {
  count = var.import_existing_resources ? 0 : 1
  name  = "${var.environment}-eks-bastion-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })

  tags = var.tags
}

resource "aws_iam_role_policy_attachment" "eks_read_only" {
  count      = var.import_existing_resources ? 0 : 1
  role       = aws_iam_role.bastion_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"
}

resource "aws_iam_role_policy_attachment" "eks_console_access" {
  count      = var.import_existing_resources ? 0 : 1
  role       = aws_iam_role.bastion_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy"
}

resource "aws_iam_policy" "eks_bastion_policy" {
  count       = var.import_existing_resources ? 0 : 1
  name        = "${var.environment}-eks-bastion-policy"
  description = "Policy for EKS bastion host to connect to EKS cluster"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "eks:DescribeCluster",
          "eks:ListClusters",
          "eks:DescribeNodegroup",
          "eks:ListNodegroups",
          "eks:ListFargateProfiles",
          "eks:CreateAccessEntry",
          "eks:ListAccessEntries",
          "eks:DescribeAccessEntry",
          "eks:ListAssociatedAccessPolicies",
          "eks:AssociateAccessPolicy",
          "eks:AccessKubernetesApi",
          "eks:UpdateClusterConfig",
          "eks:DescribeUpdate"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ec2:DescribeInstances",
          "ec2:DescribeSecurityGroups",
          "ec2:DescribeSubnets",
          "ec2:DescribeVpcs",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DescribeAvailabilityZones",
          "ec2:DescribeRouteTables"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "iam:GetRole",
          "iam:ListAttachedRolePolicies",
          "iam:PassRole",
          "iam:ListRoles"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "logs:DescribeLogGroups",
          "logs:DescribeLogStreams",
          "logs:GetLogEvents",
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret",
          "secretsmanager:ListSecrets"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt"
        ]
        Resource = "*"
      },
      {
        Effect = "Allow"
        Action = [
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:DescribeParameters"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "eks_bastion_policy_attachment" {
  count      = var.import_existing_resources ? 0 : 1
  role       = aws_iam_role.bastion_role[0].name
  policy_arn = aws_iam_policy.eks_bastion_policy[0].arn
}

resource "aws_iam_role_policy_attachment" "ssm_managed_instance" {
  count      = var.import_existing_resources ? 0 : 1
  role       = aws_iam_role.bastion_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
}

resource "aws_iam_role_policy_attachment" "cloudwatch_agent" {
  count      = var.import_existing_resources ? 0 : 1
  role       = aws_iam_role.bastion_role[0].name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy"
}

resource "aws_iam_instance_profile" "bastion_profile" {
  count = var.import_existing_resources ? 0 : 1
  name  = "${var.environment}-eks-bastion-profile"
  role  = aws_iam_role.bastion_role[0].name
}

data "aws_ami" "amazon_linux" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-hvm-*-x86_64-gp2"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

data "aws_caller_identity" "current" {}

resource "aws_secretsmanager_secret" "bastion_ssh_key" {
  count       = var.enable_bastion ? 1 : 0
  name        = "${var.environment}-bastion-ssh-key-${uuid()}"
  description = "SSH private key for bastion host"
  # DISABLED: kms_key_id = aws_kms_key.session_manager_key.arn

  tags = merge(var.tags, {
    Name = "${var.environment}-bastion-ssh-key"
  })
}

resource "tls_private_key" "bastion_key" {
  count     = var.enable_bastion && !var.import_existing_resources ? 1 : 0
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "aws_secretsmanager_secret_version" "bastion_ssh_key" {
  count         = var.enable_bastion && !var.import_existing_resources ? 1 : 0
  secret_id     = aws_secretsmanager_secret.bastion_ssh_key[0].id
  secret_string = tls_private_key.bastion_key[0].private_key_pem
}

# Create key pair for bastion host
resource "aws_key_pair" "bastion_key" {
  count      = var.enable_bastion && !var.import_existing_resources ? 1 : 0
  key_name   = "${var.environment}-bastion-key"
  public_key = tls_private_key.bastion_key[0].public_key_openssh

  tags = merge(var.tags, {
    Name = "${var.environment}-bastion-key"
  })

  # Add lifecycle to handle existing key pairs
  lifecycle {
    # Permanently disable prevent_destroy to allow easy creation/deletion
    prevent_destroy = false
    # Ignore changes to public_key to avoid conflicts with existing keys
    ignore_changes = [public_key]
    # Create before destroy to ensure smooth replacement
    create_before_destroy = true
  }
}

# KMS keys have been disabled to avoid deletion protection
# resource "aws_kms_key" "session_manager_key" {
#   description             = "KMS key for Session Manager encryption"
#   deletion_window_in_days = 10
#   enable_key_rotation     = true
#
#   tags = merge(var.tags, {
#     Name = "${var.environment}-session-manager-key"
#   })
# }

# KMS aliases have been disabled to avoid deletion protection
# resource "aws_kms_alias" "session_manager_key_alias" {
#   count         = var.import_existing_resources ? 0 : 1
#   name          = "alias/${var.environment}-session-manager-key"
#   target_key_id = aws_kms_key.session_manager_key.key_id
# }

resource "tls_private_key" "bastion" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "aws_ssm_document" "session_manager_prefs" {
  count           = var.import_existing_resources ? 0 : 1
  name            = "${var.environment}-session-manager-prefs"
  document_type   = "Session"
  document_format = "JSON"

  content = jsonencode({
    schemaVersion = "1.0"
    description   = "Session Manager preferences for bastion host"
    sessionType   = "Standard_Stream"
    inputs = {
      # KMS keys have been disabled
      # kmsKeyId         = aws_kms_key.session_manager_key.arn
      runAsEnabled     = true
      runAsDefaultUser = "ec2-user"
    }
  })
}
