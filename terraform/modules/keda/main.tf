variable "environment" {
  description = "Environment name"
  type        = string
}

variable "namespace" {
  description = "Kubernetes namespace"
  type        = string
  default     = "keda"
}

variable "create_namespace" {
  description = "Create Kubernetes namespace"
  type        = bool
  default     = true
}

variable "keda_version" {
  description = "KEDA version"
  type        = string
  default     = "2.10.2"
}

variable "skip_k8s_connection" {
  description = "Skip Kubernetes connection"
  type        = bool
  default     = false
}

variable "tags" {
  description = "Tags to apply to resources"
  type        = map(string)
  default     = {}
}

variable "cpu_scaled_objects" {
  description = "Map of deployments to configure CPU-based KEDA scaling for"
  type = map(object({
    namespace        = string
    min_replicas     = number
    max_replicas     = number
    polling_interval = number
    cooldown_period  = number
    cpu_threshold    = number
  }))
  default = {}
}

variable "memory_scaled_objects" {
  description = "Map of deployments to configure memory-based KEDA scaling for"
  type = map(object({
    namespace        = string
    min_replicas     = number
    max_replicas     = number
    polling_interval = number
    cooldown_period  = number
    memory_threshold = number
  }))
  default = {}
}

variable "prometheus_scaled_objects" {
  description = "Map of deployments to configure Prometheus-based KEDA scaling for"
  type = map(object({
    namespace         = string
    min_replicas      = number
    max_replicas      = number
    polling_interval  = number
    cooldown_period   = number
    prometheus_server = string
    metric_name       = string
    query             = string
    threshold         = number
  }))
  default = {}
}

# Deploy KEDA using Helm
resource "helm_release" "keda" {
  count            = var.skip_k8s_connection ? 0 : 1
  name             = "keda"
  repository       = "https://kedacore.github.io/charts"
  chart            = "keda"
  namespace        = var.namespace
  version          = var.keda_version
  create_namespace = var.create_namespace

  set {
    name  = "operator.resources.limits.cpu"
    value = "100m"
  }

  set {
    name  = "operator.resources.limits.memory"
    value = "128Mi"
  }

  set {
    name  = "operator.resources.requests.cpu"
    value = "50m"
  }

  set {
    name  = "operator.resources.requests.memory"
    value = "64Mi"
  }

  set {
    name  = "metricsServer.resources.limits.cpu"
    value = "100m"
  }

  set {
    name  = "metricsServer.resources.limits.memory"
    value = "128Mi"
  }

  set {
    name  = "metricsServer.resources.requests.cpu"
    value = "50m"
  }

  set {
    name  = "metricsServer.resources.requests.memory"
    value = "64Mi"
  }
}

# Create ScaledObjects for CPU-based scaling
resource "kubernetes_manifest" "cpu_scaled_object" {
  for_each = var.skip_k8s_connection ? {} : var.cpu_scaled_objects

  manifest = {
    apiVersion = "keda.sh/v1alpha1"
    kind       = "ScaledObject"
    metadata = {
      name      = "${each.key}-cpu-scaler"
      namespace = each.value.namespace
    }
    spec = {
      scaleTargetRef = {
        name = each.key
      }
      minReplicaCount = each.value.min_replicas
      maxReplicaCount = each.value.max_replicas
      pollingInterval = each.value.polling_interval
      cooldownPeriod  = each.value.cooldown_period
      triggers = [
        {
          type = "cpu"
          metadata = {
            type  = "Utilization"
            value = tostring(each.value.cpu_threshold)
          }
        }
      ]
    }
  }

  depends_on = [helm_release.keda]
}

# Create ScaledObjects for memory-based scaling
resource "kubernetes_manifest" "memory_scaled_object" {
  for_each = var.skip_k8s_connection ? {} : var.memory_scaled_objects

  manifest = {
    apiVersion = "keda.sh/v1alpha1"
    kind       = "ScaledObject"
    metadata = {
      name      = "${each.key}-memory-scaler"
      namespace = each.value.namespace
    }
    spec = {
      scaleTargetRef = {
        name = each.key
      }
      minReplicaCount = each.value.min_replicas
      maxReplicaCount = each.value.max_replicas
      pollingInterval = each.value.polling_interval
      cooldownPeriod  = each.value.cooldown_period
      triggers = [
        {
          type = "memory"
          metadata = {
            type  = "Utilization"
            value = tostring(each.value.memory_threshold)
          }
        }
      ]
    }
  }

  depends_on = [helm_release.keda]
}

# Create ScaledObjects for Prometheus-based scaling
resource "kubernetes_manifest" "prometheus_scaled_object" {
  for_each = var.skip_k8s_connection ? {} : var.prometheus_scaled_objects

  manifest = {
    apiVersion = "keda.sh/v1alpha1"
    kind       = "ScaledObject"
    metadata = {
      name      = "${each.key}-prometheus-scaler"
      namespace = each.value.namespace
    }
    spec = {
      scaleTargetRef = {
        name = each.key
      }
      minReplicaCount = each.value.min_replicas
      maxReplicaCount = each.value.max_replicas
      pollingInterval = each.value.polling_interval
      cooldownPeriod  = each.value.cooldown_period
      triggers = [
        {
          type = "prometheus"
          metadata = {
            serverAddress = each.value.prometheus_server
            metricName    = each.value.metric_name
            query         = each.value.query
            threshold     = tostring(each.value.threshold)
          }
        }
      ]
    }
  }

  depends_on = [helm_release.keda]
}

# Output for KEDA status
output "keda_enabled" {
  value = true
}
