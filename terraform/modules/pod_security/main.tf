provider "kubernetes" {
  config_path = var.skip_k8s_connection ? "" : "~/.kube/config"
}

locals {
  namespace = var.namespace
}

# Create namespace for pod security policies
resource "kubernetes_namespace" "pod_security" {
  count = var.create_namespace && !var.skip_k8s_connection ? 1 : 0

  metadata {
    name = local.namespace

    labels = {
      "app.kubernetes.io/managed-by"       = "terraform"
      "pod-security.kubernetes.io/enforce" = "restricted"
    }
  }
}

# Create pod security admission configuration
resource "kubernetes_manifest" "pod_security_admission" {
  count = var.enable_pod_security && !var.skip_k8s_connection ? 1 : 0

  manifest = {
    apiVersion = "apiserver.config.k8s.io/v1"
    kind       = "AdmissionConfiguration"
    plugins = [
      {
        name = "PodSecurity"
        configuration = {
          apiVersion = "pod-security.admission.config.k8s.io/v1"
          kind       = "PodSecurityConfiguration"
          defaults = {
            enforce         = "baseline"
            enforce-version = "latest"
            audit           = "restricted"
            audit-version   = "latest"
            warn            = "restricted"
            warn-version    = "latest"
          }
          exemptions = {
            usernames      = var.exempted_usernames
            runtimeClasses = var.exempted_runtime_classes
            namespaces     = var.exempted_namespaces
          }
        }
      }
    ]
  }
}

# Apply pod security standards to tenant namespaces
resource "kubernetes_labels" "tenant_namespace_pod_security" {
  for_each = var.enable_pod_security && !var.skip_k8s_connection ? {
    for ns in var.tenant_namespaces : ns => var.tenant_security_levels[ns] != null ? var.tenant_security_levels[ns] : var.default_security_level
  } : {}

  api_version = "v1"
  kind        = "Namespace"
  metadata {
    name = each.key
  }

  labels = {
    "pod-security.kubernetes.io/enforce" = each.value
    "pod-security.kubernetes.io/audit"   = var.audit_level
    "pod-security.kubernetes.io/warn"    = var.warn_level
  }
}

# Create security contexts for deployments
resource "kubernetes_manifest" "security_context_mutating_webhook" {
  count = var.enable_security_context_webhook && !var.skip_k8s_connection ? 1 : 0

  manifest = {
    apiVersion = "admissionregistration.k8s.io/v1"
    kind       = "MutatingWebhookConfiguration"
    metadata = {
      name = "security-context-webhook"
    }
    webhooks = [
      {
        name = "security-context.architrave.io"
        clientConfig = {
          service = {
            namespace = local.namespace
            name      = "security-context-webhook"
            path      = "/mutate"
          }
          caBundle = var.ca_bundle
        }
        rules = [
          {
            apiGroups   = ["apps"]
            apiVersions = ["v1"]
            operations  = ["CREATE", "UPDATE"]
            resources   = ["deployments", "statefulsets", "daemonsets"]
            scope       = "Namespaced"
          }
        ]
        admissionReviewVersions = ["v1"]
        sideEffects             = "None"
        timeoutSeconds          = 5
        failurePolicy           = "Ignore"
        namespaceSelector = {
          matchExpressions = [
            {
              key      = "security-context-webhook"
              operator = "In"
              values   = ["enabled"]
            }
          ]
        }
      }
    ]
  }
}

# Deploy security context webhook
resource "kubernetes_deployment" "security_context_webhook" {
  count = var.enable_security_context_webhook && !var.skip_k8s_connection ? 1 : 0

  metadata {
    name      = "security-context-webhook"
    namespace = local.namespace

    labels = {
      app = "security-context-webhook"
    }
  }

  spec {
    replicas = 2

    selector {
      match_labels = {
        app = "security-context-webhook"
      }
    }

    template {
      metadata {
        labels = {
          app = "security-context-webhook"
        }
      }

      spec {
        container {
          name  = "webhook"
          image = var.webhook_image

          port {
            container_port = 8443
          }

          volume_mount {
            name       = "webhook-certs"
            mount_path = "/etc/webhook/certs"
            read_only  = true
          }

          resources {
            limits = {
              cpu    = "100m"
              memory = "128Mi"
            }
            requests = {
              cpu    = "50m"
              memory = "64Mi"
            }
          }

          security_context {
            read_only_root_filesystem = true
            run_as_non_root           = true
            run_as_user               = 1000
            capabilities {
              drop = ["ALL"]
            }
          }
        }

        volume {
          name = "webhook-certs"
          secret {
            secret_name = "webhook-certs"
          }
        }
      }
    }
  }
}

# Create service for security context webhook
resource "kubernetes_service" "security_context_webhook" {
  count = var.enable_security_context_webhook && !var.skip_k8s_connection ? 1 : 0

  metadata {
    name      = "security-context-webhook"
    namespace = local.namespace
  }

  spec {
    selector = {
      app = "security-context-webhook"
    }

    port {
      port        = 443
      target_port = 8443
    }
  }
}

# Enable security context webhook for tenant namespaces
resource "kubernetes_labels" "tenant_namespace_webhook" {
  for_each = var.enable_security_context_webhook && !var.skip_k8s_connection ? toset(var.tenant_namespaces) : []

  api_version = "v1"
  kind        = "Namespace"
  metadata {
    name = each.key
  }

  labels = {
    "security-context-webhook" = "enabled"
  }
}
