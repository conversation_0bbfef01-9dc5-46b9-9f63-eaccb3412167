/**
 * # Network Policies Module
 *
 * This module implements comprehensive network policies for tenant isolation.
 * It creates the necessary Kubernetes network policies to isolate tenants from each other.
 */

terraform {
  required_providers {
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
  }
}

# Local variables
locals {
  name_prefix = "${var.environment}-${var.name}"

  # Common labels
  common_labels = {
    "app.kubernetes.io/managed-by" = "terraform"
    "app.kubernetes.io/part-of"    = "network-policies"
  }

  # Default allowed namespaces
  default_allowed_namespaces = [
    "kube-system",
    "istio-system",
    "monitoring",
    "cert-manager",
    "tenant-system"
  ]

  # Default allowed egress CIDRs
  default_allowed_egress_cidrs = [
    var.vpc_cidr
  ]
}

# Create default deny all network policy for each tenant
resource "kubernetes_network_policy" "tenant_default_deny" {
  for_each = var.tenants

  metadata {
    name      = "tenant-default-deny"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {}

    policy_types = ["Ingress", "Egress"]
  }
}

# Create network policy to allow traffic within the same namespace
resource "kubernetes_network_policy" "tenant_allow_same_namespace" {
  for_each = var.tenants

  metadata {
    name      = "tenant-allow-same-namespace"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {}

    policy_types = ["Ingress", "Egress"]

    ingress {
      from {
        pod_selector {}
      }
    }

    egress {
      to {
        pod_selector {}
      }
    }
  }
}

# Create network policy to allow traffic from Istio ingress gateway
resource "kubernetes_network_policy" "tenant_allow_istio_ingress" {
  for_each = var.tenants

  metadata {
    name      = "tenant-allow-istio-ingress"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {}

    policy_types = ["Ingress"]

    ingress {
      from {
        namespace_selector {
          match_labels = {
            name = "istio-system"
          }
        }

        pod_selector {
          match_labels = {
            app = "istio-ingressgateway"
          }
        }
      }
    }
  }
}

# Create network policy to allow DNS traffic
resource "kubernetes_network_policy" "tenant_allow_dns" {
  for_each = var.tenants

  metadata {
    name      = "tenant-allow-dns"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {}

    policy_types = ["Egress"]

    egress {
      to {
        namespace_selector {
          match_labels = {
            name = "kube-system"
          }
        }

        pod_selector {
          match_labels = {
            k8s-app = "kube-dns"
          }
        }
      }

      ports {
        port     = 53
        protocol = "UDP"
      }

      ports {
        port     = 53
        protocol = "TCP"
      }
    }
  }
}

# Create network policy to allow traffic to AWS services
resource "kubernetes_network_policy" "tenant_allow_aws_services" {
  for_each = var.tenants

  metadata {
    name      = "tenant-allow-aws-services"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {}

    policy_types = ["Egress"]

    egress {
      to {
        ip_block {
          cidr = "0.0.0.0/0"
          except = concat(
            ["10.0.0.0/8", "**********/12", "***********/16"],
            var.additional_private_cidrs
          )
        }
      }

      ports {
        port     = 443
        protocol = "TCP"
      }
    }
  }
}

# Create network policy to allow traffic to RDS
resource "kubernetes_network_policy" "tenant_allow_rds" {
  for_each = var.tenants

  metadata {
    name      = "tenant-allow-rds"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {
      match_labels = {
        app = "tenant-api"
      }
    }

    policy_types = ["Egress"]

    egress {
      to {
        ip_block {
          cidr = var.vpc_cidr
        }
      }

      ports {
        port     = 3306
        protocol = "TCP"
      }
    }
  }
}

# Create network policy to allow traffic from monitoring
resource "kubernetes_network_policy" "tenant_allow_monitoring" {
  for_each = var.tenants

  metadata {
    name      = "tenant-allow-monitoring"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {}

    policy_types = ["Ingress"]

    ingress {
      from {
        namespace_selector {
          match_labels = {
            name = "monitoring"
          }
        }
      }
    }
  }
}

# Create network policy to allow traffic to Prometheus
resource "kubernetes_network_policy" "tenant_allow_prometheus" {
  for_each = var.tenants

  metadata {
    name      = "tenant-allow-prometheus"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {}

    policy_types = ["Egress"]

    egress {
      to {
        namespace_selector {
          match_labels = {
            name = "monitoring"
          }
        }

        pod_selector {
          match_labels = {
            app = "prometheus"
          }
        }
      }

      ports {
        port     = 9090
        protocol = "TCP"
      }
    }
  }
}

# Create network policy to allow traffic to Loki
resource "kubernetes_network_policy" "tenant_allow_loki" {
  for_each = var.enable_loki ? var.tenants : {}

  metadata {
    name      = "tenant-allow-loki"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {}

    policy_types = ["Egress"]

    egress {
      to {
        namespace_selector {
          match_labels = {
            name = "monitoring"
          }
        }

        pod_selector {
          match_labels = {
            app = "loki"
          }
        }
      }

      ports {
        port     = 3100
        protocol = "TCP"
      }
    }
  }
}

# Create network policy to allow traffic to Jaeger
resource "kubernetes_network_policy" "tenant_allow_jaeger" {
  for_each = var.enable_jaeger ? var.tenants : {}

  metadata {
    name      = "tenant-allow-jaeger"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {}

    policy_types = ["Egress"]

    egress {
      to {
        namespace_selector {
          match_labels = {
            name = "istio-system"
          }
        }

        pod_selector {
          match_labels = {
            app = "jaeger"
          }
        }
      }

      ports {
        port     = 9411
        protocol = "TCP"
      }

      ports {
        port     = 16686
        protocol = "TCP"
      }
    }
  }
}

# Create network policy to allow traffic to S3 mount
resource "kubernetes_network_policy" "tenant_allow_s3_mount" {
  for_each = var.enable_s3_mount ? var.tenants : {}

  metadata {
    name      = "tenant-allow-s3-mount"
    namespace = "tenant-${each.key}"

    labels = merge(local.common_labels, {
      "tenant.architrave.io/tenant-id" = each.key
    })
  }

  spec {
    pod_selector {}

    policy_types = ["Egress"]

    egress {
      to {
        namespace_selector {
          match_labels = {
            name = var.s3_mount_namespace
          }
        }

        pod_selector {
          match_labels = {
            "app.kubernetes.io/name" = "s3-mount-${each.key}"
          }
        }
      }
    }
  }
}
