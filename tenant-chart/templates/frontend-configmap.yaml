apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.tenantName }}-frontend-nginx-config
  namespace: {{ .Values.namespace }}
data:
  default.conf: |
    upstream webapp {
        server {{ .Values.tenantName }}-backend-service.{{ .Values.namespace }}.svc.cluster.local:8080;
    }
    server {
        listen 80;
        server_name localhost;
        location / {
            proxy_pass http://webapp;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    } 