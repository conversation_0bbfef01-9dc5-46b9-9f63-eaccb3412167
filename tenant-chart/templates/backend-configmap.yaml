apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.tenantName }}-backend-nginx-config
  namespace: {{ .Values.namespace }}
data:
  default.conf: |
    server {
        listen 8080;
        server_name localhost;
        root /var/www/html;
        index index.php index.html;

        location / {
            try_files $uri $uri/ /index.php?$query_string;
        }

        location ~ \.php$ {
            include fastcgi_params;
            fastcgi_pass 127.0.0.1:9000;
            fastcgi_index index.php;
            fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        }

        location /api/health {
            return 200 'OK';
            add_header Content-Type text/plain;
        }
    } 