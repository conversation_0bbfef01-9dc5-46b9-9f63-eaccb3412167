apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.tenantName }}-backend
  namespace: {{ .Values.namespace }}
  labels:
    app: {{ .Values.tenantName }}-backend
    tenant: {{ .Values.tenantName }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.tenantName }}-backend
      tenant: {{ .Values.tenantName }}
  template:
    metadata:
      labels:
        app: {{ .Values.tenantName }}-backend
        tenant: {{ .Values.tenantName }}
    spec:
      containers:
        - name: nginx
          image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl
          ports:
            - containerPort: 8080
          volumeMounts:
            - name: nginx-backend-config
              mountPath: /etc/nginx/conf.d/default.conf
              subPath: default.conf
        - name: php-fpm
          image: {{ .Values.backendImage }}
          ports:
            - containerPort: 9000
      volumes:
        - name: nginx-backend-config
          configMap:
            name: {{ .Values.tenantName }}-backend-nginx-config 