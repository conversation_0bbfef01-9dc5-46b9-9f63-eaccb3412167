apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.tenantName }}-frontend
  namespace: {{ .Values.namespace }}
  labels:
    app: {{ .Values.tenantName }}-frontend
    tenant: {{ .Values.tenantName }}
spec:
  replicas: {{ .Values.frontendReplicas }}
  selector:
    matchLabels:
      app: {{ .Values.tenantName }}-frontend
      tenant: {{ .Values.tenantName }}
  template:
    metadata:
      labels:
        app: {{ .Values.tenantName }}-frontend
        tenant: {{ .Values.tenantName }}
    spec:
      containers:
        - name: frontend
          image: {{ .Values.frontendImage }}
          ports:
            - containerPort: {{ .Values.frontendPort }}
          volumeMounts:
            - name: nginx-config
              mountPath: /etc/nginx/conf.d/default.conf
              subPath: default.conf
      volumes:
        - name: nginx-config
          configMap:
            name: {{ .Values.tenantName }}-frontend-nginx-config 