apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.tenantName }}-backend-service
  namespace: {{ .Values.namespace }}
  labels:
    app: {{ .Values.tenantName }}-backend
    tenant: {{ .Values.tenantName }}
spec:
  selector:
    app: {{ .Values.tenantName }}-backend
    tenant: {{ .Values.tenantName }}
  ports:
    - name: http
      protocol: TCP
      port: {{ .Values.backendPort }}
      targetPort: {{ .Values.backendPort }} 