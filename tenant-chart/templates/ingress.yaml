{{- if .Values.ingress.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .Values.tenantName }}-ingress
  namespace: {{ .Values.namespace }}
  labels:
    app: {{ .Values.tenantName }}-ingress
    tenant: {{ .Values.tenantName }}
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/certificate-arn: {{ .Values.acmCertificateArn }}
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
    alb.ingress.kubernetes.io/target-type: ip
spec:
  rules:
    - host: {{ .Values.subdomain }}.architrave-assets.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{ .Values.tenantName }}-backend-service
                port:
                  number: 8080
  tls:
    - hosts:
        - {{ .Values.subdomain }}.architrave-assets.com
      secretName: {{ .Values.tenantName }}-tls 
{{- end }} 