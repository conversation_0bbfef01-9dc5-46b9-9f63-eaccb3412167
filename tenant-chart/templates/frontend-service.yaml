apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.tenantName }}-frontend-service
  namespace: {{ .Values.namespace }}
  labels:
    app: {{ .Values.tenantName }}-frontend
    tenant: {{ .Values.tenantName }}
spec:
  selector:
    app: {{ .Values.tenantName }}-frontend
    tenant: {{ .Values.tenantName }}
  ports:
    - name: http
      protocol: TCP
      port: {{ .Values.frontendPort }}
      targetPort: {{ .Values.frontendPort }} 