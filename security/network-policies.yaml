apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-all
  namespace: default
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-monitoring
  namespace: default
spec:
  podSelector:
    matchLabels:
      app: app
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-isolation
  namespace: tenant-management
spec:
  podSelector:
    matchLabels:
      app: tenant-manager
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: tenant-management
    ports:
    - protocol: TCP
      port: 8080
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: tenant-management
    ports:
    - protocol: TCP
      port: 8080
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: security-scan
  namespace: kube-system
spec:
  schedule: "0 0 * * *"  # Run daily at midnight
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: trivy
            image: aquasec/trivy:latest
            command:
            - /bin/sh
            - -c
            - |
              # Scan all images in the cluster
              kubectl get pods --all-namespaces -o json | jq '.items[].spec.containers[].image' | sort | uniq | xargs -I {} trivy image --exit-code 1 --severity HIGH,CRITICAL {}
              
              # Scan for vulnerabilities in the cluster
              trivy k8s --report summary cluster
              
              # Scan for misconfigurations
              trivy config .
          - name: kube-bench
            image: aquasec/kube-bench:latest
            command:
            - /bin/sh
            - -c
            - |
              # Run CIS benchmark
              kube-bench --benchmark cis-1.6
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: falco-rules
  namespace: kube-system
data:
  falco_rules.local.yaml: |
    - rule: Unauthorized Process
      desc: Detect unauthorized process execution
      condition: spawned_process and not proc.name in (authorized_processes)
      output: Unauthorized process started (user=%user.name command=%proc.cmdline)
      priority: WARNING
      
    - rule: Suspicious File Access
      desc: Detect suspicious file access patterns
      condition: evt.type=open and fd.name contains "/etc/passwd" and not proc.name in (authorized_processes)
      output: Suspicious file access detected (user=%user.name file=%fd.name)
      priority: WARNING
      
    - rule: Container Escape
      desc: Detect container escape attempts
      condition: evt.type=execve and evt.arg.path contains "/bin/sh" and container.id != host
      output: Container escape attempt detected (container=%container.name)
      priority: CRITICAL 