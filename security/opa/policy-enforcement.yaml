apiVersion: v1
kind: ConfigMap
metadata:
  name: opa-policies
  namespace: kube-system
data:
  main.rego: |
    package kubernetes.admission

    import data.kubernetes.namespaces

    deny[msg] {
        input.request.kind.kind == "Pod"
        container := input.request.object.spec.containers[_]
        not container.securityContext.runAsNonRoot
        msg := sprintf("Container %v must run as non-root", [container.name])
    }

    deny[msg] {
        input.request.kind.kind == "Pod"
        container := input.request.object.spec.containers[_]
        not container.securityContext.readOnlyRootFilesystem
        msg := sprintf("Container %v must have a read-only root filesystem", [container.name])
    }

    deny[msg] {
        input.request.kind.kind == "Pod"
        container := input.request.object.spec.containers[_]
        not container.securityContext.allowPrivilegeEscalation == false
        msg := sprintf("Container %v must not allow privilege escalation", [container.name])
    }

    deny[msg] {
        input.request.kind.kind == "Pod"
        container := input.request.object.spec.containers[_]
        not container.securityContext.capabilities.drop[_] == "ALL"
        msg := sprintf("Container %v must drop all capabilities", [container.name])
    }

---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: opa-validating-webhook
webhooks:
  - name: validating-webhook.openpolicyagent.org
    clientConfig:
      service:
        namespace: kube-system
        name: opa
        path: "/v1/admit"
    rules:
      - operations: ["CREATE", "UPDATE"]
        apiGroups: ["*"]
        apiVersions: ["*"]
        resources: ["*"]
    failurePolicy: Ignore
    sideEffects: None
    admissionReviewVersions: ["v1"]
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: opa
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: opa
  template:
    metadata:
      labels:
        app: opa
    spec:
      containers:
        - name: opa
          image: openpolicyagent/opa:latest
          args:
            - "run"
            - "--server"
            - "--addr=:8443"
            - "--tls-cert-file=/certs/tls.crt"
            - "--tls-private-key-file=/certs/tls.key"
            - "--config-file=/config/config.yaml"
          volumeMounts:
            - mountPath: /certs
              name: opa-certs
            - mountPath: /config
              name: opa-config
            - mountPath: /policies
              name: opa-policies
      volumes:
        - name: opa-certs
          secret:
            secretName: opa-certs
        - name: opa-config
          configMap:
            name: opa-config
        - name: opa-policies
          configMap:
            name: opa-policies 