apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: istio-system
spec:
  mtls:
    mode: STRICT
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-isolation
  namespace: tenant-management
spec:
  selector:
    matchLabels:
      app: tenant-manager
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/tenant-management/sa/tenant-manager"]
    to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE"]
        paths: ["/api/v1/tenants/*"]
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: mtls-enabled
  namespace: istio-system
spec:
  host: "*.local"
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL
---
apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: jwt-auth
  namespace: istio-system
spec:
  selector:
    matchLabels:
      app: tenant-manager
  jwtRules:
  - issuer: "https://auth.architrave.com"
    jwksUri: "https://auth.architrave.com/.well-known/jwks.json"
    fromHeaders:
    - name: Authorization
      prefix: "Bearer " 