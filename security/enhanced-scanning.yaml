apiVersion: batch/v1
kind: CronJob
metadata:
  name: enhanced-security-scan
  namespace: kube-system
spec:
  schedule: "0 */6 * * *"  # Run every 6 hours
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: trivy
            image: aquasec/trivy:latest
            command:
            - /bin/sh
            - -c
            - |
              # Enhanced image scanning
              echo "Scanning container images..."
              kubectl get pods --all-namespaces -o json | jq '.items[].spec.containers[].image' | sort | uniq | xargs -I {} trivy image --exit-code 1 --severity HIGH,CRITICAL --format json --output trivy-results.json {}
              
              # Enhanced cluster scanning
              echo "Scanning cluster for vulnerabilities..."
              trivy k8s --report summary cluster --format json --output cluster-vulns.json
              
              # Enhanced config scanning
              echo "Scanning configurations..."
              trivy config . --severity HIGH,CRITICAL --format json --output config-vulns.json
              
              # Send results to security monitoring
              curl -X POST -H "Content-Type: application/json" -d @trivy-results.json http://security-monitor:8080/api/v1/scan-results
              curl -X POST -H "Content-Type: application/json" -d @cluster-vulns.json http://security-monitor:8080/api/v1/cluster-vulns
              curl -X POST -H "Content-Type: application/json" -d @config-vulns.json http://security-monitor:8080/api/v1/config-vulns
          - name: kube-bench
            image: aquasec/kube-bench:latest
            command:
            - /bin/sh
            - -c
            - |
              # Enhanced CIS benchmark
              echo "Running CIS benchmark..."
              kube-bench --benchmark cis-1.6 --json --output kube-bench-results.json
              
              # Send results to security monitoring
              curl -X POST -H "Content-Type: application/json" -d @kube-bench-results.json http://security-monitor:8080/api/v1/bench-results
          - name: falco
            image: falcosecurity/falco:latest
            command:
            - /bin/sh
            - -c
            - |
              # Enhanced runtime security monitoring
              echo "Starting Falco monitoring..."
              falco -K /var/run/secrets/kubernetes.io/serviceaccount/token -k https://kubernetes.default.svc.cluster.local -pk
          restartPolicy: OnFailure
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: enhanced-falco-rules
  namespace: kube-system
data:
  falco_rules.local.yaml: |
    - rule: Unauthorized Process
      desc: Detect unauthorized process execution
      condition: spawned_process and not proc.name in (authorized_processes)
      output: Unauthorized process started (user=%user.name command=%proc.cmdline)
      priority: WARNING
      
    - rule: Suspicious File Access
      desc: Detect suspicious file access patterns
      condition: evt.type=open and fd.name contains "/etc/passwd" and not proc.name in (authorized_processes)
      output: Suspicious file access detected (user=%user.name file=%fd.name)
      priority: WARNING
      
    - rule: Container Escape
      desc: Detect container escape attempts
      condition: evt.type=execve and evt.arg.path contains "/bin/sh" and container.id != host
      output: Container escape attempt detected (container=%container.name)
      priority: CRITICAL
      
    - rule: Privilege Escalation
      desc: Detect privilege escalation attempts
      condition: evt.type=execve and evt.arg.path contains "sudo" and not proc.name in (authorized_processes)
      output: Privilege escalation attempt detected (user=%user.name command=%proc.cmdline)
      priority: CRITICAL
      
    - rule: Network Policy Violation
      desc: Detect network policy violations
      condition: evt.type=connect and not fd.sport in (authorized_ports)
      output: Network policy violation detected (container=%container.name port=%fd.sport)
      priority: WARNING
      
    - rule: Sensitive Data Access
      desc: Detect access to sensitive data
      condition: evt.type=open and fd.name contains "/etc/kubernetes" and not proc.name in (authorized_processes)
      output: Sensitive data access detected (user=%user.name file=%fd.name)
      priority: CRITICAL
      
    - rule: Resource Exhaustion
      desc: Detect resource exhaustion attempts
      condition: evt.type=execve and evt.arg.path contains "stress" and not proc.name in (authorized_processes)
      output: Resource exhaustion attempt detected (container=%container.name command=%proc.cmdline)
      priority: WARNING 