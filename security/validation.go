package security

import (
	"fmt"
	"regexp"
	"strings"
	"unicode"
)

// SecurityValidator provides comprehensive input validation
type SecurityValidator struct{}

// ValidationError represents a validation error
type ValidationError struct {
	Field   string
	Message string
	Value   string
}

func (e *ValidationError) Error() string {
	return fmt.Sprintf("validation error in field '%s': %s (value: %s)", e.Field, e.Message, e.Value)
}

// Injection patterns
var (
	sqlInjectionPatterns = []*regexp.Regexp{
		regexp.MustCompile(`(?i)(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)`),
		regexp.MustCompile(`(?i)(--|#|/\*|\*/)`),
		regexp.MustCompile(`(?i)(\bOR\b|\bAND\b).*=.*`),
		regexp.MustCompile(`['";]`),
	}
	
	commandInjectionPatterns = []*regexp.Regexp{
		regexp.MustCompile(`[;&|` + "`" + `$(){}[\]<>]`),
		regexp.MustCompile(`(?i)\b(rm|cat|ls|ps|kill|sudo|su|chmod|chown)\b`),
		regexp.MustCompile(`\.\./`),
	}
	
	xssPatterns = []*regexp.Regexp{
		regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`),
		regexp.MustCompile(`(?i)javascript:`),
		regexp.MustCompile(`(?i)on\w+\s*=`),
		regexp.MustCompile(`(?i)<iframe[^>]*>`),
	}
)

// ValidateTenantID performs comprehensive tenant ID validation
func (v *SecurityValidator) ValidateTenantID(tenantID string) (string, error) {
	if tenantID == "" {
		return "", &ValidationError{Field: "tenantID", Message: "cannot be empty", Value: tenantID}
	}
	
	// Length validation
	if len(tenantID) < 3 {
		return "", &ValidationError{Field: "tenantID", Message: "must be at least 3 characters", Value: tenantID}
	}
	
	if len(tenantID) > 50 {
		return "", &ValidationError{Field: "tenantID", Message: "too long (max 50 characters)", Value: tenantID}
	}
	
	// Format validation
	validPattern := regexp.MustCompile(`^[a-z0-9\-]+$`)
	if !validPattern.MatchString(tenantID) {
		return "", &ValidationError{Field: "tenantID", Message: "can only contain lowercase letters, numbers, and hyphens", Value: tenantID}
	}
	
	// Hyphen validation
	if strings.HasPrefix(tenantID, "-") || strings.HasSuffix(tenantID, "-") {
		return "", &ValidationError{Field: "tenantID", Message: "cannot start or end with hyphen", Value: tenantID}
	}
	
	if strings.Contains(tenantID, "--") {
		return "", &ValidationError{Field: "tenantID", Message: "cannot contain consecutive hyphens", Value: tenantID}
	}
	
	// Reserved names check
	reservedNames := []string{"admin", "root", "system", "default", "test", "prod", "production", "staging", "dev", "development"}
	lowerTenantID := strings.ToLower(tenantID)
	for _, reserved := range reservedNames {
		if lowerTenantID == reserved {
			return "", &ValidationError{Field: "tenantID", Message: fmt.Sprintf("'%s' is reserved", tenantID), Value: tenantID}
		}
	}
	
	// Injection pattern check
	if err := v.checkInjectionPatterns(tenantID, "tenantID"); err != nil {
		return "", err
	}
	
	return strings.ToLower(tenantID), nil
}

// ValidateDatabaseName performs comprehensive database name validation
func (v *SecurityValidator) ValidateDatabaseName(dbName string) (string, error) {
	if dbName == "" {
		return "", &ValidationError{Field: "dbName", Message: "cannot be empty", Value: dbName}
	}
	
	// Length validation
	if len(dbName) > 64 {
		return "", &ValidationError{Field: "dbName", Message: "too long (max 64 characters)", Value: dbName}
	}
	
	// Must start with letter
	if !unicode.IsLetter(rune(dbName[0])) {
		return "", &ValidationError{Field: "dbName", Message: "must start with a letter", Value: dbName}
	}
	
	// Format validation
	validPattern := regexp.MustCompile(`^[a-zA-Z0-9_]+$`)
	if !validPattern.MatchString(dbName) {
		return "", &ValidationError{Field: "dbName", Message: "can only contain letters, numbers, and underscores", Value: dbName}
	}
	
	// Injection pattern check
	if err := v.checkInjectionPatterns(dbName, "dbName"); err != nil {
		return "", err
	}
	
	return dbName, nil
}

// SanitizeSQLInput sanitizes input for SQL queries
func (v *SecurityValidator) SanitizeSQLInput(input string) (string, error) {
	if input == "" {
		return "", &ValidationError{Field: "sqlInput", Message: "cannot be empty", Value: input}
	}
	
	// Check for SQL injection patterns
	for _, pattern := range sqlInjectionPatterns {
		if pattern.MatchString(input) {
			return "", &ValidationError{Field: "sqlInput", Message: "contains potentially dangerous SQL pattern", Value: input}
		}
	}
	
	// Escape special characters
	sanitized := strings.ReplaceAll(input, "'", "''")
	sanitized = strings.ReplaceAll(sanitized, "\"", "\"\"")
	
	return sanitized, nil
}

// checkInjectionPatterns checks for various injection patterns
func (v *SecurityValidator) checkInjectionPatterns(input, fieldName string) error {
	// Check SQL injection
	for _, pattern := range sqlInjectionPatterns {
		if pattern.MatchString(input) {
			return &ValidationError{Field: fieldName, Message: "contains potentially dangerous SQL pattern", Value: input}
		}
	}
	
	// Check command injection
	for _, pattern := range commandInjectionPatterns {
		if pattern.MatchString(input) {
			return &ValidationError{Field: fieldName, Message: "contains potentially dangerous command pattern", Value: input}
		}
	}
	
	// Check XSS
	for _, pattern := range xssPatterns {
		if pattern.MatchString(input) {
			return &ValidationError{Field: fieldName, Message: "contains potentially dangerous XSS pattern", Value: input}
		}
	}
	
	return nil
} 