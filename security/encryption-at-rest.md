# Encryption at Rest for AWS Resources

## EKS (Kubernetes Secrets Encryption)

Use AWS KMS for envelope encryption of Kubernetes secrets:

```hcl
resource "aws_kms_key" "eks" {
  description = "KMS key for EKS secrets encryption"
}

resource "aws_eks_cluster" "main" {
  # ...
  encryption_config {
    resources = ["secrets"]
    provider {
      key_arn = aws_kms_key.eks.arn
    }
  }
}
```

## RDS (Database Encryption)

Enable storage encryption using AWS KMS:

```hcl
resource "aws_db_instance" "main" {
  # ...
  storage_encrypted = true
  kms_key_id        = aws_kms_key.rds.arn
}

resource "aws_kms_key" "rds" {
  description = "KMS key for RDS encryption"
}
```

## S3 (Bucket Encryption)

Enable default encryption for S3 buckets:

```hcl
resource "aws_s3_bucket" "main" {
  # ...
  server_side_encryption_configuration {
    rule {
      apply_server_side_encryption_by_default {
        sse_algorithm     = "aws:kms"
        kms_master_key_id = aws_kms_key.s3.arn
      }
    }
  }
}

resource "aws_kms_key" "s3" {
  description = "KMS key for S3 bucket encryption"
}
```

---

**Note:**
- Always use customer-managed KMS keys for maximum control.
- Enable encryption for all persistent storage (EBS, EFS) as well.
- Review AWS documentation for service-specific encryption options. 