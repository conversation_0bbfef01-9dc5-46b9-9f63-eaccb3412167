# Consolidated outputs.tf - All project-level outputs are defined here
# REMOVED duplicate outputs that exist in data.tf or main.tf

# VPC Outputs
output "vpc_id" {
  description = "The ID of the VPC"
  value       = module.vpc.vpc_id
}

output "private_subnet_ids" {
  description = "List of IDs of private subnets"
  value       = module.vpc.private_subnet_ids
}

output "public_subnet_ids" {
  description = "List of IDs of public subnets"
  value       = module.vpc.public_subnet_ids
}

# EKS Outputs
output "eks_cluster_name" {
  description = "The name of the EKS cluster"
  value       = "prod-architrave-eks"
}

output "eks_cluster_id" {
  description = "The ID of the EKS cluster"
  value       = module.eks.cluster_id
}

output "eks_oidc_provider_url" {
  description = "The URL of the OIDC Provider for the EKS cluster"
  value       = module.eks.oidc_provider_url
}

# RDS Outputs
output "rds_cluster_endpoint" {
  description = "Endpoint for the RDS cluster"
  value       = try(module.rds.cluster_endpoint, "")
  sensitive   = true
}

output "rds_master_password" {
  description = "Master password for the RDS cluster"
  value       = try(module.rds.master_password, "")
  sensitive   = true
}

output "rds_proxy_endpoint" {
  description = "The endpoint for the RDS proxy"
  value       = try(module.rds.rds_proxy_endpoint, "")
}

output "rds_secret_arn" {
  description = "ARN of the RDS master secret"
  value       = try(module.rds.master_secret_arn, "")
}

# Tenant Outputs
output "tenant_details" {
  description = "Details of created tenants"
  value = {
    for key, tenant in var.tenants : key => {
      namespace  = "${tenant.namespace_prefix}-${tenant.id}"
      db_role    = tenant.db_role
      rate_limit = tenant.rate_limit
    }
  }
}

# Route53 Outputs
output "route53_nameservers" {
  description = "Nameservers for the Route53 hosted zone"
  value       = module.route53.nameservers
}

output "domain_name" {
  description = "The domain name for the infrastructure"
  value       = var.domain_name
}

# The previous output has been removed since the related service is disabled

# DynamoDB Output
output "dynamodb_table_name" {
  description = "Name of the DynamoDB table"
  value       = module.dynamodb.dynamodb_table_name
}

# Bastion Outputs
output "bastion_public_ip" {
  description = "The public IP of the bastion instance"
  value       = try(module.bastion.public_ip, "")
}

output "ssh_key_secret_name" {
  description = "Name of the secret containing the SSH private key"
  value       = try(module.bastion.ssh_key_secret_name, "")
}

output "ssh_key_retrieval_command" {
  description = "Command to retrieve the SSH private key"
  value       = try(module.bastion.ssh_key_retrieval_command, "")
}

output "bastion_public_dns" {
  description = "The public DNS of the bastion instance"
  value       = try(module.bastion.bastion_public_dns, "")
}

output "ssh_connection_command" {
  description = "Command to connect via SSH"
  value       = try(module.bastion.ssh_connection_command, "")
}


# CI User Outputs (commented out for now)
#output "ci_user_access_key_id" {
#  description = "Access key ID for the CI user"
#  value       = module.ci_user[0].access_key_id
#  sensitive   = true
#}
#
#output "ci_user_secret_access_key" {
#  description = "Secret access key for the CI user"
#  value       = module.ci_user[0].secret_access_key
#  sensitive   = true
#}

# Commented out outputs for resources that no longer exist
# output "kubernetes_dashboard_url" {
#   description = "URL to access the Kubernetes Dashboard"
#   value       = module.eks.module.kubernetes_dashboard.dashboard_url
# }

# Istio Service Mesh Outputs
output "istio_namespace" {
  description = "Istio system namespace"
  value       = module.istio.istio_namespace
}

output "istio_version" {
  description = "Installed Istio version"
  value       = module.istio.istio_version
}

output "istio_mesh_id" {
  description = "Istio mesh ID"
  value       = module.istio.mesh_id
}

output "istio_ingress_gateway_status" {
  description = "Istio ingress gateway status"
  value       = module.istio.ingress_gateway_status
}

output "istio_mtls_enabled" {
  description = "Whether mTLS is enabled in Istio"
  value       = module.istio.mtls_enabled
}

# API Management Outputs
output "api_gateway_type" {
  description = "Type of API Gateway deployed"
  value       = module.api_management.api_gateway_type
}

output "api_endpoint" {
  description = "Main API Gateway endpoint URL"
  value       = module.api_management.api_endpoint
}

output "tenant_api_endpoints" {
  description = "API endpoints for each tenant"
  value       = module.api_management.tenant_endpoints
}

output "api_dashboard_url" {
  description = "CloudWatch dashboard URL for API metrics"
  value       = module.api_management.api_dashboard_url
}

output "api_rate_limiting_config" {
  description = "API rate limiting configuration"
  value       = module.api_management.rate_limiting_config
}

output "api_versions_config" {
  description = "API versioning configuration"
  value       = module.api_management.api_versions_config
}
