apiVersion: apps/v1
kind: Deployment
metadata:
  name: test-cycle-01-backend
  namespace: tenant-test-cycle-01
  labels:
    app: test-cycle-01-backend
    tenant: test-cycle-01
spec:
  replicas: 1
  selector:
    matchLabels:
      app: test-cycle-01-backend
  template:
    metadata:
      labels:
        app: test-cycle-01-backend
        tenant: test-cycle-01
    spec:
      initContainers:
      - name: ssl-cert-downloader
        image: amazon/aws-cli:2.0.6
        command: ["/bin/sh"]
        args:
        - -c
        - |
          echo "Downloading SSL certificate from S3..."
          aws s3 cp s3://architravetestdb/ssl-cert.pem /tmp/ssl-cert.pem || echo "SSL cert download failed, continuing..."
          ls -la /tmp/
        env:
        - name: AWS_DEFAULT_REGION
          value: "eu-central-1"
        volumeMounts:
        - name: ssl-cert-volume
          mountPath: /tmp
      - name: app-files-copier
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
        command: ["/bin/sh"]
        args:
        - -c
        - |
          echo "Copying application files from /storage/ArchAssets/..."
          cp -r /storage/ArchAssets/* /shared-app/
          echo "Application files copied successfully"
        volumeMounts:
        - name: shared-app-volume
          mountPath: /shared-app
      - name: php-config-fixer
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
        command: ["/bin/sh"]
        args:
        - -c
        - |
          echo "Creating PHP-FPM polyfill and email service bypass..."
          cat > /shared-app/php_fpm_polyfill.php << 'EOF'
          <?php
          if (!function_exists('apache_request_headers')) {
              function apache_request_headers() {
                  $headers = [];
                  foreach ($_SERVER as $key => $value) {
                      if (strpos($key, 'HTTP_') === 0) {
                          $header = str_replace('_', '-', substr($key, 5));
                          $headers[$header] = $value;
                      }
                  }
                  return $headers;
              }
          }
          if (!function_exists('apache_response_headers')) {
              function apache_response_headers() {
                  return headers_list();
              }
          }
          EOF
          echo "PHP-FPM polyfill created successfully"
          
          # Create email service bypass configuration
          echo "Creating email service bypass configuration..."
          mkdir -p /shared-app/config/autoload
          cat > /shared-app/config/autoload/email_bypass.local.php << 'EOF'
          <?php
          return [
              'service_manager' => [
                  'factories' => [
                      'SlmMail\Mail\Transport\MandrillTransport' => function($container) {
                          // Return a mock transport that logs instead of sending emails
                          return new \Laminas\Mail\Transport\File(
                              new \Laminas\Mail\Transport\FileOptions([
                                  'path' => '/tmp',
                                  'callback' => function($transport, $filename) {
                                      error_log("Email bypassed - would have been sent via Mandrill: " . $filename);
                                  }
                              ])
                          );
                      },
                  ],
              ],
              'notifications' => [
                  'transport_mode' => 'test', // Force test mode to bypass Mandrill
                  'tmp_dir' => '/tmp',
              ],
          ];
          EOF
          echo "Email service bypass configuration created"
          
          chmod -R 755 /shared-app
          chown -R www-data:www-data /shared-app
          echo "File permissions fixed"
        volumeMounts:
        - name: shared-app-volume
          mountPath: /shared-app
      - name: nginx-config-setup
        image: nginx:1.21-alpine
        command: ["/bin/sh"]
        args:
        - -c
        - |
          echo "Setting up nginx configuration for HTTP..."
          cat > /etc/nginx/conf.d/default.conf << 'EOF'
          server {
              listen 8080;
              server_name localhost;
              root /shared-app/public;
              index index.php index.html;
              
              location / {
                  try_files $uri $uri/ /index.php?$query_string;
              }
              
              location ~ \.php$ {
                  fastcgi_pass 127.0.0.1:9000;
                  fastcgi_index index.php;
                  fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                  include fastcgi_params;
                  fastcgi_param HTTP_HOST $host;
                  fastcgi_param SERVER_NAME $host;
              }
              
              location /api/ {
                  try_files $uri $uri/ /api/index.php?$query_string;
              }
              
              location ~ /api/.*\.php$ {
                  fastcgi_pass 127.0.0.1:9000;
                  fastcgi_index index.php;
                  fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                  include fastcgi_params;
                  fastcgi_param HTTP_HOST $host;
                  fastcgi_param SERVER_NAME $host;
              }
          }
          EOF
          echo "Nginx configuration created for HTTP on port 8080"
        volumeMounts:
        - name: nginx-config-volume
          mountPath: /etc/nginx/conf.d
      containers:
      - name: php-fpm
        image: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.56-test
        ports:
        - containerPort: 9000
        env:
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: test-cycle-01-secret
              key: DB_HOST
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: test-cycle-01-secret
              key: DB_PORT
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: test-cycle-01-secret
              key: DB_USER
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: test-cycle-01-secret
              key: DB_PASSWORD
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: test-cycle-01-secret
              key: DB_NAME
        - name: DB_SSL_CA
          valueFrom:
            secretKeyRef:
              name: test-cycle-01-secret
              key: DB_SSL_CA
        - name: DB_SSL_MODE
          valueFrom:
            secretKeyRef:
              name: test-cycle-01-secret
              key: DB_SSL_MODE
        - name: TENANT_ID
          valueFrom:
            configMapKeyRef:
              name: test-cycle-01-config
              key: TENANT_ID
        - name: DOMAIN
          valueFrom:
            configMapKeyRef:
              name: test-cycle-01-config
              key: DOMAIN
        - name: ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: test-cycle-01-config
              key: ENVIRONMENT
        - name: LANGUAGE
          valueFrom:
            configMapKeyRef:
              name: test-cycle-01-config
              key: LANGUAGE
        - name: ARCH_IS_DEVELOPMENT
          value: "false"
        volumeMounts:
        - name: shared-app-volume
          mountPath: /shared-app
        - name: ssl-cert-volume
          mountPath: /tmp
        workingDir: /shared-app
        livenessProbe:
          tcpSocket:
            port: 9000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          tcpSocket:
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
      - name: nginx
        image: nginx:1.21-alpine
        ports:
        - containerPort: 8080
        volumeMounts:
        - name: shared-app-volume
          mountPath: /shared-app
        - name: nginx-config-volume
          mountPath: /etc/nginx/conf.d
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      volumes:
      - name: shared-app-volume
        emptyDir: {}
      - name: ssl-cert-volume
        emptyDir: {}
      - name: nginx-config-volume
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
