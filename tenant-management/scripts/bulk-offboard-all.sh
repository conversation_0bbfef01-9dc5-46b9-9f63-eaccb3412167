#!/bin/bash

# Bulk Tenant Offboarding Script
# This script offboards all tenants except system tenants

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get all tenant namespaces (excluding system ones)
get_tenant_namespaces() {
    kubectl get namespaces -o name | grep "namespace/tenant-" | grep -v "tenant-system" | sed 's/namespace\/tenant-//'
}

# Extract tenant ID from namespace
extract_tenant_id() {
    local namespace="$1"
    echo "$namespace"
}

# Main execution
main() {
    log_info "Starting bulk tenant offboarding..."
    
    # Get all tenant namespaces
    local tenants=($(get_tenant_namespaces))
    local total_tenants=${#tenants[@]}
    
    if [[ $total_tenants -eq 0 ]]; then
        log_warning "No tenant namespaces found to offboard"
        exit 0
    fi
    
    log_info "Found $total_tenants tenants to offboard:"
    for tenant in "${tenants[@]}"; do
        echo "  - $tenant"
    done
    
    echo
    read -p "Are you sure you want to offboard ALL these tenants? (yes/no): " confirm
    if [[ "$confirm" != "yes" ]]; then
        log_info "Offboarding cancelled by user"
        exit 0
    fi
    
    log_info "Starting offboarding process..."
    local success_count=0
    local failure_count=0
    local failed_tenants=()
    
    for tenant in "${tenants[@]}"; do
        log_info "Offboarding tenant: $tenant ($((success_count + failure_count + 1))/$total_tenants)"
        
        if python3 advanced_tenant_offboard.py --tenant-id "$tenant" --force --skip-db-cleanup --skip-s3-cleanup; then
            log_success "Successfully offboarded tenant: $tenant"
            ((success_count++))
        else
            log_error "Failed to offboard tenant: $tenant"
            ((failure_count++))
            failed_tenants+=("$tenant")
        fi
        
        echo "----------------------------------------"
    done
    
    # Summary
    echo
    log_info "=== OFFBOARDING SUMMARY ==="
    log_success "Successfully offboarded: $success_count tenants"
    
    if [[ $failure_count -gt 0 ]]; then
        log_error "Failed to offboard: $failure_count tenants"
        log_error "Failed tenants:"
        for failed_tenant in "${failed_tenants[@]}"; do
            echo "  - $failed_tenant"
        done
    fi
    
    # Clean up any remaining resources
    log_info "Cleaning up remaining resources..."
    
    # Clean up any remaining ingresses
    log_info "Cleaning up remaining ingresses..."
    kubectl get ingress -A | grep -E "tenant-" | awk '{print $2 " -n " $1}' | xargs -r -n 3 kubectl delete ingress || true
    
    # Clean up any remaining services
    log_info "Cleaning up remaining services..."
    for ns in $(kubectl get namespaces -o name | grep "namespace/tenant-" | grep -v "tenant-system"); do
        ns_name=$(echo $ns | sed 's/namespace\///')
        kubectl delete namespace "$ns_name" --force --grace-period=0 || true
    done
    
    # Clean up any remaining PVCs
    log_info "Cleaning up remaining PVCs..."
    kubectl get pvc -A | grep -E "tenant-" | awk '{print $2 " -n " $1}' | xargs -r -n 3 kubectl delete pvc || true
    
    # Clean up any remaining secrets
    log_info "Cleaning up remaining secrets..."
    kubectl get secrets -A | grep -E "tenant-" | awk '{print $2 " -n " $1}' | xargs -r -n 3 kubectl delete secret || true
    
    # Clean up monitoring resources
    log_info "Cleaning up monitoring resources..."
    kubectl delete servicemonitor -A -l "tenant.architrave.io/tenant-id" || true
    kubectl delete prometheusrule -A -l "tenant.architrave.io/tenant-id" || true
    kubectl delete configmap -n monitoring -l "tenant.architrave.io/tenant-id" || true
    
    # Clean up Istio resources
    log_info "Cleaning up Istio resources..."
    kubectl delete virtualservice -A -l "tenant.architrave.io/tenant-id" || true
    kubectl delete destinationrule -A -l "tenant.architrave.io/tenant-id" || true
    kubectl delete peerauthentication -A -l "tenant.architrave.io/tenant-id" || true
    kubectl delete authorizationpolicy -A -l "tenant.architrave.io/tenant-id" || true
    kubectl delete telemetry -A -l "tenant.architrave.io/tenant-id" || true
    
    # Clean up KEDA resources
    log_info "Cleaning up KEDA resources..."
    kubectl delete scaledobject -A -l "tenant.architrave.io/tenant-id" || true
    
    log_success "Bulk offboarding completed!"
    log_info "Successfully offboarded: $success_count/$total_tenants tenants"
    
    if [[ $failure_count -gt 0 ]]; then
        log_warning "Some tenants failed to offboard completely, but cleanup was forced"
        exit 1
    fi
}

# Run main function
main "$@"
