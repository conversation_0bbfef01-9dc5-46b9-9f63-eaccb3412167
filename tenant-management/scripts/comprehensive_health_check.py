#!/usr/bin/env python3
"""
Comprehensive Health Check Script
Checks database tables, health endpoints, backend, frontend, nginx, RabbitMQ, database
Runs SELECT statements and verifies all components
"""

import subprocess
import sys
import tempfile
import json
from datetime import datetime

def run_command(command, timeout=15):
    """Run a command with timeout."""
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except subprocess.TimeoutExpired:
        return False, "", "Command timed out"
    except Exception as e:
        return False, "", str(e)

def print_header(title):
    """Print formatted header."""
    print(f"\n{'='*60}")
    print(f"🔍 {title}")
    print(f"{'='*60}")

def print_result(test_name, success, details=""):
    """Print test result."""
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status}: {test_name}")
    if details:
        print(f"   Details: {details}")

def create_health_check_pod():
    """Create a comprehensive health check pod."""
    print("🔧 Creating comprehensive health check pod...")
    
    pod_yaml = """
apiVersion: v1
kind: Pod
metadata:
  name: comprehensive-health-check
  namespace: default
spec:
  restartPolicy: Never
  containers:
  - name: health-checker
    image: mysql:8.0
    command: ["sleep", "300"]
    env:
    - name: MYSQL_PWD
      value: "&BZzY_<AK(=a*UhZ"
    resources:
      limits:
        cpu: 200m
        memory: 512Mi
      requests:
        cpu: 100m
        memory: 256Mi
  - name: curl-checker
    image: curlimages/curl:latest
    command: ["sleep", "300"]
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.yaml') as f:
        f.write(pod_yaml)
        pod_file = f.name
    
    try:
        # Create pod
        success, stdout, stderr = run_command(f"kubectl apply -f {pod_file}")
        if success:
            print("✅ Health check pod created")
            
            # Wait for pod to be ready
            print("⏳ Waiting for pod to be ready...")
            success, stdout, stderr = run_command("kubectl wait --for=condition=ready pod/comprehensive-health-check --timeout=60s")
            if success:
                print("✅ Health check pod is ready")
                return True
            else:
                print(f"❌ Pod not ready: {stderr}")
                return False
        else:
            print(f"❌ Failed to create pod: {stderr}")
            return False
    finally:
        import os
        os.unlink(pod_file)

def check_database_tables():
    """Check database tables with SELECT statements."""
    print_header("DATABASE TABLES & SELECT STATEMENTS CHECK")
    
    db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
    db_port = "3306"
    db_user = "admin"
    db_name = "architrave"
    
    results = []
    
    # Test 1: Basic connection
    success, stdout, stderr = run_command(
        f"kubectl exec comprehensive-health-check -c health-checker -- mysql -h {db_host} -P {db_port} -u {db_user} -e 'SELECT 1 as connection_test;'", 15)
    
    connection_success = success and "connection_test" in stdout
    print_result("Database Connection", connection_success, 
                f"Connection to {db_host} {'successful' if connection_success else 'failed'}")
    results.append(connection_success)
    
    # Test 2: Show databases
    success, stdout, stderr = run_command(
        f"kubectl exec comprehensive-health-check -c health-checker -- mysql -h {db_host} -P {db_port} -u {db_user} -e 'SHOW DATABASES;'", 15)
    
    databases_visible = success and "architrave" in stdout
    print_result("Show Databases", databases_visible, 
                f"Architrave database {'visible' if databases_visible else 'not visible'}")
    results.append(databases_visible)
    
    # Test 3: Show tables
    success, stdout, stderr = run_command(
        f"kubectl exec comprehensive-health-check -c health-checker -- mysql -h {db_host} -P {db_port} -u {db_user} {db_name} -e 'SHOW TABLES;'", 15)
    
    tables_visible = success and len(stdout.split('\n')) > 2
    table_count = len(stdout.split('\n')) - 1 if success else 0
    print_result("Show Tables", tables_visible, 
                f"Found {table_count} tables in architrave database")
    results.append(tables_visible)
    
    # Test 4: SELECT from specific tables
    test_queries = [
        ("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA='architrave';", "Table Count"),
        ("SELECT NOW() as current_time, VERSION() as mysql_version;", "System Info"),
        ("SELECT 1+1 as math_test;", "Math Test"),
    ]
    
    for query, test_name in test_queries:
        success, stdout, stderr = run_command(
            f"kubectl exec comprehensive-health-check -c health-checker -- mysql -h {db_host} -P {db_port} -u {db_user} {db_name} -e \"{query}\"", 15)
        
        query_success = success and len(stdout.strip()) > 0
        print_result(f"SELECT Query - {test_name}", query_success, 
                    f"Query {'executed successfully' if query_success else 'failed'}")
        results.append(query_success)
    
    # Test 5: Check for tenant_config table (if exists)
    success, stdout, stderr = run_command(
        f"kubectl exec comprehensive-health-check -c health-checker -- mysql -h {db_host} -P {db_port} -u {db_user} {db_name} -e 'DESCRIBE tenant_config;' 2>/dev/null", 15)
    
    tenant_table_exists = success and "Field" in stdout
    print_result("Tenant Config Table", tenant_table_exists, 
                f"tenant_config table {'exists' if tenant_table_exists else 'does not exist'}")
    results.append(tenant_table_exists)
    
    return results

def check_tenant_components():
    """Check tenant components (backend, frontend, nginx, RabbitMQ)."""
    print_header("TENANT COMPONENTS HEALTH CHECK")
    
    results = []
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=10s", 12)
    if not success:
        print_result("Get Tenant Namespaces", False, "Cannot get namespaces")
        return [False]
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    
    if not tenant_namespaces:
        print_result("Tenant Namespaces Found", False, "No tenant namespaces found")
        return [False]
    
    print_result("Tenant Namespaces Found", True, f"Found {len(tenant_namespaces)} tenant namespaces")
    results.append(True)
    
    # Check first tenant in detail
    ns = tenant_namespaces[0]
    tenant_id = ns.replace('tenant-', '')
    
    # Check pods
    success, stdout, stderr = run_command(f"kubectl get pods -n {ns} --no-headers --request-timeout=10s", 12)
    if success:
        pods = [line for line in stdout.split('\n') if line.strip()]
        running_pods = [line for line in pods if 'Running' in line]
        
        pod_health = len(running_pods) >= 1
        print_result(f"Tenant {tenant_id} Pods", pod_health, 
                    f"{len(running_pods)}/{len(pods)} pods running")
        results.append(pod_health)
        
        # Check specific components
        components = ['backend', 'frontend', 'rabbitmq']
        for component in components:
            component_pods = [line for line in pods if component in line]
            component_running = any('Running' in line for line in component_pods)
            print_result(f"Tenant {tenant_id} {component.title()}", component_running,
                        f"{component} {'running' if component_running else 'not running'}")
            results.append(component_running)
    else:
        print_result(f"Tenant {tenant_id} Pods Check", False, "Cannot get pod status")
        results.append(False)
    
    # Check services
    success, stdout, stderr = run_command(f"kubectl get services -n {ns} --no-headers --request-timeout=10s", 12)
    if success:
        services = [line.split()[0] for line in stdout.split('\n') if line.strip()]
        
        expected_services = ['backend', 'frontend', 'rabbitmq']
        for service in expected_services:
            service_exists = any(service in s for s in services)
            print_result(f"Tenant {tenant_id} {service.title()} Service", service_exists,
                        f"{service} service {'exists' if service_exists else 'missing'}")
            results.append(service_exists)
    else:
        print_result(f"Tenant {tenant_id} Services Check", False, "Cannot get service status")
        results.append(False)
    
    return results

def check_health_endpoints():
    """Check health endpoints for tenant components."""
    print_header("HEALTH ENDPOINTS CHECK")
    
    results = []
    
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=10s", 12)
    if not success:
        print_result("Health Endpoints Check", False, "Cannot get namespaces")
        return [False]
    
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    
    if not tenant_namespaces:
        print_result("Health Endpoints Check", False, "No tenant namespaces found")
        return [False]
    
    # Check first tenant
    ns = tenant_namespaces[0]
    tenant_id = ns.replace('tenant-', '')
    
    # Try to check backend health endpoint
    backend_service = f"tenant-{tenant_id}-backend-service"
    success, stdout, stderr = run_command(
        f"kubectl exec comprehensive-health-check -c curl-checker -- curl -s --connect-timeout 5 http://{backend_service}.{ns}.svc.cluster.local/health 2>/dev/null", 10)
    
    backend_health = success and ("healthy" in stdout.lower() or "ok" in stdout.lower())
    print_result(f"Backend Health Endpoint", backend_health,
                f"Backend health endpoint {'responding' if backend_health else 'not responding'}")
    results.append(backend_health)
    
    # Try to check frontend health
    frontend_service = f"tenant-{tenant_id}-frontend-service"
    success, stdout, stderr = run_command(
        f"kubectl exec comprehensive-health-check -c curl-checker -- curl -s --connect-timeout 5 http://{frontend_service}.{ns}.svc.cluster.local/ 2>/dev/null", 10)
    
    frontend_health = success and len(stdout) > 0
    print_result(f"Frontend Health Check", frontend_health,
                f"Frontend {'responding' if frontend_health else 'not responding'}")
    results.append(frontend_health)
    
    # Try to check RabbitMQ management interface
    rabbitmq_service = f"tenant-{tenant_id}-rabbitmq-service"
    success, stdout, stderr = run_command(
        f"kubectl exec comprehensive-health-check -c curl-checker -- curl -s --connect-timeout 5 http://{rabbitmq_service}.{ns}.svc.cluster.local:15672/ 2>/dev/null", 10)
    
    rabbitmq_health = success and ("rabbitmq" in stdout.lower() or "management" in stdout.lower())
    print_result(f"RabbitMQ Health Check", rabbitmq_health,
                f"RabbitMQ management {'responding' if rabbitmq_health else 'not responding'}")
    results.append(rabbitmq_health)
    
    return results

def check_s3_mount_and_directories():
    """Check S3 PVC, mount, and directory structure for all tenants."""
    print_header("S3 MOUNT & DIRECTORY STRUCTURE VALIDATION")
    results = []
    # Get tenant namespaces
    success, stdout, stderr = run_command("kubectl get namespaces --no-headers --request-timeout=10s", 12)
    if not success:
        print_result("Get Tenant Namespaces", False, "Cannot get namespaces")
        return [False]
    tenant_namespaces = [line.split()[0] for line in stdout.split('\n') if 'tenant-' in line and line.strip()]
    if not tenant_namespaces:
        print_result("Tenant Namespaces Found", False, "No tenant namespaces found")
        return [False]
    print_result("Tenant Namespaces Found", True, f"Found {len(tenant_namespaces)} tenant namespaces")
    results.append(True)
    # Directory structure to check
    expected_dirs = [
        "assets/", "backups/", "logo/", "logs/", "tmp/", "transfer/", "quarantine/", "uploads/", "exports/", "archive/"
    ]
    for ns in tenant_namespaces:
        tenant_id = ns.replace('tenant-', '')
        print("\n--- Checking tenant: {} ---".format(tenant_id))
        # 1. Check PVC
        pvc_name = f"tenant-{tenant_id}-s3-pvc"
        success, pvc_out, _ = run_command(f"kubectl get pvc {pvc_name} -n {ns} -o json", 10)
        pvc_bound = False
        if success:
            try:
                pvc_json = json.loads(pvc_out)
                pvc_bound = pvc_json['status']['phase'] == 'Bound'
            except Exception:
                pvc_bound = False
        print_result(f"PVC Bound ({pvc_name})", pvc_bound)
        results.append(pvc_bound)
        # 2. Check mount in frontend/backend pod
        for comp in ["frontend", "backend"]:
            pod_success, pod_out, _ = run_command(f"kubectl get pods -n {ns} -l app={comp} -o json", 10)
            pod_mounted = False
            if pod_success:
                try:
                    pod_json = json.loads(pod_out)
                    for item in pod_json['items']:
                        pod_name = item['metadata']['name']
                        # Try to exec ls in /storage/clear (or /mnt/s3)
                        for mount_path in ["/storage/clear", "/mnt/s3", "/storage"]:
                            ls_success, ls_out, _ = run_command(f"kubectl exec -n {ns} {pod_name} -- ls {mount_path}", 8)
                            if ls_success and any(d.replace('/', '') in ls_out for d in ["assets", "backups", "logo"]):
                                pod_mounted = True
                                break
                        if pod_mounted:
                            break
                except Exception:
                    pod_mounted = False
            print_result(f"{comp.title()} Pod S3 Mount", pod_mounted)
            results.append(pod_mounted)
        # 3. Check S3 bucket directory structure
        bucket_name = f"tenant-{tenant_id}-assets"
        all_dirs_exist = True
        for d in expected_dirs:
            s3_success, s3_out, _ = run_command(f"aws s3 ls s3://{bucket_name}/{d}", 10)
            if not s3_success:
                all_dirs_exist = False
                print_result(f"S3 Dir {d}", False, f"Missing in {bucket_name}")
            else:
                print_result(f"S3 Dir {d}", True)
        results.append(all_dirs_exist)
    return results

def cleanup_health_check_pod():
    """Clean up the health check pod."""
    print("\n🧹 Cleaning up health check pod...")
    success, stdout, stderr = run_command("kubectl delete pod comprehensive-health-check --ignore-not-found=true")
    if success:
        print("✅ Health check pod cleaned up")
    else:
        print(f"⚠️ Cleanup warning: {stderr}")

def main():
    """Main health check function."""
    print("🔍 COMPREHENSIVE HEALTH CHECK")
    print("=" * 50)
    print(f"Started at: {datetime.now()}")
    
    try:
        # Create health check pod
        if not create_health_check_pod():
            print("❌ Failed to create health check pod")
            return 1
        
        # Run all health checks
        db_results = check_database_tables()
        component_results = check_tenant_components()
        endpoint_results = check_health_endpoints()
        s3_results = check_s3_mount_and_directories()
        
        # Calculate overall health
        all_results = db_results + component_results + endpoint_results + s3_results
        passed = sum(all_results)
        total = len(all_results)
        
        print_header("COMPREHENSIVE HEALTH SUMMARY")
        print(f"Tests passed: {passed}/{total}")
        print(f"Success rate: {(passed/total)*100:.1f}%")
        
        # Categorize results
        db_passed = sum(db_results)
        db_total = len(db_results)
        component_passed = sum(component_results)
        component_total = len(component_results)
        endpoint_passed = sum(endpoint_results)
        endpoint_total = len(endpoint_results)
        s3_passed = sum(s3_results)
        s3_total = len(s3_results)
        
        print(f"\n📊 DETAILED BREAKDOWN:")
        print(f"Database Health: {db_passed}/{db_total} ({(db_passed/db_total)*100:.1f}%)")
        print(f"Component Health: {component_passed}/{component_total} ({(component_passed/component_total)*100:.1f}%)")
        print(f"Endpoint Health: {endpoint_passed}/{endpoint_total} ({(endpoint_passed/endpoint_total)*100:.1f}%)")
        print(f"S3 Mount/Dirs: {s3_passed}/{s3_total} ({(s3_passed/s3_total)*100:.1f}%)")
        
        if passed >= total * 0.7:  # 70% pass rate
            print("\n🎉 COMPREHENSIVE HEALTH CHECK PASSED!")
            print("✅ System is functioning well overall")
            return 0
        else:
            print("\n⚠️ COMPREHENSIVE HEALTH CHECK ISSUES")
            print("❌ System has significant health issues")
            return 1
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return 1
    finally:
        cleanup_health_check_pod()

if __name__ == "__main__":
    sys.exit(main())
