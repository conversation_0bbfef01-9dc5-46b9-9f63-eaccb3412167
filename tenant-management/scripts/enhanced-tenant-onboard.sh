#!/bin/bash

# Enhanced Tenant Onboarding Script with Monitoring, Security, and Advanced Networking
# This script extends the existing onboarding with comprehensive observability and security

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TENANT_ID=""
TENANT_NAME=""
SKIP_DNS=false
SKIP_WEB_CHECK=false
ENABLE_MONITORING=true
ENABLE_SECURITY=true
ENABLE_ADVANCED_NETWORKING=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --tenant-id)
                TENANT_ID="$2"
                shift 2
                ;;
            --tenant-name)
                TENANT_NAME="$2"
                shift 2
                ;;
            --skip-dns)
                SKIP_DNS=true
                shift
                ;;
            --skip-web-check)
                SKIP_WEB_CHECK=true
                shift
                ;;
            --disable-monitoring)
                ENABLE_MONITORING=false
                shift
                ;;
            --disable-security)
                ENABLE_SECURITY=false
                shift
                ;;
            --disable-advanced-networking)
                ENABLE_ADVANCED_NETWORKING=false
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # Validate required parameters
    if [[ -z "$TENANT_ID" ]]; then
        log_error "Tenant ID is required. Use --tenant-id"
        exit 1
    fi

    if [[ -z "$TENANT_NAME" ]]; then
        TENANT_NAME="$TENANT_ID"
        log_warning "Tenant name not provided, using tenant ID: $TENANT_NAME"
    fi
}

show_help() {
    cat << EOF
Enhanced Tenant Onboarding Script

Usage: $0 --tenant-id TENANT_ID [OPTIONS]

Required:
  --tenant-id ID              Unique identifier for the tenant

Optional:
  --tenant-name NAME          Human-readable name for the tenant
  --skip-dns                  Skip DNS configuration
  --skip-web-check           Skip web accessibility check
  --disable-monitoring       Skip monitoring setup
  --disable-security         Skip security hardening
  --disable-advanced-networking  Skip advanced Istio features
  -h, --help                 Show this help message

Examples:
  $0 --tenant-id mycompany --tenant-name "My Company"
  $0 --tenant-id testcorp --skip-dns --disable-monitoring
EOF
}

# Function to apply security configurations
apply_security_configs() {
    if [[ "$ENABLE_SECURITY" != "true" ]]; then
        log_info "Security configurations disabled, skipping..."
        return 0
    fi

    log_info "Applying enhanced security configurations for tenant $TENANT_ID..."
    
    local namespace="tenant-$TENANT_ID"
    local temp_file=$(mktemp)
    
    # Replace placeholders in security template
    sed -e "s/TENANT_ID_PLACEHOLDER/$TENANT_ID/g" \
        -e "s/TENANT_NAMESPACE_PLACEHOLDER/$namespace/g" \
        "$SCRIPT_DIR/enhanced-tenant-security.yaml" > "$temp_file"
    
    # Apply security configurations
    if kubectl apply -f "$temp_file"; then
        log_success "Security configurations applied successfully"
    else
        log_error "Failed to apply security configurations"
        rm -f "$temp_file"
        return 1
    fi
    
    rm -f "$temp_file"
    
    # Wait for network policies to be applied
    log_info "Waiting for network policies to be applied..."
    sleep 5
    
    # Verify security policies
    if kubectl get networkpolicy -n "$namespace" tenant-isolation-policy >/dev/null 2>&1; then
        log_success "Network isolation policy applied"
    else
        log_warning "Network isolation policy not found"
    fi
    
    if kubectl get resourcequota -n "$namespace" tenant-resource-quota >/dev/null 2>&1; then
        log_success "Resource quota applied"
    else
        log_warning "Resource quota not found"
    fi
}

# Function to apply advanced networking configurations
apply_advanced_networking() {
    if [[ "$ENABLE_ADVANCED_NETWORKING" != "true" ]]; then
        log_info "Advanced networking disabled, skipping..."
        return 0
    fi

    log_info "Applying advanced Istio networking configurations for tenant $TENANT_ID..."
    
    local namespace="tenant-$TENANT_ID"
    local temp_file=$(mktemp)
    
    # Replace placeholders in networking template
    sed -e "s/TENANT_ID_PLACEHOLDER/$TENANT_ID/g" \
        -e "s/TENANT_NAMESPACE_PLACEHOLDER/$namespace/g" \
        "$SCRIPT_DIR/advanced-istio-networking.yaml" > "$temp_file"
    
    # Apply advanced networking configurations
    if kubectl apply -f "$temp_file"; then
        log_success "Advanced networking configurations applied successfully"
    else
        log_error "Failed to apply advanced networking configurations"
        rm -f "$temp_file"
        return 1
    fi
    
    rm -f "$temp_file"
    
    # Wait for Istio configurations to be applied
    log_info "Waiting for Istio configurations to be applied..."
    sleep 10
    
    # Verify Istio configurations
    if kubectl get virtualservice -n "$namespace" tenant-advanced-vs >/dev/null 2>&1; then
        log_success "Advanced VirtualService applied"
    else
        log_warning "Advanced VirtualService not found"
    fi
    
    if kubectl get destinationrule -n "$namespace" tenant-advanced-dr >/dev/null 2>&1; then
        log_success "Advanced DestinationRule applied"
    else
        log_warning "Advanced DestinationRule not found"
    fi
    
    if kubectl get peerauthentication -n "$namespace" tenant-mtls >/dev/null 2>&1; then
        log_success "mTLS policy applied"
    else
        log_warning "mTLS policy not found"
    fi
}

# Function to apply monitoring configurations
apply_monitoring_configs() {
    if [[ "$ENABLE_MONITORING" != "true" ]]; then
        log_info "Monitoring configurations disabled, skipping..."
        return 0
    fi

    log_info "Applying comprehensive monitoring configurations for tenant $TENANT_ID..."
    
    local namespace="tenant-$TENANT_ID"
    local temp_file=$(mktemp)
    
    # Replace placeholders in monitoring template
    sed -e "s/TENANT_ID_PLACEHOLDER/$TENANT_ID/g" \
        -e "s/TENANT_NAMESPACE_PLACEHOLDER/$namespace/g" \
        "$SCRIPT_DIR/tenant-monitoring-config.yaml" > "$temp_file"
    
    # Apply monitoring configurations
    if kubectl apply -f "$temp_file"; then
        log_success "Monitoring configurations applied successfully"
    else
        log_error "Failed to apply monitoring configurations"
        rm -f "$temp_file"
        return 1
    fi
    
    rm -f "$temp_file"
    
    # Verify monitoring configurations
    if kubectl get servicemonitor -n "$namespace" tenant-app-monitor >/dev/null 2>&1; then
        log_success "ServiceMonitor applied"
    else
        log_warning "ServiceMonitor not found"
    fi
    
    if kubectl get prometheusrule -n "$namespace" tenant-specific-alerts >/dev/null 2>&1; then
        log_success "PrometheusRule applied"
    else
        log_warning "PrometheusRule not found"
    fi
    
    if kubectl get configmap -n monitoring tenant-dashboard >/dev/null 2>&1; then
        log_success "Grafana dashboard applied"
    else
        log_warning "Grafana dashboard not found"
    fi
}

# Main execution function
main() {
    log_info "Starting Enhanced Tenant Onboarding for: $TENANT_ID"
    log_info "Tenant Name: $TENANT_NAME"
    log_info "Monitoring: $ENABLE_MONITORING"
    log_info "Security: $ENABLE_SECURITY"
    log_info "Advanced Networking: $ENABLE_ADVANCED_NETWORKING"
    
    # Step 1: Run the existing onboarding script
    log_info "Step 1: Running base tenant onboarding..."
    local onboard_cmd="./advanced_tenant_onboard --tenant-id $TENANT_ID --tenant-name \"$TENANT_NAME\""
    
    if [[ "$SKIP_DNS" == "true" ]]; then
        onboard_cmd="$onboard_cmd --skip-dns"
    fi
    
    if [[ "$SKIP_WEB_CHECK" == "true" ]]; then
        onboard_cmd="$onboard_cmd --skip-web-check"
    fi
    
    if eval "$onboard_cmd"; then
        log_success "Base tenant onboarding completed"
    else
        log_error "Base tenant onboarding failed"
        exit 1
    fi
    
    # Step 2: Apply security configurations
    log_info "Step 2: Applying security configurations..."
    apply_security_configs
    
    # Step 3: Apply advanced networking
    log_info "Step 3: Applying advanced networking configurations..."
    apply_advanced_networking
    
    # Step 4: Apply monitoring configurations
    log_info "Step 4: Applying monitoring configurations..."
    apply_monitoring_configs
    
    # Step 5: Final verification
    log_info "Step 5: Final verification..."
    local namespace="tenant-$TENANT_ID"
    
    # Check namespace exists
    if kubectl get namespace "$namespace" >/dev/null 2>&1; then
        log_success "Namespace $namespace exists"
    else
        log_error "Namespace $namespace not found"
        exit 1
    fi
    
    # Check pods are running
    local running_pods=$(kubectl get pods -n "$namespace" --field-selector=status.phase=Running --no-headers 2>/dev/null | wc -l)
    log_success "Running pods in $namespace: $running_pods"
    
    log_success "Enhanced tenant onboarding completed successfully!"
    log_info "Tenant: $TENANT_ID"
    log_info "Namespace: $namespace"
    log_info "Domain: $TENANT_ID.architrave-assets.com"
    
    if [[ "$ENABLE_MONITORING" == "true" ]]; then
        log_info "Grafana Dashboard: Available in monitoring namespace"
        log_info "Prometheus Alerts: Configured for SLA monitoring"
    fi
    
    if [[ "$ENABLE_SECURITY" == "true" ]]; then
        log_info "Security: Network policies and RBAC applied"
    fi
    
    if [[ "$ENABLE_ADVANCED_NETWORKING" == "true" ]]; then
        log_info "Networking: Circuit breakers and rate limiting enabled"
    fi
}

# Parse arguments and run main function
parse_args "$@"
main
