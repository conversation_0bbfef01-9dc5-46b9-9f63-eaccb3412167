#!/bin/bash

# Cleanup Unnecessary Files Script
# This script removes all unnecessary test files, temporary files, documentation, 
# examples, and other non-essential files from the codebase

set -e

echo "🧹 Starting cleanup of unnecessary files..."
echo "================================================"

# Function to safely remove files/directories
safe_remove() {
    local path="$1"
    if [ -e "$path" ]; then
        echo "🗑️  Removing: $path"
        rm -rf "$path"
    else
        echo "⚠️  Not found: $path"
    fi
}

# Function to remove files by pattern
remove_pattern() {
    local pattern="$1"
    local description="$2"
    echo "🔍 Removing $description..."
    find . -name "$pattern" -type f -exec rm -f {} \; 2>/dev/null || true
}

# Change to repository root
cd "$(dirname "$0")/../.."

echo "📍 Current directory: $(pwd)"
echo ""

# 1. Remove test and documentation files
echo "1️⃣  Removing test and documentation files..."
safe_remove "testing/"
safe_remove "tenant-management/docs/"
safe_remove "tenant-management/examples/"
safe_remove ".gitlab-ci-local/"
safe_remove ".trunk/"

# 2. Remove README and documentation files
echo "2️⃣  Removing README and documentation files..."
remove_pattern "README*.md" "README files"
remove_pattern "*.adoc" "AsciiDoc files"
remove_pattern "COMPLETION_SUMMARY.md" "completion summaries"
remove_pattern "TEST_RESULTS*.md" "test result files"
remove_pattern "SECURITY*.md" "security documentation"
remove_pattern "SUMMARY.md" "summary files"
remove_pattern "MODULE_CLEANUP.md" "module cleanup docs"
remove_pattern "MULTI_TENANT_ARCHITECTURE.md" "architecture docs"
remove_pattern "TENANT_ENHANCEMENTS.md" "enhancement docs"

# 3. Remove test scripts
echo "3️⃣  Removing test scripts..."
remove_pattern "test_*.sh" "test shell scripts"
remove_pattern "test_*.py" "test Python scripts"
remove_pattern "*_test.py" "Python test files"
remove_pattern "test-*.yaml" "test YAML files"

# 4. Remove temporary and backup files
echo "4️⃣  Removing temporary and backup files..."
remove_pattern "*.tmp" "temporary files"
remove_pattern "*.bak" "backup files"
remove_pattern "*.swp" "swap files"
remove_pattern "*~" "editor backup files"
remove_pattern "*.log" "log files"
remove_pattern "nohup.out" "nohup output files"

# 5. Remove security scan and analysis files
echo "5️⃣  Removing security scan files..."
remove_pattern "tfsec-*.json" "tfsec scan results"
remove_pattern "*.plan" "Terraform plan files"
remove_pattern "plan.out" "Terraform plan output"
remove_pattern "tfplan" "Terraform plan files"
safe_remove "security_scan_results/"

# 6. Remove specific unnecessary scripts
echo "6️⃣  Removing specific unnecessary scripts..."
safe_remove "tenant-management/scripts/simple_tenant_discovery.py"
safe_remove "tenant-management/scripts/find_and_remove_all_tenants.py"
safe_remove "tenant-management/scripts/comprehensive_health_check.py"
safe_remove "check-security-issues.sh"
safe_remove "cleanup_modules.sh"
safe_remove "disable_all_deletion_protection.sh"
safe_remove "enhance-tenant-backup-security.sh"
safe_remove "get-pip.py"
safe_remove "prerequisites_setup.log"
safe_remove "activate.sh"

# 7. Remove backup configuration files
echo "7️⃣  Removing backup configuration files..."
remove_pattern ".gitlab-ci.yml.backup*" "GitLab CI backup files"
remove_pattern "*.backup" "backup files"

# 8. Remove module backups and disabled modules
echo "8️⃣  Removing module backups..."
safe_remove "module_backups_*/"
safe_remove "modules/elasticsearch/"
safe_remove "modules/elasticsearch.disabled/"

# 9. Remove unnecessary modules (as per .gitignore)
echo "9️⃣  Removing unnecessary modules..."
safe_remove "modules/backup/"
safe_remove "modules/centralized_logging/"
safe_remove "modules/ci_user/"
safe_remove "modules/cost_management/"
safe_remove "modules/firewall/"
safe_remove "modules/kubernetes_dashboard/"
safe_remove "modules/mq/"
safe_remove "modules/nacl/"
safe_remove "modules/network/"
safe_remove "modules/rmq/"
safe_remove "modules/waf/"

# 10. Remove Python cache and virtual environments
echo "🔟 Removing Python cache and virtual environments..."
remove_pattern "__pycache__" "Python cache directories"
remove_pattern "*.pyc" "Python compiled files"
remove_pattern "*.pyo" "Python optimized files"
remove_pattern "*.py[cod]" "Python compiled files"
remove_pattern "*$py.class" "Python class files"
safe_remove ".venv/"
safe_remove "venv/"

# 11. Remove Terraform temporary files
echo "1️⃣1️⃣ Removing Terraform temporary files..."
safe_remove ".terraform/"
safe_remove ".terraform.lock.hcl"
remove_pattern "*.tfstate" "Terraform state files"
remove_pattern "*.tfstate.*" "Terraform state backup files"
remove_pattern "*.tfvars.json" "Terraform variables JSON files"
safe_remove ".terragrunt-cache/"

# 12. Remove other temporary files
echo "1️⃣2️⃣ Removing other temporary files..."
remove_pattern "*.tgz" "compressed files"
remove_pattern "*.zip" "zip files"
remove_pattern ".DS_Store" "macOS system files"
safe_remove "crash.log"
safe_remove "kubeconfig"
remove_pattern "kubeconfig_*" "kubeconfig files"
safe_remove "aws_credentials"

# 13. Remove Istio samples
echo "1️⃣3️⃣ Removing Istio samples..."
safe_remove "istio-1.17.2/samples/"

# 14. Remove additional unnecessary Python scripts from root
echo "1️⃣4️⃣ Removing additional unnecessary Python scripts..."
safe_remove "final_health_check_and_fix.py"
safe_remove "comprehensive_verification_remediation.py"
safe_remove "comprehensive_verification_with_operations.py"
safe_remove "tenant-event-integration.py"
safe_remove "quick_health_check.py"
safe_remove "mass_operations_monitor.py"
safe_remove "final_verification_after_remediation.py"
safe_remove "final_100_percent_verification.py"
safe_remove "comprehensive_tenant_verification.py"
safe_remove "make_everything_100_percent.py"
safe_remove "comprehensive_system_diagnostic.py"
safe_remove "comprehensive_remediation_script.py"
safe_remove "comprehensive_database_health_verification.py"
safe_remove "comprehensive_system_verification.py"

# 15. Remove UI and debug scripts (keep only essential onboarding)
echo "1️⃣5️⃣ Removing UI and debug scripts..."
safe_remove "scripts/tenant/ui/"
safe_remove "scripts/tenant/onboarding/debug_tenant.py"

# 16. Remove Makefile (not needed for production)
echo "1️⃣6️⃣ Removing Makefile..."
safe_remove "Makefile"

# 17. Clean up any remaining test or verification scripts
echo "1️⃣7️⃣ Removing remaining verification scripts..."
remove_pattern "*verification*.py" "verification scripts"
remove_pattern "*diagnostic*.py" "diagnostic scripts"
remove_pattern "*health_check*.py" "health check scripts"
remove_pattern "*monitor*.py" "monitoring scripts"
remove_pattern "*remediation*.py" "remediation scripts"

echo ""
echo "✅ Cleanup completed!"
echo "================================================"
echo "📊 Summary of cleanup actions:"
echo "   - Removed test and documentation directories"
echo "   - Removed README and documentation files"
echo "   - Removed test scripts and files"
echo "   - Removed temporary and backup files"
echo "   - Removed security scan results"
echo "   - Removed unnecessary Python scripts"
echo "   - Removed backup configuration files"
echo "   - Removed module backups and disabled modules"
echo "   - Removed unnecessary infrastructure modules"
echo "   - Removed Python cache and virtual environments"
echo "   - Removed Terraform temporary files"
echo "   - Removed other temporary and system files"
echo ""
echo "🎯 The codebase now contains only essential files for:"
echo "   - Infrastructure provisioning"
echo "   - Tenant onboarding/offboarding"
echo "   - Core operational scripts"
echo ""
