#!/usr/bin/env python3
"""
Find and Remove All Tenants from Aurora Database
===============================================
This script finds all existing tenants in the Aurora database and removes them completely.
It provides both a discovery mode to list all tenants and a removal mode to delete them.
"""

import argparse
import asyncio
import boto3
import json
import logging
import os
import re
import subprocess
import sys
import tempfile
import time
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Union

# Import security modules
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

try:
    from security.credentials import get_rds_credentials
    from security.validators import validate_tenant_id, ValidationError
except ImportError:
    print("Warning: Security modules not found. Some security features may not work.")
    get_rds_credentials = None
    validate_tenant_id = None
    ValidationError = Exception

# Third-party libraries for enhanced terminal output
try:
    from rich.console import Console
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.panel import Panel
    from rich.logging import RichHandler
    RICH_AVAILABLE = True
except ImportError:
    print("Rich library not found. Installing...")
    subprocess.run([sys.executable, "-m", "pip", "install", "rich"], check=True)
    from rich.console import Console
    from rich.table import Table
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.panel import Panel
    from rich.logging import RichHandler
    RICH_AVAILABLE = True

# Initialize Rich console
console = Console()

# Configure logging with Rich
logging.basicConfig(
    level=logging.INFO,
    format="%(message)s",
    datefmt="[%X]",
    handlers=[RichHandler(rich_tracebacks=True, markup=True)]
)
logger = logging.getLogger("tenant-discovery")

# Constants
DEFAULT_RDS_SECRET_NAME = "production/rds/credentials"

# Helper functions for enhanced terminal output
def print_header(title: str) -> None:
    """Print a formatted header."""
    console.print(Panel(f"[bold blue]{title}[/bold blue]", expand=False))

def print_step(step: str, description: str) -> None:
    """Print a formatted step."""
    console.print(f"[bold cyan]STEP {step}:[/bold cyan] {description}")

def print_success(message: str) -> None:
    """Print a success message."""
    console.print(f"[bold green]✓ SUCCESS:[/bold green] {message}")

def print_warning(message: str) -> None:
    """Print a warning message."""
    console.print(f"[bold yellow]⚠ WARNING:[/bold yellow] {message}")

def print_error(message: str) -> None:
    """Print an error message."""
    console.print(f"[bold red]✗ ERROR:[/bold red] {message}")

def print_info(message: str) -> None:
    """Print an info message."""
    console.print(f"[bold white]ℹ INFO:[/bold white] {message}")

def create_status_table(title: str, headers: List[str]) -> Table:
    """Create a Rich table for status display."""
    table = Table(title=title)
    for header in headers:
        table.add_column(header, style="cyan")
    return table

def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result."""
    print_info(f"Running command: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=check,
            text=True,
            capture_output=True
        )
        if result.returncode == 0:
            if result.stdout.strip():
                print_info(f"Command output: {result.stdout.strip()}")
        else:
            print_error(f"Command failed with exit code {result.returncode}")
            print_error(f"Error output: {result.stderr.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        print_error(f"Command execution failed: {e}")
        raise

def get_database_connection_info() -> Dict[str, str]:
    """Get database connection information from AWS Secrets Manager."""
    try:
        if not get_rds_credentials:
            print_error("Security modules not available. Cannot retrieve RDS credentials.")
            return None
        
        rds_credentials = get_rds_credentials(DEFAULT_RDS_SECRET_NAME)
        return {
            'host': rds_credentials['host'],
            'port': rds_credentials['port'],
            'user': rds_credentials['username'],
            'password': rds_credentials['password'],
            'database': 'architrave'
        }
    except Exception as e:
        print_error(f"Failed to retrieve RDS credentials: {e}")
        return None

def create_database_bastion_pod(pod_name: str) -> str:
    """Create a bastion pod for database operations."""
    bastion_yaml = f"""
apiVersion: v1
kind: Pod
metadata:
  name: {pod_name}
  namespace: default
  annotations:
    sidecar.istio.io/inject: "false"
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 999
    runAsGroup: 999
    fsGroup: 999
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: python
    image: python:3.11-slim
    command: ["sleep", "infinity"]
    securityContext:
      allowPrivilegeEscalation: false
      runAsNonRoot: true
      runAsUser: 999
      runAsGroup: 999
      readOnlyRootFilesystem: false
      capabilities:
        drop:
        - ALL
      seccompProfile:
        type: RuntimeDefault
    resources:
      limits:
        cpu: 200m
        memory: 256Mi
      requests:
        cpu: 100m
        memory: 128Mi
    volumeMounts:
    - name: tmp-volume
      mountPath: /tmp
  volumes:
  - name: tmp-volume
    emptyDir: {{}}
  restartPolicy: Never
"""
    
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write(bastion_yaml)
        bastion_file = f.name
    
    run_command(f"kubectl apply -f {bastion_file}")
    os.unlink(bastion_file)
    
    # Wait for pod to be ready
    run_command(f"kubectl wait --for=condition=ready pod/{pod_name} --timeout=60s")
    
    # Install pymysql in the pod
    run_command(f"kubectl exec {pod_name} -- pip install pymysql")
    
    return pod_name

def find_all_tenants_in_database() -> List[Dict[str, str]]:
    """Find all tenants in the Aurora database."""
    print_step("1", "Finding all tenants in Aurora database")
    
    db_info = get_database_connection_info()
    if not db_info:
        return []
    
    pod_name = f"tenant-discovery-{int(time.time())}"
    
    try:
        # Create bastion pod
        create_database_bastion_pod(pod_name)
        
        # Create discovery script
        discovery_script = f"""
import pymysql
import json
import sys

try:
    conn = pymysql.connect(
        host='{db_info['host']}', 
        port={int(db_info['port'])}, 
        user='{db_info['user']}', 
        password='{db_info['password']}', 
        database='{db_info['database']}', 
        ssl={{'ssl':{{}}}}
    )
    
    tenants = []
    
    with conn.cursor() as cursor:
        # Method 1: Check tenant_config table
        try:
            cursor.execute("SELECT tenant_id, tenant_name, subdomain, status FROM tenant_config")
            tenant_configs = cursor.fetchall()
            for row in tenant_configs:
                tenants.append({{
                    'tenant_id': row[0],
                    'tenant_name': row[1],
                    'subdomain': row[2],
                    'status': row[3],
                    'source': 'tenant_config'
                }})
        except Exception as e:
            print(f"Could not query tenant_config: {{e}}")
        
        # Method 2: Check for tenant-specific data in main tables
        tenant_tables = [
            'users', 'assets', 'documents', 'folders', 'user_roles', 
            'user_groups', 'notifications', 'audit_logs'
        ]
        
        for table in tenant_tables:
            try:
                cursor.execute(f"SELECT DISTINCT tenant_id FROM {{table}} WHERE tenant_id IS NOT NULL AND tenant_id != ''")
                tenant_ids = cursor.fetchall()
                for row in tenant_ids:
                    tenant_id = row[0]
                    # Check if we already have this tenant
                    if not any(t['tenant_id'] == tenant_id for t in tenants):
                        tenants.append({{
                            'tenant_id': tenant_id,
                            'tenant_name': f'Unknown-{{tenant_id}}',
                            'subdomain': f'{{tenant_id}}',
                            'status': 'unknown',
                            'source': f'{{table}}_table'
                        }})
            except Exception as e:
                print(f"Could not query {{table}}: {{e}}")
        
        # Method 3: Check for tenant-specific schemas (if using schema isolation)
        try:
            cursor.execute("SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME LIKE 'tenant_%_db'")
            schemas = cursor.fetchall()
            for row in schemas:
                schema_name = row[0]
                tenant_id = schema_name.replace('tenant_', '').replace('_db', '')
                if not any(t['tenant_id'] == tenant_id for t in tenants):
                    tenants.append({{
                        'tenant_id': tenant_id,
                        'tenant_name': f'Schema-{{tenant_id}}',
                        'subdomain': f'{{tenant_id}}',
                        'status': 'schema_only',
                        'source': 'schema_isolation'
                    }})
        except Exception as e:
            print(f"Could not query schemas: {{e}}")
    
    conn.close()
    
    # Output as JSON
    print(json.dumps(tenants))
    sys.exit(0)
    
except Exception as e:
    print(f"Database discovery failed: {{e}}")
    sys.exit(1)
"""
        
        # Write discovery script to pod
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(discovery_script)
            script_file = f.name
        
        run_command(f"kubectl cp {script_file} {pod_name}:/tmp/discovery.py")
        os.unlink(script_file)
        
        # Run discovery script
        result = run_command(f"kubectl exec {pod_name} -- python /tmp/discovery.py", check=False)
        
        if result.returncode == 0:
            tenants_data = json.loads(result.stdout.strip())
            print_success(f"Found {len(tenants_data)} tenants in database")
            return tenants_data
        else:
            print_error(f"Discovery script failed: {result.stderr}")
            return []
    
    finally:
        # Clean up pod
        run_command(f"kubectl delete pod {pod_name} --ignore-not-found")

def remove_tenant_from_database(tenant_id: str) -> bool:
    """Remove a specific tenant from the Aurora database."""
    print_step("2", f"Removing tenant {tenant_id} from database")
    
    db_info = get_database_connection_info()
    if not db_info:
        return False
    
    pod_name = f"tenant-removal-{tenant_id}-{int(time.time())}"
    
    try:
        # Create bastion pod
        create_database_bastion_pod(pod_name)
        
        # List of tenant-specific tables to clean
        tenant_tables = [
            'users', 'user_roles', 'user2role', 'user2current_role', 'user_groups', 'user_logins',
            'assets', 'assets_dqa_workflows', 'assets_responsible_users', 'documents', 'downloads',
            'notifications', 'notifications_history', 'notifications_to_create', 'tenant_config', 'tenants',
            'onboardings', 'onboarding_report_operations', 'operation_locks', 'portfolios', 'portfolios_assets',
            'qa_answers', 'qa_clearers', 'qa_dispatch_clearers', 'qa_dispatch_experts', 'qa_experts', 'qa_hints',
            'qa_processes', 'qa_processes_assets', 'qa_questioners', 'qa_questions', 'qa_sequences', 'qa_spectators',
            'queue_jobs', 'reference_data', 'report_downloads', 'report_operations', 'staged_documents',
            'folders', 'audit_logs', 'settings', 'permissions', 'sessions', 'file_uploads', 
            'email_templates', 'system_logs'
        ]
        
        # Create removal script
        removal_script = f"""
import pymysql
import sys

errors = []
tables = {tenant_tables}
tenant_id = '{tenant_id}'

try:
    conn = pymysql.connect(
        host='{db_info['host']}', 
        port={int(db_info['port'])}, 
        user='{db_info['user']}', 
        password='{db_info['password']}', 
        database='{db_info['database']}', 
        ssl={{'ssl':{{}}}}
    )
    
    with conn.cursor() as cursor:
        # First, check if tenant exists in tenant_config
        cursor.execute("SELECT tenant_id FROM tenant_config WHERE tenant_id = %s", (tenant_id,))
        if cursor.fetchone():
            print(f"Found tenant {{tenant_id}} in tenant_config table")
        else:
            print(f"Tenant {{tenant_id}} not found in tenant_config table")
        
        # Delete tenant data from all tables
        for table in tables:
            try:
                cursor.execute(f"DELETE FROM {{table}} WHERE tenant_id = %s", (tenant_id,))
                deleted_count = cursor.rowcount
                if deleted_count > 0:
                    print(f"Deleted {{deleted_count}} records from {{table}}")
                else:
                    print(f"No records found in {{table}} for tenant {{tenant_id}}")
            except Exception as e:
                print(f"Could not delete from {{table}}: {{e}}")
                errors.append(str(e))
        
        # Also try to delete from tenant_config table
        try:
            cursor.execute("DELETE FROM tenant_config WHERE tenant_id = %s", (tenant_id,))
            deleted_count = cursor.rowcount
            if deleted_count > 0:
                print(f"Deleted {{deleted_count}} records from tenant_config")
        except Exception as e:
            print(f"Could not delete from tenant_config: {{e}}")
            errors.append(str(e))
        
        # Commit all changes
        conn.commit()
    
    conn.close()
    
    if errors:
        print(f"Some tables could not be cleaned: {{errors}}")
        sys.exit(1)
    
    print(f'All tenant data deleted from shared architrave database for tenant {{tenant_id}}')
    sys.exit(0)
    
except Exception as e:
    print(f'Database removal failed: {{e}}')
    sys.exit(2)
"""
        
        # Write removal script to pod
        with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
            f.write(removal_script)
            script_file = f.name
        
        run_command(f"kubectl cp {script_file} {pod_name}:/tmp/removal.py")
        os.unlink(script_file)
        
        # Run removal script
        result = run_command(f"kubectl exec {pod_name} -- python /tmp/removal.py", check=False)
        
        if result.returncode == 0:
            print_success(f"Successfully removed tenant {tenant_id} from database")
            return True
        else:
            print_error(f"Removal script failed: {result.stdout}\n{result.stderr}")
            return False
    
    finally:
        # Clean up pod
        run_command(f"kubectl delete pod {pod_name} --ignore-not-found")

def display_tenants_table(tenants: List[Dict[str, str]]) -> None:
    """Display tenants in a formatted table."""
    if not tenants:
        print_warning("No tenants found in database")
        return
    
    table = create_status_table("Tenants Found in Aurora Database", [
        "Tenant ID", "Tenant Name", "Subdomain", "Status", "Source"
    ])
    
    for tenant in tenants:
        table.add_row(
            tenant.get('tenant_id', 'N/A'),
            tenant.get('tenant_name', 'N/A'),
            tenant.get('subdomain', 'N/A'),
            tenant.get('status', 'N/A'),
            tenant.get('source', 'N/A')
        )
    
    console.print(table)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Find and remove all tenants from Aurora database")
    parser.add_argument("--discover-only", action="store_true", 
                       help="Only discover and list tenants, don't remove them")
    parser.add_argument("--remove-all", action="store_true", 
                       help="Remove all discovered tenants")
    parser.add_argument("--tenant-id", type=str, 
                       help="Remove a specific tenant by ID")
    parser.add_argument("--confirm", action="store_true", 
                       help="Skip confirmation prompts")
    
    args = parser.parse_args()
    
    print_header("Tenant Database Discovery and Removal Tool")
    
    # Discover tenants
    tenants = find_all_tenants_in_database()
    
    if not tenants:
        print_warning("No tenants found in database")
        return
    
    # Display tenants
    display_tenants_table(tenants)
    
    if args.discover_only:
        print_info("Discovery mode only. No tenants will be removed.")
        return
    
    # Handle removal
    if args.tenant_id:
        # Remove specific tenant
        if any(t['tenant_id'] == args.tenant_id for t in tenants):
            if args.confirm or input(f"Remove tenant {args.tenant_id}? (y/N): ").lower() == 'y':
                remove_tenant_from_database(args.tenant_id)
            else:
                print_info("Operation cancelled.")
        else:
            print_error(f"Tenant {args.tenant_id} not found in database")
    
    elif args.remove_all:
        # Remove all tenants
        tenant_ids = [t['tenant_id'] for t in tenants]
        
        if args.confirm or input(f"Remove all {len(tenant_ids)} tenants? (y/N): ").lower() == 'y':
            print_step("3", f"Removing all {len(tenant_ids)} tenants from database")
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                BarColumn(),
                TimeElapsedColumn(),
                console=console
            ) as progress:
                task = progress.add_task("Removing tenants...", total=len(tenant_ids))
                
                for tenant_id in tenant_ids:
                    progress.update(task, description=f"Removing tenant {tenant_id}...")
                    success = remove_tenant_from_database(tenant_id)
                    if success:
                        print_success(f"Removed tenant {tenant_id}")
                    else:
                        print_error(f"Failed to remove tenant {tenant_id}")
                    progress.advance(task)
            
            print_success(f"Completed removal of {len(tenant_ids)} tenants")
        else:
            print_info("Operation cancelled.")
    
    else:
        print_info("Use --discover-only to only list tenants, --remove-all to remove all tenants, or --tenant-id <id> to remove a specific tenant")

if __name__ == "__main__":
    main() 