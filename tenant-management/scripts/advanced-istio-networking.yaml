# Advanced Istio Networking Configuration for Tenant Management
# This file contains advanced traffic management, security, and observability features

---
# Enhanced VirtualService with Advanced Routing
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-advanced-vs
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    networking.architrave.io/type: "advanced"
spec:
  hosts:
  - "TENANT_ID_PLACEHOLDER.architrave-assets.com"
  gateways:
  - istio-system/tenant-gateway
  http:
  # API Routes with Rate Limiting
  - match:
    - uri:
        prefix: "/api/"
    route:
    - destination:
        host: TENANT_ID_PLACEHOLDER-backend
        port:
          number: 80
    timeout: 30s
    retries:
      attempts: 3
      perTryTimeout: 10s
      retryOn: "gateway-error,connect-failure,refused-stream"
    fault:
      delay:
        percentage:
          value: 0.1
        fixedDelay: 5s
    headers:
      request:
        set:
          x-tenant-id: "TENANT_ID_PLACEHOLDER"
          x-request-id: "%REQ(x-request-id)%"
  
  # Frontend Routes with Canary Deployment Support
  - match:
    - uri:
        prefix: "/"
    route:
    - destination:
        host: TENANT_ID_PLACEHOLDER-frontend
        port:
          number: 80
        subset: stable
      weight: 90
    - destination:
        host: TENANT_ID_PLACEHOLDER-frontend
        port:
          number: 80
        subset: canary
      weight: 10
    timeout: 15s
    headers:
      request:
        set:
          x-tenant-id: "TENANT_ID_PLACEHOLDER"

---
# DestinationRule with Circuit Breaker and Load Balancing
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: tenant-advanced-dr
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    networking.architrave.io/type: "advanced"
spec:
  host: TENANT_ID_PLACEHOLDER-backend
  trafficPolicy:
    loadBalancer:
      simple: LEAST_CONN
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: 30s
        tcpKeepalive:
          time: 7200s
          interval: 75s
      http:
        http1MaxPendingRequests: 50
        http2MaxRequests: 100
        maxRequestsPerConnection: 10
        maxRetries: 3
    outlierDetection:
      consecutiveGatewayErrors: 5
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 50
      minHealthPercent: 30
  subsets:
  - name: stable
    labels:
      version: stable
  - name: canary
    labels:
      version: canary

---
# Frontend DestinationRule
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: tenant-frontend-dr
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
spec:
  host: TENANT_ID_PLACEHOLDER-frontend
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    connectionPool:
      tcp:
        maxConnections: 50
        connectTimeout: 10s
      http:
        http1MaxPendingRequests: 25
        maxRequestsPerConnection: 5
  subsets:
  - name: stable
    labels:
      version: stable
  - name: canary
    labels:
      version: canary

---
# Istio Security Policies
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: tenant-mtls
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    security.architrave.io/type: "mtls"
spec:
  mtls:
    mode: STRICT

---
# Authorization Policy - Deny All by Default
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-deny-all
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    security.architrave.io/type: "deny-all"
spec:
  action: DENY
  rules:
  - from:
    - source:
        notNamespaces: ["TENANT_NAMESPACE_PLACEHOLDER", "istio-system", "monitoring"]

---
# Authorization Policy - Allow Frontend Access
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-allow-frontend
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    security.architrave.io/type: "allow-frontend"
spec:
  action: ALLOW
  selector:
    matchLabels:
      app: TENANT_ID_PLACEHOLDER-frontend
  rules:
  - from:
    - source:
        namespaces: ["istio-system"]
  - to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"]

---
# Authorization Policy - Allow Backend API Access
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-allow-backend
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    security.architrave.io/type: "allow-backend"
spec:
  action: ALLOW
  selector:
    matchLabels:
      app: TENANT_ID_PLACEHOLDER-backend
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/TENANT_NAMESPACE_PLACEHOLDER/sa/default"]
    - source:
        namespaces: ["istio-system"]
  - to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        paths: ["/health*", "/api/*", "/metrics"]

---
# Telemetry Configuration for Enhanced Observability
apiVersion: telemetry.istio.io/v1alpha1
kind: Telemetry
metadata:
  name: tenant-telemetry
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
spec:
  metrics:
  - providers:
    - name: prometheus
  - overrides:
    - match:
        metric: ALL_METRICS
      tagOverrides:
        tenant_id:
          value: "TENANT_ID_PLACEHOLDER"
        tenant_namespace:
          value: "TENANT_NAMESPACE_PLACEHOLDER"
  accessLogging:
  - providers:
    - name: otel
