apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: testautogo17-ingress
  namespace: tenant-testautogo17
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-central-1:545009857703:certificate/8d306c2f-62e2-42f4-a941-562ce65cda32
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
spec:
  ingressClassName: alb
  rules:
  - host: testautogo17.architrave-assets.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: testautogo17-frontend-service
            port:
              number: 80 