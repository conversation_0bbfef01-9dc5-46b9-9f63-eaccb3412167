#!/usr/bin/env python3
"""
Simple Tenant Discovery and Removal <PERSON>
==========================================
This script uses existing database pods to find and remove tenants from Aurora.
"""

import argparse
import json
import subprocess
import sys
from typing import List, Dict

def run_command(command: str, check: bool = True) -> subprocess.CompletedProcess:
    """Run a shell command and return the result."""
    print(f"Running: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            check=check,
            text=True,
            capture_output=True
        )
        if result.returncode == 0:
            if result.stdout.strip():
                print(f"Output: {result.stdout.strip()}")
        else:
            print(f"Error: {result.stderr.strip()}")
        return result
    except subprocess.CalledProcessError as e:
        print(f"Command failed: {e}")
        raise

def find_available_db_pod() -> str:
    """Find an available database pod to use."""
    result = run_command("kubectl get pods --all-namespaces | grep -E 'db-backup|mysql' | grep Running | head -1 | awk '{print $2}'")
    if result.returncode == 0 and result.stdout.strip():
        return result.stdout.strip()
    return None

def discover_tenants(pod_name: str) -> List[Dict[str, str]]:
    """Discover all tenants in the database using the specified pod."""
    print(f"Discovering tenants using pod: {pod_name}")
    
    # Database connection details
    db_host = "production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
    db_port = "3306"
    db_user = "admin"
    db_name = "architrave"
    
    # Create discovery script
    discovery_script = """
import pymysql
import json
import sys

try:
    conn = pymysql.connect(
        host='production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com', 
        port=3306, 
        user='admin', 
        password='&BZzY_<AK(=a*UhZ', 
        database='architrave', 
        ssl={'ssl':{}}
    )
    
    tenants = []
    
    with conn.cursor() as cursor:
        # Method 1: Check tenant_config table
        try:
            cursor.execute("SELECT tenant_id, tenant_name, subdomain, status FROM tenant_config")
            tenant_configs = cursor.fetchall()
            for row in tenant_configs:
                tenants.append({
                    'tenant_id': row[0],
                    'tenant_name': row[1],
                    'subdomain': row[2],
                    'status': row[3],
                    'source': 'tenant_config'
                })
        except Exception as e:
            print(f"Could not query tenant_config: {e}")
        
        # Method 2: Check for tenant-specific data in main tables
        tenant_tables = [
            'users', 'assets', 'documents', 'folders', 'user_roles', 
            'user_groups', 'notifications', 'audit_logs'
        ]
        
        for table in tenant_tables:
            try:
                cursor.execute(f"SELECT DISTINCT tenant_id FROM {table} WHERE tenant_id IS NOT NULL AND tenant_id != ''")
                tenant_ids = cursor.fetchall()
                for row in tenant_ids:
                    tenant_id = row[0]
                    # Check if we already have this tenant
                    if not any(t['tenant_id'] == tenant_id for t in tenants):
                        tenants.append({
                            'tenant_id': tenant_id,
                            'tenant_name': f'Unknown-{tenant_id}',
                            'subdomain': f'{tenant_id}',
                            'status': 'unknown',
                            'source': f'{table}_table'
                        })
            except Exception as e:
                print(f"Could not query {table}: {e}")
        
        # Method 3: Check for tenant-specific schemas
        try:
            cursor.execute("SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME LIKE 'tenant_%_db'")
            schemas = cursor.fetchall()
            for row in schemas:
                schema_name = row[0]
                tenant_id = schema_name.replace('tenant_', '').replace('_db', '')
                if not any(t['tenant_id'] == tenant_id for t in tenants):
                    tenants.append({
                        'tenant_id': tenant_id,
                        'tenant_name': f'Schema-{tenant_id}',
                        'subdomain': f'{tenant_id}',
                        'status': 'schema_only',
                        'source': 'schema_isolation'
                    })
        except Exception as e:
            print(f"Could not query schemas: {e}")
    
    conn.close()
    
    # Output as JSON
    print(json.dumps(tenants))
    sys.exit(0)
    
except Exception as e:
    print(f"Database discovery failed: {e}")
    sys.exit(1)
"""
    
    # Write script to temporary file
    import tempfile
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write(discovery_script)
        script_file = f.name
    
    try:
        # Copy script to pod
        run_command(f"kubectl cp {script_file} {pod_name}:/tmp/discovery.py")
        
        # Run discovery script
        result = run_command(f"kubectl exec {pod_name} -- python /tmp/discovery.py", check=False)
        
        if result.returncode == 0:
            tenants_data = json.loads(result.stdout.strip())
            print(f"Found {len(tenants_data)} tenants in database")
            return tenants_data
        else:
            print(f"Discovery failed: {result.stderr}")
            return []
    
    finally:
        import os
        os.unlink(script_file)

def remove_tenant(pod_name: str, tenant_id: str) -> bool:
    """Remove a specific tenant from the database."""
    print(f"Removing tenant {tenant_id} using pod: {pod_name}")
    
    # List of tenant-specific tables to clean
    tenant_tables = [
        'users', 'user_roles', 'user2role', 'user2current_role', 'user_groups', 'user_logins',
        'assets', 'assets_dqa_workflows', 'assets_responsible_users', 'documents', 'downloads',
        'notifications', 'notifications_history', 'notifications_to_create', 'tenant_config', 'tenants',
        'onboardings', 'onboarding_report_operations', 'operation_locks', 'portfolios', 'portfolios_assets',
        'qa_answers', 'qa_clearers', 'qa_dispatch_clearers', 'qa_dispatch_experts', 'qa_experts', 'qa_hints',
        'qa_processes', 'qa_processes_assets', 'qa_questioners', 'qa_questions', 'qa_sequences', 'qa_spectators',
        'queue_jobs', 'reference_data', 'report_downloads', 'report_operations', 'staged_documents',
        'folders', 'audit_logs', 'settings', 'permissions', 'sessions', 'file_uploads', 
        'email_templates', 'system_logs'
    ]
    
    # Create removal script
    removal_script = f"""
import pymysql
import sys

errors = []
tables = {tenant_tables}
tenant_id = '{tenant_id}'

try:
    conn = pymysql.connect(
        host='production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com', 
        port=3306, 
        user='admin', 
        password='&BZzY_<AK(=a*UhZ', 
        database='architrave', 
        ssl={{'ssl':{{}}}}
    )
    
    with conn.cursor() as cursor:
        # First, check if tenant exists in tenant_config
        cursor.execute("SELECT tenant_id FROM tenant_config WHERE tenant_id = %s", (tenant_id,))
        if cursor.fetchone():
            print(f"Found tenant {{tenant_id}} in tenant_config table")
        else:
            print(f"Tenant {{tenant_id}} not found in tenant_config table")
        
        # Delete tenant data from all tables
        for table in tables:
            try:
                cursor.execute(f"DELETE FROM {{table}} WHERE tenant_id = %s", (tenant_id,))
                deleted_count = cursor.rowcount
                if deleted_count > 0:
                    print(f"Deleted {{deleted_count}} records from {{table}}")
                else:
                    print(f"No records found in {{table}} for tenant {{tenant_id}}")
            except Exception as e:
                print(f"Could not delete from {{table}}: {{e}}")
                errors.append(str(e))
        
        # Also try to delete from tenant_config table
        try:
            cursor.execute("DELETE FROM tenant_config WHERE tenant_id = %s", (tenant_id,))
            deleted_count = cursor.rowcount
            if deleted_count > 0:
                print(f"Deleted {{deleted_count}} records from tenant_config")
        except Exception as e:
            print(f"Could not delete from tenant_config: {{e}}")
            errors.append(str(e))
        
        # Commit all changes
        conn.commit()
    
    conn.close()
    
    if errors:
        print(f"Some tables could not be cleaned: {{errors}}")
        sys.exit(1)
    
    print(f'All tenant data deleted from shared architrave database for tenant {{tenant_id}}')
    sys.exit(0)
    
except Exception as e:
    print(f'Database removal failed: {{e}}')
    sys.exit(2)
"""
    
    # Write script to temporary file
    import tempfile
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write(removal_script)
        script_file = f.name
    
    try:
        # Copy script to pod
        run_command(f"kubectl cp {script_file} {pod_name}:/tmp/removal.py")
        
        # Run removal script
        result = run_command(f"kubectl exec {pod_name} -- python /tmp/removal.py", check=False)
        
        if result.returncode == 0:
            print(f"Successfully removed tenant {tenant_id} from database")
            return True
        else:
            print(f"Removal failed: {result.stdout}\n{result.stderr}")
            return False
    
    finally:
        import os
        os.unlink(script_file)

def display_tenants(tenants: List[Dict[str, str]]) -> None:
    """Display tenants in a simple format."""
    if not tenants:
        print("No tenants found in database")
        return
    
    print(f"\nFound {len(tenants)} tenants in database:")
    print("-" * 80)
    print(f"{'Tenant ID':<20} {'Tenant Name':<25} {'Subdomain':<15} {'Status':<10} {'Source':<15}")
    print("-" * 80)
    
    for tenant in tenants:
        print(f"{tenant.get('tenant_id', 'N/A'):<20} "
              f"{tenant.get('tenant_name', 'N/A')[:24]:<25} "
              f"{tenant.get('subdomain', 'N/A')[:14]:<15} "
              f"{tenant.get('status', 'N/A')[:9]:<10} "
              f"{tenant.get('source', 'N/A')[:14]:<15}")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Simple tenant discovery and removal")
    parser.add_argument("--discover-only", action="store_true", 
                       help="Only discover and list tenants, don't remove them")
    parser.add_argument("--remove-all", action="store_true", 
                       help="Remove all discovered tenants")
    parser.add_argument("--tenant-id", type=str, 
                       help="Remove a specific tenant by ID")
    parser.add_argument("--pod", type=str, 
                       help="Use specific pod name for database operations")
    
    args = parser.parse_args()
    
    print("Simple Tenant Discovery and Removal Tool")
    print("=" * 50)
    
    # Find or use specified pod
    if args.pod:
        pod_name = args.pod
    else:
        pod_name = find_available_db_pod()
    
    if not pod_name:
        print("Error: No available database pod found")
        return
    
    print(f"Using pod: {pod_name}")
    
    # Discover tenants
    tenants = discover_tenants(pod_name)
    
    if not tenants:
        print("No tenants found in database")
        return
    
    # Display tenants
    display_tenants(tenants)
    
    if args.discover_only:
        print("\nDiscovery mode only. No tenants will be removed.")
        return
    
    # Handle removal
    if args.tenant_id:
        # Remove specific tenant
        if any(t['tenant_id'] == args.tenant_id for t in tenants):
            if input(f"\nRemove tenant {args.tenant_id}? (y/N): ").lower() == 'y':
                remove_tenant(pod_name, args.tenant_id)
            else:
                print("Operation cancelled.")
        else:
            print(f"Error: Tenant {args.tenant_id} not found in database")
    
    elif args.remove_all:
        # Remove all tenants
        tenant_ids = [t['tenant_id'] for t in tenants]
        
        if input(f"\nRemove all {len(tenant_ids)} tenants? (y/N): ").lower() == 'y':
            print(f"\nRemoving all {len(tenant_ids)} tenants from database...")
            
            for i, tenant_id in enumerate(tenant_ids, 1):
                print(f"\n[{i}/{len(tenant_ids)}] Removing tenant {tenant_id}...")
                success = remove_tenant(pod_name, tenant_id)
                if success:
                    print(f"✓ Removed tenant {tenant_id}")
                else:
                    print(f"✗ Failed to remove tenant {tenant_id}")
            
            print(f"\nCompleted removal of {len(tenant_ids)} tenants")
        else:
            print("Operation cancelled.")
    
    else:
        print("\nUse --discover-only to only list tenants, --remove-all to remove all tenants, or --tenant-id <id> to remove a specific tenant")

if __name__ == "__main__":
    main() 