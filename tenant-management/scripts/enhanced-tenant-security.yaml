# Enhanced Security Policies for Tenant Onboarding
# This file contains comprehensive security configurations for tenant isolation

---
# Network Policy Template for Tenant Isolation
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: tenant-isolation-policy
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    security.architrave.io/policy: "isolation"
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  # Allow ingress from Istio ingress gateway
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: istio-system
    - podSelector:
        matchLabels:
          app: istio-proxy
  
  # Allow ingress from monitoring namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9090
  
  # Allow internal communication within tenant namespace
  - from:
    - namespaceSelector:
        matchLabels:
          name: TENANT_NAMESPACE_PLACEHOLDER
  
  # Egress rules
  egress:
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # Allow HTTPS to external services
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  # Allow access to RDS database
  - to:
    - ipBlock:
        cidr: 10.0.0.0/16
    ports:
    - protocol: TCP
      port: 3306
  
  # Allow access to monitoring services
  - to:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 9090
    - protocol: TCP
      port: 3100
    - protocol: TCP
      port: 16686

---
# Pod Security Standards (replaces deprecated PodSecurityPolicy)
apiVersion: v1
kind: Namespace
metadata:
  name: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    pod-security.kubernetes.io/enforce: restricted
    pod-security.kubernetes.io/audit: restricted
    pod-security.kubernetes.io/warn: restricted

---
# RBAC for Tenant Service Account
apiVersion: v1
kind: ServiceAccount
metadata:
  name: tenant-service-account
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  name: tenant-role
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies"]
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: tenant-role-binding
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
subjects:
- kind: ServiceAccount
  name: tenant-service-account
  namespace: TENANT_NAMESPACE_PLACEHOLDER
roleRef:
  kind: Role
  name: tenant-role
  apiGroup: rbac.authorization.k8s.io

---
# Resource Quota for Tenant
apiVersion: v1
kind: ResourceQuota
metadata:
  name: tenant-resource-quota
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
spec:
  hard:
    requests.cpu: "2"
    requests.memory: 4Gi
    limits.cpu: "4"
    limits.memory: 8Gi
    persistentvolumeclaims: "5"
    services: "10"
    secrets: "20"
    configmaps: "20"
    pods: "20"

---
# Limit Range for Tenant Pods
apiVersion: v1
kind: LimitRange
metadata:
  name: tenant-limit-range
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
spec:
  limits:
  - type: Container
    default:
      cpu: 500m
      memory: 512Mi
      ephemeral-storage: 1Gi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
      ephemeral-storage: 100Mi
    max:
      cpu: "2"
      memory: 2Gi
      ephemeral-storage: 2Gi
    min:
      cpu: 50m
      memory: 64Mi
      ephemeral-storage: 100Mi
  - type: Pod
    max:
      cpu: "4"
      memory: 4Gi
