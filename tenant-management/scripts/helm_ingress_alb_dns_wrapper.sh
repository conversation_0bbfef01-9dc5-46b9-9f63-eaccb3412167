#!/bin/bash
set -euo pipefail

TENANT_ID="$1"
NAMESPACE="tenant-$TENANT_ID"
DOMAIN="$2"
ACM_ARN="$3"
BACKEND_IMAGE="$4"
FRONTEND_IMAGE="$5"
HETZNER_API_TOKEN="$6"
CHART_PATH="$(dirname "$0")/../../tenant-chart"

# 1. Helm upgrade/install (no Ingress)
helm upgrade --install "${TENANT_ID}-tenant" "$CHART_PATH" \
  -n "$NAMESPACE" --create-namespace --atomic --wait \
  --set namespace="$NAMESPACE" \
  --set tenantName="$TENANT_ID" \
  --set subdomain="$TENANT_ID" \
  --set acmCertificateArn="$ACM_ARN" \
  --set backendImage="$BACKEND_IMAGE" \
  --set backendPort=8080 \
  --set frontendImage="$FRONTEND_IMAGE" \
  --set frontendPort=80 \
  --set ingress.enabled=false

# 2. Wait for backend pod and service readiness
kubectl wait --for=condition=available --timeout=300s deployment/${TENANT_ID}-backend -n "$NAMESPACE"
kubectl wait --for=condition=ready --timeout=300s pod -l app=${TENANT_ID}-backend -n "$NAMESPACE"
kubectl get svc ${TENANT_ID}-backend-service -n "$NAMESPACE"

# 3. Helm upgrade/install (Ingress only)
helm upgrade --install "${TENANT_ID}-tenant" "$CHART_PATH" \
  -n "$NAMESPACE" --atomic --wait \
  --set namespace="$NAMESPACE" \
  --set tenantName="$TENANT_ID" \
  --set subdomain="$TENANT_ID" \
  --set acmCertificateArn="$ACM_ARN" \
  --set backendImage="$BACKEND_IMAGE" \
  --set backendPort=8080 \
  --set frontendImage="$FRONTEND_IMAGE" \
  --set frontendPort=80 \
  --set ingress.enabled=true

# 4. Poll for ALB DNS
INGRESS_NAME="${TENANT_ID}-ingress"
ALB_DNS=""
TIMEOUT=$((SECONDS+600))
while [ $SECONDS -lt $TIMEOUT ]; do
  ALB_DNS=$(kubectl get ingress "$INGRESS_NAME" -n "$NAMESPACE" -o jsonpath='{.status.loadBalancer.ingress[0].hostname}' 2>/dev/null || true)
  if [[ -n "$ALB_DNS" ]]; then
    echo "ALB DNS assigned: $ALB_DNS"
    break
  fi
  sleep 10
done
if [[ -z "$ALB_DNS" ]]; then
  echo "ERROR: Timeout waiting for ALB DNS assignment" >&2
  exit 1
fi

# 5. Hetzner DNS CNAME creation (calls Go util or Python if available)
SUBDOMAIN="${TENANT_ID}.${DOMAIN}"
if [[ -n "$HETZNER_API_TOKEN" ]]; then
  if command -v python3 &>/dev/null && [[ -f "$(dirname "$0")/hetzner_dns_cname.py" ]]; then
    python3 "$(dirname "$0")/hetzner_dns_cname.py" "$HETZNER_API_TOKEN" "$SUBDOMAIN" "$ALB_DNS"
  else
    echo "Hetzner DNS CNAME: $SUBDOMAIN -> $ALB_DNS (API token present, but no automation script found)"
  fi
else
  echo "Hetzner API token not provided, skipping DNS CNAME creation"
fi 