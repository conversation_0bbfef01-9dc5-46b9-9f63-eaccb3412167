#!/usr/bin/env python3
"""
Comprehensive cleanup and fresh onboarding script for Architrave tenants.
This script will:
1. Clean up all existing tenant resources
2. Run fresh onboarding for multiple tenants
3. Validate everything is working correctly
"""

import os
import sys
import subprocess
import time
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'cleanup_fresh_onboard_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_command(command, check=True):
    """Run a shell command and return the output."""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, check=check)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        if check:
            logger.error(f"Command failed: {command}")
            logger.error(f"Error: {e.stderr}")
            raise
        return e.stdout.strip() if e.stdout else ""

def cleanup_all_tenant_resources():
    """Clean up all existing tenant resources comprehensively."""
    logger.info("🧹 Starting comprehensive cleanup of all tenant resources...")
    
    cleanup_tasks = [
        # 1. Delete all tenant namespaces
        ("Deleting tenant namespaces", "kubectl delete namespaces -l tenant.architrave.io/tenant-id --ignore-not-found"),
        ("Deleting any remaining tenant namespaces", "kubectl get namespaces | grep tenant- | awk '{print $1}' | xargs -r kubectl delete namespace --ignore-not-found"),
        
        # 2. Delete cluster-wide resources
        ("Deleting tenant PVs", "kubectl get pv | grep tenant- | awk '{print $1}' | xargs -r kubectl delete pv --ignore-not-found"),
        ("Deleting tenant storage classes", "kubectl get storageclass | grep s3-sc- | awk '{print $1}' | xargs -r kubectl delete storageclass --ignore-not-found"),
        
        # 3. Delete ALB/Ingress resources
        ("Deleting ALB ingresses", "kubectl get ingress --all-namespaces | grep tenant- | awk '{print $1\" \"$2}' | xargs -r -n2 kubectl delete ingress -n"),
        
        # 4. Clean up any stuck resources
        ("Force cleanup stuck namespaces", "kubectl get namespaces | grep tenant- | grep Terminating | awk '{print $1}' | xargs -r kubectl patch namespace -p '{\"metadata\":{\"finalizers\":[]}}' --type=merge"),
        
        # 5. Wait for cleanup
        ("Waiting for cleanup to complete", "sleep 30"),
    ]
    
    for task_name, command in cleanup_tasks:
        logger.info(f"🔧 {task_name}...")
        try:
            if "sleep" in command:
                time.sleep(30)
            else:
                result = run_command(command, check=False)
                if result:
                    logger.info(f"✅ {task_name} completed: {result}")
                else:
                    logger.info(f"✅ {task_name} completed (no resources found)")
        except Exception as e:
            logger.warning(f"⚠️ {task_name} had issues: {e}")
    
    logger.info("✅ Comprehensive cleanup completed")

def run_fresh_onboarding(tenant_configs):
    """Run fresh onboarding for multiple tenants."""
    logger.info(f"🚀 Starting fresh onboarding for {len(tenant_configs)} tenants...")
    
    onboarding_results = {}
    
    for i, config in enumerate(tenant_configs, 1):
        tenant_id = config['tenant_id']
        tenant_name = config['tenant_name']
        subdomain = config['subdomain']
        
        logger.info(f"📦 [{i}/{len(tenant_configs)}] Onboarding tenant: {tenant_id}")
        
        try:
            # Run onboarding command
            onboard_command = f"""
            cd /Users/<USER>/Projects/new_project/infra-provisioning/tenant-management/scripts && \
            python advanced_tenant_onboard.py \
                --tenant-id {tenant_id} \
                --tenant-name "{tenant_name}" \
                --subdomain {subdomain} \
                --domain architrave-assets.com \
                --environment production \
                --language en
            """
            
            logger.info(f"🔧 Running onboarding for {tenant_id}...")
            start_time = time.time()
            
            result = run_command(onboard_command, check=True)
            
            end_time = time.time()
            duration = end_time - start_time
            
            onboarding_results[tenant_id] = {
                'status': 'SUCCESS',
                'duration': duration,
                'message': f'Onboarded successfully in {duration:.1f}s'
            }
            
            logger.info(f"✅ {tenant_id} onboarded successfully in {duration:.1f}s")
            
            # Wait between onboardings to avoid resource conflicts
            if i < len(tenant_configs):
                logger.info("⏳ Waiting 30s before next onboarding...")
                time.sleep(30)
                
        except Exception as e:
            onboarding_results[tenant_id] = {
                'status': 'FAILED',
                'duration': 0,
                'message': f'Failed: {str(e)}'
            }
            logger.error(f"❌ {tenant_id} onboarding failed: {e}")
    
    return onboarding_results

def validate_onboarding_results(tenant_configs, onboarding_results):
    """Validate that all tenants were onboarded successfully."""
    logger.info("🔍 Validating onboarding results...")
    
    validation_results = {}
    
    for config in tenant_configs:
        tenant_id = config['tenant_id']
        subdomain = config['subdomain']
        
        logger.info(f"🔧 Validating {tenant_id}...")
        
        validation_checks = {
            'namespace': False,
            'pods_running': False,
            'services': False,
            'ingress': False,
            'external_access': False
        }
        
        try:
            # Check namespace
            ns_result = run_command(f"kubectl get namespace tenant-{tenant_id}", check=False)
            if f"tenant-{tenant_id}" in ns_result:
                validation_checks['namespace'] = True
            
            # Check pods
            pods_result = run_command(f"kubectl get pods -n tenant-{tenant_id} --field-selector=status.phase=Running", check=False)
            if "Running" in pods_result:
                validation_checks['pods_running'] = True
            
            # Check services
            svc_result = run_command(f"kubectl get services -n tenant-{tenant_id}", check=False)
            if "webapp" in svc_result:
                validation_checks['services'] = True
            
            # Check ingress
            ing_result = run_command(f"kubectl get ingress -n tenant-{tenant_id}", check=False)
            if f"tenant-{tenant_id}-alb" in ing_result:
                validation_checks['ingress'] = True
                
                # Check external access
                alb_address = run_command(f"kubectl get ingress tenant-{tenant_id}-alb -n tenant-{tenant_id} -o jsonpath='{{.status.loadBalancer.ingress[0].hostname}}'", check=False)
                if alb_address:
                    validation_checks['external_access'] = True
            
            passed_checks = sum(validation_checks.values())
            total_checks = len(validation_checks)
            
            validation_results[tenant_id] = {
                'passed_checks': passed_checks,
                'total_checks': total_checks,
                'checks': validation_checks,
                'success': passed_checks >= 4  # Allow some flexibility
            }
            
            if validation_results[tenant_id]['success']:
                logger.info(f"✅ {tenant_id} validation passed ({passed_checks}/{total_checks})")
            else:
                logger.warning(f"⚠️ {tenant_id} validation failed ({passed_checks}/{total_checks})")
                
        except Exception as e:
            validation_results[tenant_id] = {
                'passed_checks': 0,
                'total_checks': 5,
                'checks': validation_checks,
                'success': False,
                'error': str(e)
            }
            logger.error(f"❌ {tenant_id} validation error: {e}")
    
    return validation_results

def print_final_summary(tenant_configs, onboarding_results, validation_results):
    """Print comprehensive final summary."""
    logger.info("📊 FINAL SUMMARY")
    logger.info("=" * 80)
    
    successful_onboards = sum(1 for r in onboarding_results.values() if r['status'] == 'SUCCESS')
    successful_validations = sum(1 for r in validation_results.values() if r['success'])
    
    logger.info(f"Total tenants: {len(tenant_configs)}")
    logger.info(f"Successful onboardings: {successful_onboards}/{len(tenant_configs)}")
    logger.info(f"Successful validations: {successful_validations}/{len(tenant_configs)}")
    logger.info("")
    
    for config in tenant_configs:
        tenant_id = config['tenant_id']
        subdomain = config['subdomain']
        
        onboard_status = onboarding_results.get(tenant_id, {}).get('status', 'UNKNOWN')
        validation_success = validation_results.get(tenant_id, {}).get('success', False)
        
        status_icon = "✅" if onboard_status == 'SUCCESS' and validation_success else "❌"
        
        logger.info(f"{status_icon} {tenant_id}")
        logger.info(f"   Subdomain: {subdomain}.architrave-assets.com")
        logger.info(f"   Onboarding: {onboard_status}")
        logger.info(f"   Validation: {'PASSED' if validation_success else 'FAILED'}")
        
        if validation_success:
            # Get ALB address if available
            try:
                alb_address = run_command(f"kubectl get ingress tenant-{tenant_id}-alb -n tenant-{tenant_id} -o jsonpath='{{.status.loadBalancer.ingress[0].hostname}}'", check=False)
                if alb_address:
                    logger.info(f"   HTTPS URL: https://{subdomain}.architrave-assets.com")
                    logger.info(f"   ALB Address: {alb_address}")
            except:
                pass
        logger.info("")

def main():
    """Main function to run cleanup and fresh onboarding."""
    logger.info("🚀 Starting Comprehensive Cleanup and Fresh Onboarding")
    logger.info("=" * 80)
    
    # Define tenant configurations for testing
    tenant_configs = [
        {
            'tenant_id': 'demo-tenant',
            'tenant_name': 'Demo Tenant',
            'subdomain': 'demo-tenant'
        },
        {
            'tenant_id': 'test-tenant',
            'tenant_name': 'Test Tenant',
            'subdomain': 'test-tenant'
        },
        {
            'tenant_id': 'staging-tenant',
            'tenant_name': 'Staging Tenant',
            'subdomain': 'staging-tenant'
        }
    ]
    
    try:
        # Step 1: Comprehensive cleanup
        cleanup_all_tenant_resources()
        
        # Step 2: Fresh onboarding
        onboarding_results = run_fresh_onboarding(tenant_configs)
        
        # Step 3: Validation
        validation_results = validate_onboarding_results(tenant_configs, onboarding_results)
        
        # Step 4: Final summary
        print_final_summary(tenant_configs, onboarding_results, validation_results)
        
        # Step 5: Overall result
        successful_count = sum(1 for r in validation_results.values() if r['success'])
        if successful_count == len(tenant_configs):
            logger.info("🎉 ALL TENANTS SUCCESSFULLY ONBOARDED!")
        else:
            logger.warning(f"⚠️ {successful_count}/{len(tenant_configs)} tenants successfully onboarded")
        
    except Exception as e:
        logger.error(f"❌ Script failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
