#!/bin/bash

# MySQL Tenant Discovery and Removal <PERSON>
# =========================================
# This script uses MySQL client directly to find and remove tenants from Aurora database

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database connection details
DB_HOST="production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
DB_PORT="3306"
DB_USER="admin"
DB_PASSWORD="&BZzY_<AK(=a*UhZ"
DB_NAME="architrave"

# Functions
print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

find_available_db_pod() {
    echo "Finding available database pod..."
    POD_NAME=$(kubectl get pods --all-namespaces | grep -E 'db-backup|mysql' | grep Running | head -1 | awk '{print $2}')
    if [ -z "$POD_NAME" ]; then
        print_error "No available database pod found"
        exit 1
    fi
    echo "Using pod: $POD_NAME"
}

run_mysql_query() {
    local pod_name="$1"
    local query="$2"
    local output_file="$3"
    
    echo "Running query: $query"
    
    # Create a temporary SQL file
    cat > /tmp/temp_query.sql << EOF
$query
EOF
    
    # Copy to pod and execute
    kubectl cp /tmp/temp_query.sql "$pod_name:/tmp/temp_query.sql"
    kubectl exec "$pod_name" -- mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < /tmp/temp_query.sql > "$output_file" 2>/dev/null || true
    
    # Clean up
    rm -f /tmp/temp_query.sql
}

discover_tenants() {
    local pod_name="$1"
    echo "Discovering tenants using pod: $pod_name"
    
    # Create discovery queries
    local discovery_query="
-- Method 1: Check tenant_config table
SELECT 'tenant_config' as source, tenant_id, tenant_name, subdomain, status 
FROM tenant_config 
WHERE tenant_id IS NOT NULL AND tenant_id != '';

-- Method 2: Check for tenant-specific data in main tables
SELECT 'users_table' as source, tenant_id, CONCAT('Unknown-', tenant_id) as tenant_name, tenant_id as subdomain, 'unknown' as status 
FROM users 
WHERE tenant_id IS NOT NULL AND tenant_id != '' 
GROUP BY tenant_id;

SELECT 'assets_table' as source, tenant_id, CONCAT('Unknown-', tenant_id) as tenant_name, tenant_id as subdomain, 'unknown' as status 
FROM assets 
WHERE tenant_id IS NOT NULL AND tenant_id != '' 
GROUP BY tenant_id;

SELECT 'documents_table' as source, tenant_id, CONCAT('Unknown-', tenant_id) as tenant_name, tenant_id as subdomain, 'unknown' as status 
FROM documents 
WHERE tenant_id IS NOT NULL AND tenant_id != '' 
GROUP BY tenant_id;

SELECT 'folders_table' as source, tenant_id, CONCAT('Unknown-', tenant_id) as tenant_name, tenant_id as subdomain, 'unknown' as status 
FROM folders 
WHERE tenant_id IS NOT NULL AND tenant_id != '' 
GROUP BY tenant_id;

SELECT 'user_roles_table' as source, tenant_id, CONCAT('Unknown-', tenant_id) as tenant_name, tenant_id as subdomain, 'unknown' as status 
FROM user_roles 
WHERE tenant_id IS NOT NULL AND tenant_id != '' 
GROUP BY tenant_id;

SELECT 'user_groups_table' as source, tenant_id, CONCAT('Unknown-', tenant_id) as tenant_name, tenant_id as subdomain, 'unknown' as status 
FROM user_groups 
WHERE tenant_id IS NOT NULL AND tenant_id != '' 
GROUP BY tenant_id;

SELECT 'notifications_table' as source, tenant_id, CONCAT('Unknown-', tenant_id) as tenant_name, tenant_id as subdomain, 'unknown' as status 
FROM notifications 
WHERE tenant_id IS NOT NULL AND tenant_id != '' 
GROUP BY tenant_id;

SELECT 'audit_logs_table' as source, tenant_id, CONCAT('Unknown-', tenant_id) as tenant_name, tenant_id as subdomain, 'unknown' as status 
FROM audit_logs 
WHERE tenant_id IS NOT NULL AND tenant_id != '' 
GROUP BY tenant_id;

-- Method 3: Check for tenant-specific schemas
SELECT 'schema_isolation' as source, 
       REPLACE(REPLACE(SCHEMA_NAME, 'tenant_', ''), '_db', '') as tenant_id,
       CONCAT('Schema-', REPLACE(REPLACE(SCHEMA_NAME, 'tenant_', ''), '_db', '')) as tenant_name,
       REPLACE(REPLACE(SCHEMA_NAME, 'tenant_', ''), '_db', '') as subdomain,
       'schema_only' as status
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME LIKE 'tenant_%_db';
"
    
    # Run discovery query
    run_mysql_query "$pod_name" "$discovery_query" "/tmp/tenant_discovery.txt"
    
    # Process results
    if [ -f "/tmp/tenant_discovery.txt" ]; then
        # Extract unique tenants
        cat /tmp/tenant_discovery.txt | grep -v "source" | awk -F'\t' '{
            if ($2 != "" && $2 != "NULL") {
                print $2 "\t" $3 "\t" $4 "\t" $5 "\t" $1
            }
        }' | sort -u > /tmp/unique_tenants.txt
        
        # Count tenants
        local tenant_count=$(wc -l < /tmp/unique_tenants.txt)
        print_success "Found $tenant_count tenants in database"
        
        # Display tenants
        if [ "$tenant_count" -gt 0 ]; then
            echo ""
            echo "Tenants found in database:"
            echo "--------------------------------------------------------------------------------"
            printf "%-20s %-25s %-15s %-10s %-15s\n" "Tenant ID" "Tenant Name" "Subdomain" "Status" "Source"
            echo "--------------------------------------------------------------------------------"
            
            while IFS=$'\t' read -r tenant_id tenant_name subdomain status source; do
                printf "%-20s %-25s %-15s %-10s %-15s\n" \
                    "$tenant_id" \
                    "${tenant_name:0:24}" \
                    "${subdomain:0:14}" \
                    "${status:0:9}" \
                    "${source:0:14}"
            done < /tmp/unique_tenants.txt
        fi
        
        # Clean up
        rm -f /tmp/tenant_discovery.txt /tmp/unique_tenants.txt
    else
        print_warning "No tenants found in database"
    fi
}

remove_tenant() {
    local pod_name="$1"
    local tenant_id="$2"
    
    echo "Removing tenant $tenant_id using pod: $pod_name"
    
    # List of tenant-specific tables to clean
    local tenant_tables=(
        "users" "user_roles" "user2role" "user2current_role" "user_groups" "user_logins"
        "assets" "assets_dqa_workflows" "assets_responsible_users" "documents" "downloads"
        "notifications" "notifications_history" "notifications_to_create" "tenant_config" "tenants"
        "onboardings" "onboarding_report_operations" "operation_locks" "portfolios" "portfolios_assets"
        "qa_answers" "qa_clearers" "qa_dispatch_clearers" "qa_dispatch_experts" "qa_experts" "qa_hints"
        "qa_processes" "qa_processes_assets" "qa_questioners" "qa_questions" "qa_sequences" "qa_spectators"
        "queue_jobs" "reference_data" "report_downloads" "report_operations" "staged_documents"
        "folders" "audit_logs" "settings" "permissions" "sessions" "file_uploads" 
        "email_templates" "system_logs"
    )
    
    # Create removal query
    local removal_query="START TRANSACTION;"
    
    # Add DELETE statements for each table
    for table in "${tenant_tables[@]}"; do
        removal_query="$removal_query
DELETE FROM $table WHERE tenant_id = '$tenant_id';"
    done
    
    removal_query="$removal_query
COMMIT;"
    
    # Run removal query
    run_mysql_query "$pod_name" "$removal_query" "/tmp/tenant_removal.txt"
    
    # Check if removal was successful
    if [ -f "/tmp/tenant_removal.txt" ]; then
        local error_count=$(grep -c "ERROR\|error" /tmp/tenant_removal.txt || echo "0")
        if [ "$error_count" -eq 0 ]; then
            print_success "Successfully removed tenant $tenant_id from database"
            rm -f /tmp/tenant_removal.txt
            return 0
        else
            print_error "Failed to remove tenant $tenant_id: $(cat /tmp/tenant_removal.txt)"
            rm -f /tmp/tenant_removal.txt
            return 1
        fi
    else
        print_error "Failed to remove tenant $tenant_id"
        return 1
    fi
}

get_tenant_list() {
    local pod_name="$1"
    
    # Query to get list of tenants
    local list_query="
SELECT DISTINCT tenant_id 
FROM (
    SELECT tenant_id FROM tenant_config WHERE tenant_id IS NOT NULL AND tenant_id != ''
    UNION
    SELECT tenant_id FROM users WHERE tenant_id IS NOT NULL AND tenant_id != ''
    UNION
    SELECT tenant_id FROM assets WHERE tenant_id IS NOT NULL AND tenant_id != ''
    UNION
    SELECT tenant_id FROM documents WHERE tenant_id IS NOT NULL AND tenant_id != ''
    UNION
    SELECT tenant_id FROM folders WHERE tenant_id IS NOT NULL AND tenant_id != ''
    UNION
    SELECT tenant_id FROM user_roles WHERE tenant_id IS NOT NULL AND tenant_id != ''
    UNION
    SELECT tenant_id FROM user_groups WHERE tenant_id IS NOT NULL AND tenant_id != ''
    UNION
    SELECT tenant_id FROM notifications WHERE tenant_id IS NOT NULL AND tenant_id != ''
    UNION
    SELECT tenant_id FROM audit_logs WHERE tenant_id IS NOT NULL AND tenant_id != ''
) as all_tenants
WHERE tenant_id IS NOT NULL AND tenant_id != '';
"
    
    run_mysql_query "$pod_name" "$list_query" "/tmp/tenant_list.txt"
    
    if [ -f "/tmp/tenant_list.txt" ]; then
        cat /tmp/tenant_list.txt | grep -v "tenant_id" | grep -v "^$" | sort -u
        rm -f /tmp/tenant_list.txt
    fi
}

main() {
    print_header "MySQL Tenant Discovery and Removal Tool"
    
    # Parse arguments
    DISCOVER_ONLY=false
    REMOVE_ALL=false
    TENANT_ID=""
    POD_NAME=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --discover-only)
                DISCOVER_ONLY=true
                shift
                ;;
            --remove-all)
                REMOVE_ALL=true
                shift
                ;;
            --tenant-id)
                TENANT_ID="$2"
                shift 2
                ;;
            --pod)
                POD_NAME="$2"
                shift 2
                ;;
            *)
                echo "Unknown option: $1"
                echo "Usage: $0 [--discover-only] [--remove-all] [--tenant-id <id>] [--pod <pod-name>]"
                exit 1
                ;;
        esac
    done
    
    # Find or use specified pod
    if [ -z "$POD_NAME" ]; then
        POD_NAME=$(kubectl get pods --all-namespaces | grep -E 'db-backup|mysql' | grep Running | head -1 | awk '{print $2}')
        if [ -z "$POD_NAME" ]; then
            print_error "No available database pod found"
            exit 1
        fi
    fi
    
    if [ -z "$POD_NAME" ]; then
        print_error "No available database pod found"
        exit 1
    fi
    
    print_info "Using pod: $POD_NAME"
    
    # Discover tenants
    discover_tenants "$POD_NAME"
    
    if [ "$DISCOVER_ONLY" = true ]; then
        print_info "Discovery mode only. No tenants will be removed."
        exit 0
    fi
    
    # Handle removal
    if [ -n "$TENANT_ID" ]; then
        # Remove specific tenant
        echo ""
        read -p "Remove tenant $TENANT_ID? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            remove_tenant "$POD_NAME" "$TENANT_ID"
        else
            print_info "Operation cancelled."
        fi
    
    elif [ "$REMOVE_ALL" = true ]; then
        # Remove all tenants
        echo ""
        local tenant_list=$(get_tenant_list "$POD_NAME")
        local tenant_count=$(echo "$tenant_list" | wc -l)
        
        if [ "$tenant_count" -gt 0 ]; then
            echo "Found $tenant_count tenants to remove:"
            echo "$tenant_list"
            echo ""
            read -p "Remove all $tenant_count tenants? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                echo ""
                print_info "Removing all $tenant_count tenants from database..."
                
                local i=1
                while IFS= read -r tenant_id; do
                    if [ -n "$tenant_id" ]; then
                        echo ""
                        echo "[$i/$tenant_count] Removing tenant $tenant_id..."
                        if remove_tenant "$POD_NAME" "$tenant_id"; then
                            print_success "Removed tenant $tenant_id"
                        else
                            print_error "Failed to remove tenant $tenant_id"
                        fi
                        ((i++))
                    fi
                done <<< "$tenant_list"
                
                echo ""
                print_success "Completed removal of $tenant_count tenants"
            else
                print_info "Operation cancelled."
            fi
        else
            print_warning "No tenants found to remove"
        fi
    
    else
        echo ""
        print_info "Use --discover-only to only list tenants, --remove-all to remove all tenants, or --tenant-id <id> to remove a specific tenant"
    fi
}

# Run main function
main "$@" 