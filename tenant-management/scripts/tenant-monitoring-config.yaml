# Comprehensive Tenant Monitoring Configuration
# This file contains monitoring, alerting, and observability configurations for tenants

---
# ServiceMonitor for Tenant Application Metrics
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: tenant-app-monitor
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    monitoring.architrave.io/type: "application"
    release: prometheus
spec:
  selector:
    matchLabels:
      tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
      monitoring.architrave.io/scrape: "true"
  endpoints:
  - port: metrics
    interval: 15s
    path: /metrics
    honorLabels: true
  - port: http
    interval: 30s
    path: /health
    honorLabels: true

---
# PrometheusRule for Tenant-Specific Alerts
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: tenant-specific-alerts
  namespace: TENANT_NAMESPACE_PLACEHOLDER
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    monitoring.architrave.io/type: "alerts"
    release: prometheus
spec:
  groups:
  - name: tenant.TENANT_ID_PLACEHOLDER.sla
    interval: 30s
    rules:
    # SLA-based alerts
    - alert: TenantResponseTimeHigh
      expr: |
        histogram_quantile(0.95,
          sum(rate(istio_request_duration_milliseconds_bucket{
            destination_namespace="TENANT_NAMESPACE_PLACEHOLDER"
          }[5m])) by (le)
        ) > 500
      for: 2m
      labels:
        severity: warning
        tenant_id: TENANT_ID_PLACEHOLDER
        sla_type: response_time
      annotations:
        summary: "High response time for tenant TENANT_ID_PLACEHOLDER"
        description: "95th percentile response time is {{ $value }}ms, exceeding SLA of 500ms"
    
    - alert: TenantErrorRateHigh
      expr: |
        sum(rate(istio_requests_total{
          destination_namespace="TENANT_NAMESPACE_PLACEHOLDER",
          response_code!~"2.."
        }[5m])) /
        sum(rate(istio_requests_total{
          destination_namespace="TENANT_NAMESPACE_PLACEHOLDER"
        }[5m])) > 0.05
      for: 2m
      labels:
        severity: critical
        tenant_id: TENANT_ID_PLACEHOLDER
        sla_type: error_rate
      annotations:
        summary: "High error rate for tenant TENANT_ID_PLACEHOLDER"
        description: "Error rate is {{ $value | humanizePercentage }}, exceeding SLA of 5%"
    
    - alert: TenantAvailabilityLow
      expr: |
        avg_over_time(up{
          namespace="TENANT_NAMESPACE_PLACEHOLDER"
        }[5m]) < 0.99
      for: 1m
      labels:
        severity: critical
        tenant_id: TENANT_ID_PLACEHOLDER
        sla_type: availability
      annotations:
        summary: "Low availability for tenant TENANT_ID_PLACEHOLDER"
        description: "Availability is {{ $value | humanizePercentage }}, below SLA of 99%"

  - name: tenant.TENANT_ID_PLACEHOLDER.resources
    interval: 30s
    rules:
    # Resource utilization alerts
    - alert: TenantCPUUsageHigh
      expr: |
        sum(rate(container_cpu_usage_seconds_total{
          namespace="TENANT_NAMESPACE_PLACEHOLDER",
          container!=""
        }[5m])) /
        sum(container_spec_cpu_quota{
          namespace="TENANT_NAMESPACE_PLACEHOLDER",
          container!=""
        } / container_spec_cpu_period{
          namespace="TENANT_NAMESPACE_PLACEHOLDER",
          container!=""
        }) > 0.8
      for: 5m
      labels:
        severity: warning
        tenant_id: TENANT_ID_PLACEHOLDER
        resource_type: cpu
      annotations:
        summary: "High CPU usage for tenant TENANT_ID_PLACEHOLDER"
        description: "CPU usage is {{ $value | humanizePercentage }}, exceeding 80% threshold"
    
    - alert: TenantMemoryUsageHigh
      expr: |
        sum(container_memory_usage_bytes{
          namespace="TENANT_NAMESPACE_PLACEHOLDER",
          container!=""
        }) /
        sum(container_spec_memory_limit_bytes{
          namespace="TENANT_NAMESPACE_PLACEHOLDER",
          container!=""
        }) > 0.8
      for: 5m
      labels:
        severity: warning
        tenant_id: TENANT_ID_PLACEHOLDER
        resource_type: memory
      annotations:
        summary: "High memory usage for tenant TENANT_ID_PLACEHOLDER"
        description: "Memory usage is {{ $value | humanizePercentage }}, exceeding 80% threshold"
    
    - alert: TenantPodRestartHigh
      expr: |
        rate(kube_pod_container_status_restarts_total{
          namespace="TENANT_NAMESPACE_PLACEHOLDER"
        }[15m]) > 0.1
      for: 5m
      labels:
        severity: warning
        tenant_id: TENANT_ID_PLACEHOLDER
        stability_type: restarts
      annotations:
        summary: "High pod restart rate for tenant TENANT_ID_PLACEHOLDER"
        description: "Pod {{ $labels.pod }} is restarting frequently"

  - name: tenant.TENANT_ID_PLACEHOLDER.business
    interval: 60s
    rules:
    # Business metrics alerts
    - alert: TenantRequestVolumeAnomaly
      expr: |
        abs(
          sum(rate(istio_requests_total{
            destination_namespace="TENANT_NAMESPACE_PLACEHOLDER"
          }[5m])) -
          avg_over_time(
            sum(rate(istio_requests_total{
              destination_namespace="TENANT_NAMESPACE_PLACEHOLDER"
            }[5m]))[1h:5m]
          )
        ) / 
        avg_over_time(
          sum(rate(istio_requests_total{
            destination_namespace="TENANT_NAMESPACE_PLACEHOLDER"
          }[5m]))[1h:5m]
        ) > 0.5
      for: 10m
      labels:
        severity: info
        tenant_id: TENANT_ID_PLACEHOLDER
        anomaly_type: request_volume
      annotations:
        summary: "Request volume anomaly for tenant TENANT_ID_PLACEHOLDER"
        description: "Request volume deviates significantly from historical average"

---
# ConfigMap for Grafana Dashboard
apiVersion: v1
kind: ConfigMap
metadata:
  name: tenant-dashboard
  namespace: monitoring
  labels:
    tenant.architrave.io/tenant-id: TENANT_ID_PLACEHOLDER
    grafana_dashboard: "1"
data:
  tenant-TENANT_ID_PLACEHOLDER-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Tenant TENANT_ID_PLACEHOLDER Overview",
        "tags": ["tenant", "TENANT_ID_PLACEHOLDER"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Request Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(rate(istio_requests_total{destination_namespace=\"TENANT_NAMESPACE_PLACEHOLDER\"}[5m]))",
                "legendFormat": "Requests/sec"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Error Rate",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(rate(istio_requests_total{destination_namespace=\"TENANT_NAMESPACE_PLACEHOLDER\",response_code!~\"2..\"}[5m])) / sum(rate(istio_requests_total{destination_namespace=\"TENANT_NAMESPACE_PLACEHOLDER\"}[5m]))",
                "legendFormat": "Error Rate"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 3,
            "title": "Response Time (95th percentile)",
            "type": "graph",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(istio_request_duration_milliseconds_bucket{destination_namespace=\"TENANT_NAMESPACE_PLACEHOLDER\"}[5m])) by (le))",
                "legendFormat": "95th percentile"
              }
            ],
            "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
          }
        ],
        "time": {"from": "now-1h", "to": "now"},
        "refresh": "30s"
      }
    }
