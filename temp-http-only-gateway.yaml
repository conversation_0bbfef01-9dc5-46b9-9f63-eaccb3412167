---
# Temporary HTTP-only Gateway for testing
apiVersion: networking.istio.io/v1
kind: Gateway
metadata:
  name: temp-http-gateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    managed-by: temp-testing
spec:
  selector:
    istio: ingress
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "testaudit2025.architrave-assets.com"

---
# Temporary VirtualService for HTTP testing
apiVersion: networking.istio.io/v1
kind: VirtualService
metadata:
  name: temp-testaudit2025-vs
  namespace: tenant-testaudit2025
  labels:
    tenant: testaudit2025
    managed-by: temp-testing
spec:
  hosts:
  - "testaudit2025.architrave-assets.com"
  gateways:
  - istio-system/temp-http-gateway
  http:
  - match:
    - uri:
        prefix: "/api"
    route:
    - destination:
        host: testaudit2025-backend-service.tenant-testaudit2025.svc.cluster.local
        port:
          number: 8080
    headers:
      request:
        set:
          X-Forwarded-Proto: "http"
  - match:
    - uri:
        prefix: "/"
    route:
    - destination:
        host: testaudit2025-frontend-service.tenant-testaudit2025.svc.cluster.local
        port:
          number: 80
    headers:
      request:
        set:
          X-Forwarded-Proto: "http"
