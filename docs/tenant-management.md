# Tenant Management

This document provides information about the tenant management system.

## Overview

The tenant management system is designed to handle the complete lifecycle of tenants in the multi-tenant infrastructure. It provides the following features:

- Tenant onboarding
- Tenant offboarding
- Tenant lifecycle management (upgrade, downgrade, migration, suspension, reactivation)
- Tenant isolation
- Tenant resource management
- Tenant billing

## Tenant Onboarding

The tenant onboarding process creates all the necessary resources for a new tenant, including:

- Kubernetes namespace
- Database schema
- S3 bucket
- IAM roles and policies
- Network policies
- Service mesh configuration

### Onboarding Script

The onboarding script is located at `scripts/tenant/onboarding/onboard.sh` and supports the following parameters:

```
--tenant-name      Name of the tenant (required)
--subdomain        Subdomain for the tenant (required)
--environment      Environment (production, staging, development) (required)
--dms              Enable Document Management System (optional)
--logo             Custom logo URL (optional)
--delphi           Enable Delphi integration (optional)
--external-api     Enable External API (optional)
--document-class-set  Document class set (optional)
--language         Language code (optional)
--reference-data   Reference data set (optional)
--heap-tracking    Enable heap tracking (optional)
```

### Database Import

During onboarding, the script imports the database schema from the S3 bucket `architravetestdb` with the file `architrave_1.45.2.sql`. The import is performed using the AWS RDS Data API or a direct MySQL connection.

## Tenant Offboarding

The tenant offboarding process removes all resources associated with a tenant, including:

- Kubernetes namespace and resources
- Database schema
- S3 bucket
- IAM roles and policies

### Offboarding Script

The offboarding script is located at `scripts/tenant/offboarding/tenant-offboard.sh` and supports the following parameters:

```
--tenant-name      Name of the tenant (required)
--keep-backup      Keep backup of tenant data (optional)
--force            Force offboarding even if validation fails (optional)
```

## Tenant Lifecycle Management

The tenant lifecycle management system provides the following features:

### Upgrade/Downgrade

The upgrade/downgrade process changes the resources allocated to a tenant, such as:

- CPU and memory limits
- Storage capacity
- Feature enablement

### Migration

The migration process moves a tenant from one environment to another, such as from staging to production.

### Suspension/Reactivation

The suspension process temporarily disables a tenant without removing its resources. The reactivation process re-enables a suspended tenant.

### Data Export/Import

The data export/import process allows exporting tenant data for backup or migration purposes, and importing it into a new or existing tenant.

## Tenant Isolation

The tenant isolation system ensures that each tenant's resources are isolated from other tenants, including:

- Network isolation using Kubernetes network policies
- Data isolation using separate database schemas
- Storage isolation using separate S3 buckets
- Access control using IAM roles and policies

## Tenant Resource Management

The tenant resource management system ensures that each tenant has the appropriate resources allocated, including:

- CPU and memory limits using Kubernetes resource quotas
- Storage capacity using PVCs
- Network bandwidth using network policies

## Tenant Billing

The tenant billing system tracks resource usage for each tenant and generates billing reports, including:

- CPU and memory usage
- Storage usage
- Network usage
- Feature usage
