# Tenant Onboarding Script Documentation

The tenant onboarding script automates the deployment of a new tenant with all necessary components in the Architrave platform. This document provides detailed information about the script, its usage, and the resources it creates.

## Overview

The script creates the following resources for each tenant:

1. **Namespace**: `tenant-<tenant-id>`
2. **Secrets**:
   - `db-credentials`: Database connection details
   - `ssl-cert`: SSL certificate and key
3. **ConfigMaps**:
   - `php-fpm-config`: PHP-FPM configuration
   - `app-config`: Application configuration
   - `webapp-env`: Environment variables for the webapp
4. **Deployments**:
   - `tenant-<tenant-id>-backend`: Backend application
   - `tenant-<tenant-id>-frontend`: Frontend application
   - `tenant-<tenant-id>-rabbitmq`: RabbitMQ message broker
5. **Services**:
   - `webapp`: Service for the backend
   - `tenant-<tenant-id>-rabbitmq`: Service for RabbitMQ
   - `tenant-<tenant-id>-frontend`: Service for the frontend

## Prerequisites

- Python 3.6+
- `kubectl` configured with access to your Kubernetes cluster
- SSL certificate and key files for the tenant
- SQL file for database initialization

## Installation

The script is located in the `scripts/tenant/onboarding` directory. You can run it directly or use the wrapper shell script `onboard_tenant.sh`.

## Usage

### Python Script

```bash
python scripts/tenant/onboarding/tenant_onboarding.py \
  --tenant-id <tenant-id> \
  --tenant-name "<Tenant Name>" \
  --db-password "<DB Password>" \
  --ssl-cert-path <path-to-cert> \
  --ssl-key-path <path-to-key> \
  --sql-file <path-to-sql-file>
```

### Shell Script Wrapper

```bash
./scripts/tenant/onboarding/onboard_tenant.sh \
  --tenant-id <tenant-id> \
  --tenant-name "<Tenant Name>" \
  --db-password "<DB Password>" \
  --ssl-cert-path <path-to-cert> \
  --ssl-key-path <path-to-key> \
  --sql-file <path-to-sql-file>
```

## Required Arguments

- `--tenant-id`: Tenant ID (lowercase, no spaces)
- `--tenant-name`: Tenant Name
- `--db-password`: Database password
- `--ssl-cert-path`: Path to SSL certificate file
- `--ssl-key-path`: Path to SSL key file
- `--sql-file`: Path to SQL file for database initialization

## Optional Arguments

- `--db-host`: Database host (default: production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com)
- `--db-port`: Database port (default: 3306)
- `--db-user`: Database username (default: admin)
- `--backend-image`: Backend image (default: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.41)
- `--frontend-image`: Frontend image (default: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl)
- `--rabbitmq-image`: RabbitMQ image (default: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02)
- `--replicas`: Number of replicas for deployments (default: 1)
- `--dry-run`: Print the generated YAML without applying

## Example

```bash
./scripts/tenant/onboarding/onboard_tenant.sh \
  --tenant-id example-tenant \
  --tenant-name "Example Tenant" \
  --db-password "StrongPassword123!" \
  --ssl-cert-path /path/to/architrave.crt \
  --ssl-key-path /path/to/architrave.key \
  --sql-file /path/to/architrave_1.45.2.sql
```

## Dry Run

To see what resources would be created without actually applying them:

```bash
./scripts/tenant/onboarding/onboard_tenant.sh \
  --tenant-id example-tenant \
  --tenant-name "Example Tenant" \
  --db-password "StrongPassword123!" \
  --ssl-cert-path /path/to/architrave.crt \
  --ssl-key-path /path/to/architrave.key \
  --sql-file /path/to/architrave_1.45.2.sql \
  --dry-run
```

## Resource Details

### Namespace

The script creates a namespace for the tenant with the name `tenant-<tenant-id>`. This namespace isolates the tenant's resources from other tenants.

### Secrets

#### Database Credentials Secret

The script creates a secret named `db-credentials` that contains the following keys:

- `host`: Database host
- `port`: Database port
- `user`: Database username
- `username`: Database username (duplicate for compatibility)
- `password`: Database password
- `database`: Database name
- `dbname`: Database name (duplicate for compatibility)

The database name is automatically generated as `tenant_<tenant_id>` with hyphens replaced by underscores.

#### SSL Certificate Secret

The script creates a secret named `ssl-cert` that contains the SSL certificate and key for the tenant's domain. The secret contains the following keys:

- `tls.crt`: SSL certificate
- `tls.key`: SSL key

### ConfigMaps

#### PHP-FPM Configuration

The script creates a ConfigMap named `php-fpm-config` that contains the PHP-FPM configuration for the backend. The configuration includes:

- Process manager settings
- Resource limits
- Error logging
- File upload limits
- Timeout settings

#### Application Configuration

The script creates a ConfigMap named `app-config` that contains the application configuration for the backend. The configuration includes:

- Database connection settings
- Storage settings (S3 bucket)
- RabbitMQ connection settings
- Feature flags
- Tenant information

#### Webapp Environment Variables

The script creates a ConfigMap named `webapp-env` that contains environment variables for the webapp. The variables include:

- RabbitMQ connection settings
- PHP-FPM process manager settings

### Deployments

#### Backend Deployment

The script creates a deployment named `tenant-<tenant-id>-backend` for the backend application. The deployment includes:

- Container with the backend image
- Environment variables for tenant ID, database connection, and RabbitMQ connection
- Resource limits and requests
- Liveness and readiness probes
- Volume mounts for storage, PHP-FPM configuration, and application configuration
- Init container for setting up the directory structure

#### Frontend Deployment

The script creates a deployment named `tenant-<tenant-id>-frontend` for the frontend application. The deployment includes:

- Container with the frontend image
- Environment variables for tenant ID and backend host
- Resource limits and requests
- Liveness and readiness probes
- Volume mounts for storage and SSL certificate
- Init container for setting up the directory structure
- Startup script for configuring Nginx

#### RabbitMQ Deployment

The script creates a deployment named `tenant-<tenant-id>-rabbitmq` for the RabbitMQ message broker. The deployment includes:

- Container with the RabbitMQ image
- Environment variables for RabbitMQ credentials
- Resource limits and requests

### Services

#### Webapp Service

The script creates a service named `webapp` for the backend application. The service exposes the following ports:

- `8080`: API port (maps to container port 9000)
- `9090`: Metrics port (maps to container port 9090)

#### RabbitMQ Service

The script creates a service named `tenant-<tenant-id>-rabbitmq` for the RabbitMQ message broker. The service exposes the following ports:

- `5672`: AMQP port
- `15672`: Management port
- `15692`: Prometheus metrics port

#### Frontend Service

The script creates a service named `tenant-<tenant-id>-frontend` for the frontend application. The service exposes the following ports:

- `80`: HTTP port
- `443`: HTTPS port

## Database Initialization

The script initializes the database with the provided SQL file. It performs the following steps:

1. Creates a new database named `tenant_<tenant_id>` (with hyphens replaced by underscores)
2. Imports the SQL file into the database
3. Verifies that the import was successful by checking the number of tables

## Troubleshooting

If you encounter issues:

1. Check the logs of the pods:
   ```bash
   kubectl logs -n tenant-<tenant-id> <pod-name>
   ```

2. Check the status of the pods:
   ```bash
   kubectl get pods -n tenant-<tenant-id>
   ```

3. Check the events in the namespace:
   ```bash
   kubectl get events -n tenant-<tenant-id>
   ```

4. If the database initialization fails, you can manually initialize it:
   ```bash
   kubectl exec -n tenant-<tenant-id> <backend-pod-name> -c backend -- php -r "..."
   ```

## Security Considerations

The script creates several resources that contain sensitive information, such as database credentials and SSL certificates. Make sure to:

1. Use strong passwords for the database
2. Use valid SSL certificates
3. Restrict access to the Kubernetes cluster
4. Regularly rotate credentials

## Customization

You can customize the script by modifying the default values at the beginning of the script or by passing different values as command-line arguments.

## Limitations

- The script assumes that the database server already exists and is accessible
- The script assumes that the SSL certificate and key files are valid
- The script assumes that the SQL file is compatible with the database schema
- The script does not create ingress resources for the frontend service

## Future Improvements

- Add support for creating ingress resources
- Add support for custom resource limits and requests
- Add support for custom storage classes
- Add support for custom network policies
- Add support for custom security contexts
- Add support for custom probes
- Add support for custom annotations
- Add support for custom labels
- Add support for custom environment variables
- Add support for custom volumes and volume mounts
- Add support for custom init containers
- Add support for custom sidecars
- Add support for custom affinity and anti-affinity rules
- Add support for custom tolerations
- Add support for custom node selectors
- Add support for custom priority classes
- Add support for custom pod disruption budgets
- Add support for custom horizontal pod autoscalers
- Add support for custom vertical pod autoscalers
- Add support for custom pod security policies
- Add support for custom network policies
- Add support for custom resource quotas
- Add support for custom limit ranges
- Add support for custom service accounts
- Add support for custom roles and role bindings
- Add support for custom cluster roles and cluster role bindings
- Add support for custom pod security contexts
- Add support for custom container security contexts
- Add support for custom init container security contexts
- Add support for custom sidecar security contexts
- Add support for custom pod annotations
- Add support for custom container annotations
- Add support for custom init container annotations
- Add support for custom sidecar annotations
- Add support for custom pod labels
- Add support for custom container labels
- Add support for custom init container labels
- Add support for custom sidecar labels
- Add support for custom pod environment variables
- Add support for custom container environment variables
- Add support for custom init container environment variables
- Add support for custom sidecar environment variables
- Add support for custom pod volumes
- Add support for custom container volumes
- Add support for custom init container volumes
- Add support for custom sidecar volumes
- Add support for custom pod volume mounts
- Add support for custom container volume mounts
- Add support for custom init container volume mounts
- Add support for custom sidecar volume mounts
- Add support for custom pod probes
- Add support for custom container probes
- Add support for custom init container probes
- Add support for custom sidecar probes
- Add support for custom pod resources
- Add support for custom container resources
- Add support for custom init container resources
- Add support for custom sidecar resources
- Add support for custom pod affinity
- Add support for custom container affinity
- Add support for custom init container affinity
- Add support for custom sidecar affinity
- Add support for custom pod anti-affinity
- Add support for custom container anti-affinity
- Add support for custom init container anti-affinity
- Add support for custom sidecar anti-affinity
- Add support for custom pod tolerations
- Add support for custom container tolerations
- Add support for custom init container tolerations
- Add support for custom sidecar tolerations
- Add support for custom pod node selectors
- Add support for custom container node selectors
- Add support for custom init container node selectors
- Add support for custom sidecar node selectors
- Add support for custom pod priority classes
- Add support for custom container priority classes
- Add support for custom init container priority classes
- Add support for custom sidecar priority classes
- Add support for custom pod disruption budgets
- Add support for custom container disruption budgets
- Add support for custom init container disruption budgets
- Add support for custom sidecar disruption budgets
- Add support for custom pod horizontal pod autoscalers
- Add support for custom container horizontal pod autoscalers
- Add support for custom init container horizontal pod autoscalers
- Add support for custom sidecar horizontal pod autoscalers
- Add support for custom pod vertical pod autoscalers
- Add support for custom container vertical pod autoscalers
- Add support for custom init container vertical pod autoscalers
- Add support for custom sidecar vertical pod autoscalers
- Add support for custom pod pod security policies
- Add support for custom container pod security policies
- Add support for custom init container pod security policies
- Add support for custom sidecar pod security policies
- Add support for custom pod network policies
- Add support for custom container network policies
- Add support for custom init container network policies
- Add support for custom sidecar network policies
- Add support for custom pod resource quotas
- Add support for custom container resource quotas
- Add support for custom init container resource quotas
- Add support for custom sidecar resource quotas
- Add support for custom pod limit ranges
- Add support for custom container limit ranges
- Add support for custom init container limit ranges
- Add support for custom sidecar limit ranges
- Add support for custom pod service accounts
- Add support for custom container service accounts
- Add support for custom init container service accounts
- Add support for custom sidecar service accounts
- Add support for custom pod roles
- Add support for custom container roles
- Add support for custom init container roles
- Add support for custom sidecar roles
- Add support for custom pod role bindings
- Add support for custom container role bindings
- Add support for custom init container role bindings
- Add support for custom sidecar role bindings
- Add support for custom pod cluster roles
- Add support for custom container cluster roles
- Add support for custom init container cluster roles
- Add support for custom sidecar cluster roles
- Add support for custom pod cluster role bindings
- Add support for custom container cluster role bindings
- Add support for custom init container cluster role bindings
- Add support for custom sidecar cluster role bindings
- Add support for custom pod pod security contexts
- Add support for custom container pod security contexts
- Add support for custom init container pod security contexts
- Add support for custom sidecar pod security contexts
- Add support for custom pod container security contexts
- Add support for custom container container security contexts
- Add support for custom init container container security contexts
- Add support for custom sidecar container security contexts
- Add support for custom pod init container security contexts
- Add support for custom container init container security contexts
- Add support for custom init container init container security contexts
- Add support for custom sidecar init container security contexts
- Add support for custom pod sidecar security contexts
- Add support for custom container sidecar security contexts
- Add support for custom init container sidecar security contexts
- Add support for custom sidecar sidecar security contexts# Tenant Onboarding Script Documentation

The tenant onboarding script automates the deployment of a new tenant with all necessary components in the Architrave platform.

## Overview

The script creates the following resources for each tenant:

1. **Namespace**: `tenant-<tenant-id>`
2. **Secrets**:
   - `db-credentials`: Database connection details
   - `ssl-cert`: SSL certificate and key
3. **ConfigMaps**:
   - `php-fpm-config`: PHP-FPM configuration
   - `app-config`: Application configuration
   - `webapp-env`: Environment variables for the webapp
4. **Deployments**:
   - `tenant-<tenant-id>-backend`: Backend application
   - `tenant-<tenant-id>-frontend`: Frontend application
   - `tenant-<tenant-id>-rabbitmq`: RabbitMQ message broker
5. **Services**:
   - `webapp`: Service for the backend
   - `tenant-<tenant-id>-rabbitmq`: Service for RabbitMQ
   - `tenant-<tenant-id>-frontend`: Service for the frontend

## Usage

### Python Script

```bash
python scripts/tenant/onboarding/tenant_onboarding.py \
  --tenant-id <tenant-id> \
  --tenant-name "<Tenant Name>" \
  --db-password "<DB Password>" \
  --ssl-cert-path <path-to-cert> \
  --ssl-key-path <path-to-key> \
  --sql-file <path-to-sql-file>
```

### Shell Script Wrapper

```bash
./scripts/tenant/onboarding/onboard_tenant.sh \
  --tenant-id <tenant-id> \
  --tenant-name "<Tenant Name>" \
  --db-password "<DB Password>" \
  --ssl-cert-path <path-to-cert> \
  --ssl-key-path <path-to-key> \
  --sql-file <path-to-sql-file>
```

## Required Arguments

- `--tenant-id`: Tenant ID (lowercase, no spaces)
- `--tenant-name`: Tenant Name
- `--db-password`: Database password
- `--ssl-cert-path`: Path to SSL certificate file
- `--ssl-key-path`: Path to SSL key file
- `--sql-file`: Path to SQL file for database initialization

## Optional Arguments

- `--db-host`: Database host (default: production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com)
- `--db-port`: Database port (default: 3306)
- `--db-user`: Database username (default: admin)
- `--backend-image`: Backend image (default: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/webapp_dev:2.0.41)
- `--frontend-image`: Frontend image (default: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/nginx_dev:1.0.6-update_ssl)
- `--rabbitmq-image`: RabbitMQ image (default: 545009857703.dkr.ecr.eu-central-1.amazonaws.com/rabbitmq_dev:1.02)
- `--replicas`: Number of replicas for deployments (default: 1)
- `--dry-run`: Print the generated YAML without applying

## Example

```bash
./scripts/tenant/onboarding/onboard_tenant.sh \
  --tenant-id example-tenant \
  --tenant-name "Example Tenant" \
  --db-password "StrongPassword123!" \
  --ssl-cert-path /path/to/architrave.crt \
  --ssl-key-path /path/to/architrave.key \
  --sql-file /path/to/architrave_1.45.2.sql
```

## Database Initialization

The script initializes the database with the provided SQL file. It performs the following steps:

1. Creates a new database named `tenant_<tenant_id>` (with hyphens replaced by underscores)
2. Imports the SQL file into the database
3. Verifies that the import was successful by checking the number of tables

## Troubleshooting

If you encounter issues:

1. Check the logs of the pods:
   ```bash
   kubectl logs -n tenant-<tenant-id> <pod-name>
   ```

2. Check the status of the pods:
   ```bash
   kubectl get pods -n tenant-<tenant-id>
   ```

3. Check the events in the namespace:
   ```bash
   kubectl get events -n tenant-<tenant-id>
   ```

4. If the database initialization fails, you can manually initialize it using the backend pod.

## Security Considerations

The script creates several resources that contain sensitive information, such as database credentials and SSL certificates. Make sure to:

1. Use strong passwords for the database
2. Use valid SSL certificates
3. Restrict access to the Kubernetes cluster
4. Regularly rotate credentials