#!/bin/bash

# Exit on error
set -e

echo "🚀 Deploying additional components..."

# Create necessary namespaces
kubectl create namespace velero --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace testing --dry-run=client -o yaml | kubectl apply -f -

# Install Velero for backup
echo "📦 Installing Velero..."
velero install \
  --provider aws \
  --plugins velero/velero-plugin-for-aws:v1.5.0 \
  --bucket architrave-backups \
  --backup-location-config region=eu-west-2 \
  --secret-file ./credentials-velero \
  --namespace velero

# Apply backup configuration
echo "💾 Applying backup configuration..."
kubectl apply -f backup/backup-config.yaml

# Apply performance testing configuration
echo "⚡ Applying performance testing configuration..."
kubectl apply -f testing/performance-test.yaml

# Create documentation structure
echo "📚 Creating documentation structure..."
mkdir -p docs/{architecture,deployment,operations,security,development,troubleshooting}
touch docs/architecture/{system-overview,component-architecture,data-flow,security-architecture}.md
touch docs/deployment/{prerequisites,installation,configuration,upgrade}.md
touch docs/operations/{monitoring,alerting,backup-recovery,maintenance}.md
touch docs/security/{overview,access-control,network-security,compliance}.md
touch docs/development/{api-docs,guide,testing,contributing}.md
touch docs/troubleshooting/{common-issues,debugging,performance,recovery}.md

# Wait for components to be ready
echo "⏳ Waiting for components to be ready..."
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=velero -n velero --timeout=300s
kubectl wait --for=condition=ready pod -l app=k6 -n testing --timeout=300s

# Verify installations
echo "✅ Verifying installations..."

# Check Velero
kubectl get pods -n velero

# Check performance testing
kubectl get pods -n testing

echo "🎉 Additional components deployment completed!"
echo "Next steps:"
echo "1. Configure backup credentials in ./credentials-velero"
echo "2. Review and customize performance test scenarios"
echo "3. Complete documentation with specific details"
echo "4. Run initial backup and verify"
echo "5. Execute performance tests and review results" 