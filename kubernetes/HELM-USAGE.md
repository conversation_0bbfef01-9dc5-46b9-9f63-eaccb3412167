# Helm Usage Guide for This Project

This guide explains how to use Helm charts to manage Kubernetes applications in this project. It includes step-by-step instructions for installing, upgrading, and uninstalling charts, with Gatekeeper (OPA) as a primary example. The same process applies to other charts.

---

## Prerequisites
- **Helm 3 installed** ([Install Helm](https://helm.sh/docs/intro/install/))
- **kubectl configured** to point to your Kubernetes cluster

---

## 1. Adding a Helm Repository

A Helm repository is a collection of charts. For example, to add the official Gatekeeper chart repository:

```sh
helm repo add gatekeeper https://open-policy-agent.github.io/gatekeeper/charts
helm repo update
```

You can add other repositories as needed. For example:
```sh
helm repo add bitnami https://charts.bitnami.com/bitnami
```

---

## 2. Installing a Helm Chart (Example: Gatekeeper)

To install Gatekeeper with your custom values:

```sh
helm install gatekeeper gatekeeper/gatekeeper -f kubernetes/gatekeeper-values.yaml
```
- `gatekeeper` is the release name (choose any name).
- `gatekeeper/gatekeeper` is the chart name from the repo.
- `-f kubernetes/gatekeeper-values.yaml` applies your custom configuration.

**For other charts:**
- Find the chart name and repo (e.g., `bitnami/nginx`).
- Use a custom values file if needed: `-f path/to/values.yaml`.

---

## 3. Upgrading a Helm Release

If you update your values file or want to upgrade the chart version:

```sh
helm upgrade gatekeeper gatekeeper/gatekeeper -f kubernetes/gatekeeper-values.yaml
```

---

## 4. Uninstalling a Helm Release

To remove Gatekeeper (or any Helm-managed app):

```sh
helm uninstall gatekeeper
```

---

## 5. Checking Release Status

To see the status of your Helm release:

```sh
helm status gatekeeper
```

To list all releases:

```sh
helm list
```

---

## 6. Customizing Charts

- Use the `-f` flag to provide a YAML file with your custom values.
- You can override values inline with `--set key=value`.

Example:
```sh
helm install myapp bitnami/nginx --set service.type=LoadBalancer
```

---

## 7. Using Other Charts

The process is the same for any chart:
1. Add the repository (if not already added).
2. Install with `helm install ...`.
3. Upgrade with `helm upgrade ...`.
4. Uninstall with `helm uninstall ...`.

**Example: Installing NGINX from Bitnami**
```sh
helm repo add bitnami https://charts.bitnami.com/bitnami
helm install my-nginx bitnami/nginx
```

---

## 8. Resources
- [Helm Documentation](https://helm.sh/docs/)
- [Gatekeeper Helm Chart](https://artifacthub.io/packages/helm/gatekeeper/gatekeeper)
- [ArtifactHub (find more charts)](https://artifacthub.io/)

---

## 9. Troubleshooting
- Use `helm list` to see installed releases.
- Use `helm uninstall <release>` to remove a release.
- Use `helm status <release>` for details.
- Check logs with `kubectl logs` if pods are not running as expected.

---

**For questions or issues, consult the official Helm and chart documentation, or reach out to your DevOps team.** 