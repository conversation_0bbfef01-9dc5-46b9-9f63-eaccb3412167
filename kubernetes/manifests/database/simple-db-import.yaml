apiVersion: batch/v1
kind: Job
metadata:
  name: simple-db-import
  namespace: tenant-check-testnew
spec:
  ttlSecondsAfterFinished: 3600
  template:
    spec:
      containers:
      - name: db-import
        image: mysql:8.0
        command:
        - /bin/bash
        - -c
        - |
          # Set variables
          TENANT_ID="check-testnew"
          DB_NAME="tenant_${TENANT_ID}"
          DB_USER="tenant_${TENANT_ID}"
          DB_PASSWORD="$(cat /db-creds/password)"
          RDS_HOST="production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
          RDS_PORT="3306"
          
          # Get admin credentials from mounted secret
          RDS_ADMIN_USER="$(cat /admin-creds/username)"
          RDS_ADMIN_PASSWORD="$(cat /admin-creds/password)"
          
          echo "RDS Host: $RDS_HOST"
          echo "RDS Port: $RDS_PORT"
          
          # Test connection to RDS
          echo "Testing connection to RDS..."
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SELECT 1"
          
          # Create database and user
          echo "Creating database schema and user..."
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" <<EOF
          CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
          CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASSWORD';
          GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'%';
          FLUSH PRIVILEGES;
EOF
          
          echo "Database schema and user created successfully"
          
          # Create a simple table for testing
          echo "Creating a simple table for testing..."
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" "$DB_NAME" <<EOF
          CREATE TABLE IF NOT EXISTS test_table (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
          );
          INSERT INTO test_table (name) VALUES ('Test Record');
EOF
          
          echo "Test table created successfully"
          
          # Verify the table was created
          echo "Verifying table creation..."
          TABLE_COUNT=$(mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema='$DB_NAME'" | tail -n 1)
          
          echo "Tables in database: $TABLE_COUNT"
        volumeMounts:
        - name: db-creds
          mountPath: /db-creds
        - name: admin-creds
          mountPath: /admin-creds
      volumes:
      - name: db-creds
        secret:
          secretName: db-credentials
      - name: admin-creds
        secret:
          secretName: rds-admin-credentials
      restartPolicy: Never
