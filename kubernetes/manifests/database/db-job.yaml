apiVersion: batch/v1
kind: Job
metadata:
  name: db-job
  namespace: tenant-check-testnew
spec:
  template:
    spec:
      containers:
      - name: mysql
        image: mysql:8.0
        command: ["/bin/bash"]
        args:
          - "-c"
          - |
            echo "Testing connection to RDS..."
            mysql -h production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com -P 3306 -u admin -p"1O\$JbjX%\$Gnh9LNg" -e "CREATE DATABASE IF NOT EXISTS tenant_check_testnew;"
            echo "Database created successfully"
        volumeMounts:
        - name: admin-creds
          mountPath: /admin-creds
      volumes:
      - name: admin-creds
        secret:
          secretName: rds-admin-credentials
      restartPolicy: Never
