apiVersion: batch/v1
kind: Job
metadata:
  name: db-import-mock
  namespace: tenant-check-test
spec:
  ttlSecondsAfterFinished: 3600
  template:
    spec:
      containers:
      - name: mysql-client
        image: mysql:8.0
        command:
        - /bin/bash
        - -c
        - |
          # Set variables
          TENANT_ID="check_test"
          DB_NAME="tenant_${TENANT_ID}"
          DB_USER="tenant_${TENANT_ID}"
          DB_PASSWORD="Secure123Password!"
          RDS_HOST="production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
          RDS_PORT="3306"
          RDS_ADMIN_USER="admin"
          RDS_ADMIN_PASSWORD="1O\$JbjX%\$Gnh9LNg"

          echo "Creating database and user..."
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" <<EOF
          CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
          CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASSWORD';
          GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'%';
          FLUSH PRIVILEGES;
          EOF

          if [ $? -ne 0 ]; then
            echo "Failed to create database and user"
            exit 1
          fi

          echo "Database and user created successfully"

          # Import schema
          echo "Importing schema..."
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" "$DB_NAME" < /schema/schema.sql

          if [ $? -ne 0 ]; then
            echo "Failed to import schema"
            exit 1
          fi

          echo "Schema imported successfully"

          # Update Kubernetes secret
          cat <<EOF > /tmp/db-credentials.yaml
          apiVersion: v1
          kind: Secret
          metadata:
            name: db-credentials
            namespace: tenant-check-test
          type: Opaque
          stringData:
            host: "$RDS_HOST"
            port: "$RDS_PORT"
            database: "$DB_NAME"
            username: "$DB_USER"
            password: "$DB_PASSWORD"
          EOF

          kubectl apply -f /tmp/db-credentials.yaml

          echo "Database credentials updated successfully"
        volumeMounts:
        - name: schema-volume
          mountPath: /schema
        resources:
          limits:
            memory: "64Mi"
            cpu: "50m"
          requests:
            memory: "32Mi"
            cpu: "25m"
      volumes:
      - name: schema-volume
        configMap:
          name: mock-schema
      restartPolicy: Never
  backoffLimit: 3
