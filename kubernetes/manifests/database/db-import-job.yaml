apiVersion: batch/v1
kind: Job
metadata:
  name: db-import-check-testnew
  namespace: tenant-check-testnew
spec:
  ttlSecondsAfterFinished: 3600
  template:
    spec:
      containers:
      - name: db-import
        image: amazon/aws-cli:latest
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "200m"
            memory: "512Mi"
        command:
        - /bin/bash
        - -c
        - |
          # Install MySQL client
          yum install -y mysql
          
          # Set variables
          TENANT_ID="check-testnew"
          DB_NAME="tenant_${TENANT_ID}"
          DB_USER="tenant_${TENANT_ID}"
          DB_PASSWORD=$(kubectl get secret db-credentials -n tenant-check-testnew -o jsonpath='{.data.password}' | base64 --decode)
          RDS_HOST=$(kubectl get secret db-credentials -n tenant-check-testnew -o jsonpath='{.data.host}' | base64 --decode)
          RDS_PORT=$(kubectl get secret db-credentials -n tenant-check-testnew -o jsonpath='{.data.port}' | base64 --decode)
          S3_BUCKET="architravetestdb"
          S3_KEY="architrave_1.45.2.sql"
          
          # Get RDS admin credentials from AWS Secrets Manager
          echo "Getting RDS admin credentials from AWS Secrets Manager..."
          SECRET_JSON=$(aws secretsmanager get-secret-value --secret-id production/rds/master-d8076664-cef8-4b20-1f9d-47b1247bb611 --query SecretString --output text)
          
          if [ -z "$SECRET_JSON" ]; then
            echo "Failed to retrieve secret"
            exit 1
          fi
          
          # Parse the JSON to get the credentials
          RDS_ADMIN_USER=$(echo "$SECRET_JSON" | grep -o '"username":"[^"]*"' | cut -d'"' -f4)
          RDS_ADMIN_PASSWORD=$(echo "$SECRET_JSON" | grep -o '"password":"[^"]*"' | cut -d'"' -f4)
          
          echo "Retrieved RDS credentials from secret"
          echo "RDS Host: $RDS_HOST"
          echo "RDS Port: $RDS_PORT"
          echo "RDS Admin User: $RDS_ADMIN_USER"
          
          # Test connection to RDS
          echo "Testing connection to RDS..."
          if ! mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SELECT 1" &>/dev/null; then
            echo "Failed to connect to RDS"
            echo "Trying to connect with verbose output..."
            mysql -v -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SELECT 1"
            exit 1
          fi
          
          echo "Successfully connected to RDS"
          
          # Create database and user
          echo "Creating database schema and user..."
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" <<EOF_SQL
          CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
          CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASSWORD';
          GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'%';
          FLUSH PRIVILEGES;
EOF_SQL
          
          if [ $? -ne 0 ]; then
            echo "Failed to create database schema and user"
            exit 1
          fi
          
          echo "Database schema and user created successfully"
          
          # Import SQL file directly from S3 to RDS
          echo "Importing SQL file from S3 to RDS"
          echo "Using S3 bucket: $S3_BUCKET"
          echo "Using S3 key: $S3_KEY"
          
          # Check if the S3 object exists
          if ! aws s3 ls "s3://$S3_BUCKET/$S3_KEY"; then
            echo "SQL file s3://$S3_BUCKET/$S3_KEY does not exist"
            exit 1
          fi
          
          # Use the AWS CLI to get the SQL file content and pipe it directly to mysql
          echo "Starting SQL import from S3 to RDS (this may take a few minutes)..."
          aws s3 cp "s3://$S3_BUCKET/$S3_KEY" - | mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" "$DB_NAME"
          
          if [ $? -ne 0 ]; then
            echo "Failed to import SQL file from S3 to RDS"
            exit 1
          fi
          
          echo "SQL file imported successfully"
          
          # Verify the import was successful by checking for tables
          echo "Verifying database import..."
          TABLE_COUNT=$(mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SELECT COUNT(table_name) FROM information_schema.tables WHERE table_schema='$DB_NAME'" | tail -n 1)
          
          if [ -z "$TABLE_COUNT" ] || [ "$TABLE_COUNT" -eq 0 ]; then
            echo "Database import verification failed: No tables found in the database"
            exit 1
          fi
          
          echo "SQL file imported successfully ($TABLE_COUNT tables created)"
      restartPolicy: Never
      serviceAccountName: default
