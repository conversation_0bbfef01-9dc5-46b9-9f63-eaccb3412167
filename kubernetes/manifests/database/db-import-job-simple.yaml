apiVersion: batch/v1
kind: Job
metadata:
  name: tenant-check-test-db-import-simple
  namespace: tenant-check-test
spec:
  ttlSecondsAfterFinished: 3600
  template:
    spec:
      containers:
      - name: db-import
        image: mysql:8.0
        command:
        - /bin/bash
        - -c
        - |
          # Set variables
          TENANT_ID="check-test"
          DB_NAME="tenant_${TENANT_ID}"
          DB_USER="tenant_${TENANT_ID}"
          DB_PASSWORD="Secure123Password!"
          
          # Get RDS credentials from secret
          RDS_HOST="production-aurora-serverless.cluster-cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
          RDS_PORT="3306"
          RDS_ADMIN_USER="admin"
          RDS_ADMIN_PASSWORD="1O\$JbjX%\$Gnh9LNg"
          
          echo "Creating database schema and user for tenant $TENANT_ID"
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" <<EOF
          CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
          CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASSWORD';
          GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'%';
          FLUSH PRIVILEGES;
          EOF
          
          if [ $? -ne 0 ]; then
            echo "Failed to create database schema and user"
            exit 1
          fi
          
          echo "Database schema and user created successfully"
          echo "Database import completed successfully for tenant $TENANT_ID"
      restartPolicy: Never
  backoffLimit: 3
