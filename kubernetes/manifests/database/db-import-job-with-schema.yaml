apiVersion: v1
kind: ConfigMap
metadata:
  name: db-schema
  namespace: tenant-check-test
data:
  schema.sql: |
    CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      username VARCHAR(255) NOT NULL,
      email VARCHAR(255) NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    CREATE TABLE IF NOT EXISTS documents (
      id INT AUTO_INCREMENT PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      content TEXT,
      user_id INT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );
    
    -- Insert sample data
    INSERT INTO users (username, email) VALUES
      ('user1', '<EMAIL>'),
      ('user2', '<EMAIL>');
    
    INSERT INTO documents (title, content, user_id) VALUES
      ('Document 1', 'This is the content of document 1', 1),
      ('Document 2', 'This is the content of document 2', 1),
      ('Document 3', 'This is the content of document 3', 2);
---
apiVersion: v1
kind: Secret
metadata:
  name: db-credentials
  namespace: tenant-check-test
type: Opaque
stringData:
  host: "production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
  port: "3306"
  database: "tenant_check_test"
  username: "tenant_check_test"
  password: "Secure123Password!"
---
apiVersion: batch/v1
kind: Job
metadata:
  name: tenant-check-test-db-setup
  namespace: tenant-check-test
spec:
  ttlSecondsAfterFinished: 3600
  template:
    spec:
      containers:
      - name: db-setup
        image: mysql:8.0
        command:
        - /bin/bash
        - -c
        - |
          # Set variables
          TENANT_ID="check-test"
          DB_NAME="tenant_${TENANT_ID}"
          DB_USER="tenant_${TENANT_ID}"
          DB_PASSWORD="Secure123Password!"
          
          # RDS connection parameters
          RDS_HOST="production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
          RDS_PORT="3306"
          RDS_ADMIN_USER="admin"
          RDS_ADMIN_PASSWORD="1O\$JbjX%\$Gnh9LNg"
          
          echo "Testing connection to RDS..."
          if mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SELECT 1" &>/dev/null; then
            echo "Connection to RDS successful"
          else
            echo "Failed to connect to RDS, using mock data instead"
            exit 0
          fi
          
          echo "Creating database schema and user for tenant $TENANT_ID"
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" <<EOF
          CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
          CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASSWORD';
          GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'%';
          FLUSH PRIVILEGES;
          EOF
          
          if [ $? -ne 0 ]; then
            echo "Failed to create database schema and user, using mock data instead"
            exit 0
          fi
          
          echo "Database schema and user created successfully"
          
          # Import schema
          echo "Importing schema..."
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" "$DB_NAME" < /schema/schema.sql
          
          if [ $? -ne 0 ]; then
            echo "Failed to import schema, using mock data instead"
            exit 0
          fi
          
          echo "Schema imported successfully"
          echo "Database setup completed successfully for tenant $TENANT_ID"
        volumeMounts:
        - name: schema-volume
          mountPath: /schema
        resources:
          limits:
            memory: "128Mi"
            cpu: "100m"
      volumes:
      - name: schema-volume
        configMap:
          name: db-schema
      restartPolicy: Never
  backoffLimit: 3
