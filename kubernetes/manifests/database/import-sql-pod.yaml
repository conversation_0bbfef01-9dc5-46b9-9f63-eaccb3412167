apiVersion: v1
kind: Pod
metadata:
  name: import-sql-pod
  namespace: tenant-check-testnew
spec:
  containers:
  - name: import-sql
    image: amazon/aws-cli:latest
    resources:
      requests:
        cpu: "100m"
        memory: "256Mi"
      limits:
        cpu: "200m"
        memory: "512Mi"
    command:
    - /bin/bash
    - -c
    - |
      # Install MySQL client
      yum install -y mysql

      # Set variables
      TENANT_ID="check-testnew"
      DB_NAME="tenant_check_testnew"
      DB_USER="tenant_check-testnew"
      DB_PASSWORD=$(cat /db-creds/password)
      RDS_HOST="production-architrave-db-new.cpmagwki2kv8.eu-central-1.rds.amazonaws.com"
      RDS_PORT="3306"
      S3_BUCKET="architravetestdb"
      S3_KEY="architrave_1.45.2.sql"

      # Get admin credentials
      RDS_ADMIN_USER=$(cat /admin-creds/username)
      RDS_ADMIN_PASSWORD=$(cat /admin-creds/password)

      echo "Checking database connection..."
      if ! mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SELECT 1" &>/dev/null; then
        echo "Failed to connect to RDS"
        exit 1
      fi

      echo "Successfully connected to RDS"

      # Check if the S3 object exists
      echo "Checking if SQL file exists in S3..."
      if ! aws s3 ls "s3://$S3_BUCKET/$S3_KEY"; then
        echo "SQL file s3://$S3_BUCKET/$S3_KEY does not exist"
        exit 1
      fi

      echo "SQL file exists in S3"

      # Import SQL file directly from S3 to RDS
      echo "Importing SQL file from S3 to RDS..."
      aws s3 cp "s3://$S3_BUCKET/$S3_KEY" - | mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" "$DB_NAME"

      if [ $? -ne 0 ]; then
        echo "Failed to import SQL file from S3 to RDS"
        exit 1
      fi

      echo "SQL file imported successfully"

      # Verify the import was successful by checking for tables
      echo "Verifying database import..."
      TABLE_COUNT=$(mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SELECT COUNT(table_name) FROM information_schema.tables WHERE table_schema='$DB_NAME'" | tail -n 1)

      echo "Tables in database: $TABLE_COUNT"

      # List all tables
      echo "Listing tables in database..."
      mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SHOW TABLES FROM $DB_NAME;"

      # Keep the pod running for debugging
      sleep 3600
    volumeMounts:
    - name: db-creds
      mountPath: /db-creds
    - name: admin-creds
      mountPath: /admin-creds
  volumes:
  - name: db-creds
    secret:
      secretName: db-credentials
  - name: admin-creds
    secret:
      secretName: rds-admin-credentials
