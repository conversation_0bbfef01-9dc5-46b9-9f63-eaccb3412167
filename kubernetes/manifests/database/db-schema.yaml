apiVersion: v1
kind: ConfigMap
metadata:
  name: db-schema
  namespace: tenant-check-test
data:
  schema.sql: |
    CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      username VARCHAR(255) NOT NULL,
      email VARCHAR(255) NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    
    CREATE TABLE IF NOT EXISTS documents (
      id INT AUTO_INCREMENT PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      content TEXT,
      user_id INT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );
    
    -- Insert sample data
    INSERT INTO users (username, email) VALUES
      ('user1', '<EMAIL>'),
      ('user2', '<EMAIL>');
    
    INSERT INTO documents (title, content, user_id) VALUES
      ('Document 1', 'This is the content of document 1', 1),
      ('Document 2', 'This is the content of document 2', 1),
      ('Document 3', 'This is the content of document 3', 2);
