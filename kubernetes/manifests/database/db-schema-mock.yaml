apiVersion: v1
kind: ConfigMap
metadata:
  name: mock-schema
  namespace: tenant-check-test
data:
  schema.sql: |
    -- Create tables for tenant_check_test database
    
    -- Users table
    CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      username VARCHAR(255) NOT NULL,
      email VARCHAR(255) NOT NULL,
      first_name <PERSON><PERSON><PERSON><PERSON>(255),
      last_name <PERSON><PERSON><PERSON><PERSON>(255),
      status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );
    
    -- Documents table
    CREATE TABLE IF NOT EXISTS documents (
      id INT AUTO_INCREMENT PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      file_path VARCHAR(512),
      file_size INT,
      file_type VARCHAR(100),
      user_id INT,
      status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );
    
    -- Categories table
    CREATE TABLE IF NOT EXISTS categories (
      id INT AUTO_INCREMENT PRIMARY KEY,
      name VARCHAR(255) NOT NULL,
      description TEXT,
      parent_id INT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (parent_id) REFERENCES categories(id)
    );
    
    -- Document_categories table (many-to-many relationship)
    CREATE TABLE IF NOT EXISTS document_categories (
      document_id INT,
      category_id INT,
      PRIMARY KEY (document_id, category_id),
      FOREIGN KEY (document_id) REFERENCES documents(id),
      FOREIGN KEY (category_id) REFERENCES categories(id)
    );
    
    -- Comments table
    CREATE TABLE IF NOT EXISTS comments (
      id INT AUTO_INCREMENT PRIMARY KEY,
      content TEXT NOT NULL,
      document_id INT,
      user_id INT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (document_id) REFERENCES documents(id),
      FOREIGN KEY (user_id) REFERENCES users(id)
    );
    
    -- Settings table
    CREATE TABLE IF NOT EXISTS settings (
      id INT AUTO_INCREMENT PRIMARY KEY,
      setting_key VARCHAR(255) NOT NULL,
      setting_value TEXT,
      description TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      UNIQUE KEY (setting_key)
    );
    
    -- Insert sample data
    
    -- Sample users
    INSERT INTO users (username, email, first_name, last_name) VALUES
      ('user1', '<EMAIL>', 'John', 'Doe'),
      ('user2', '<EMAIL>', 'Jane', 'Smith'),
      ('admin', '<EMAIL>', 'Admin', 'User');
    
    -- Sample categories
    INSERT INTO categories (name, description, parent_id) VALUES
      ('Documents', 'All documents', NULL),
      ('Contracts', 'Legal contracts', 1),
      ('Invoices', 'Financial invoices', 1),
      ('Reports', 'Business reports', 1);
    
    -- Sample documents
    INSERT INTO documents (title, description, file_path, file_size, file_type, user_id, status) VALUES
      ('Contract 2023', 'Annual contract for 2023', '/storage/documents/contract_2023.pdf', 1024, 'application/pdf', 1, 'published'),
      ('Invoice #12345', 'Invoice for services', '/storage/documents/invoice_12345.pdf', 512, 'application/pdf', 2, 'published'),
      ('Q1 Report', 'Quarterly report for Q1', '/storage/documents/q1_report.docx', 2048, 'application/msword', 3, 'published');
    
    -- Sample document categories
    INSERT INTO document_categories (document_id, category_id) VALUES
      (1, 2), -- Contract 2023 in Contracts category
      (2, 3), -- Invoice #12345 in Invoices category
      (3, 4); -- Q1 Report in Reports category
    
    -- Sample comments
    INSERT INTO comments (content, document_id, user_id) VALUES
      ('Great contract!', 1, 2),
      ('Please review this invoice', 2, 1),
      ('Excellent report', 3, 1);
    
    -- Sample settings
    INSERT INTO settings (setting_key, setting_value, description) VALUES
      ('site_name', 'Architrave Document Management', 'Name of the site'),
      ('max_upload_size', '10485760', 'Maximum upload size in bytes'),
      ('allowed_file_types', 'pdf,docx,xlsx,jpg,png', 'Allowed file types for upload');
