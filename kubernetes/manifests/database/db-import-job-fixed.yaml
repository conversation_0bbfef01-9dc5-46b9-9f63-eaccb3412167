apiVersion: batch/v1
kind: Job
metadata:
  name: tenant-check-test-db-import-fixed
  namespace: tenant-check-test
spec:
  ttlSecondsAfterFinished: 3600
  template:
    spec:
      serviceAccountName: tenant-check-test-sa
      containers:
      - name: db-import
        image: amazon/aws-cli:latest
        command:
        - /bin/bash
        - -c
        - |
          # Install MySQL client
          yum install -y mysql
          
          # Set variables
          TENANT_ID="check-test"
          DB_NAME="tenant_${TENANT_ID}"
          DB_USER="tenant_${TENANT_ID}"
          DB_PASSWORD="Secure123Password!"
          
          # Get RDS credentials from secret
          RDS_HOST=$(cat /mnt/secrets/rds/host)
          RDS_PORT=$(cat /mnt/secrets/rds/port)
          RDS_ADMIN_USER=$(cat /mnt/secrets/rds/username)
          RDS_ADMIN_PASSWORD=$(cat /mnt/secrets/rds/password)
          
          S3_BUCKET="architravetestdb"
          S3_KEY="architrave_1.45.2.sql"
          
          echo "Creating database schema and user for tenant $TENANT_ID"
          mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" <<EOF
          CREATE DATABASE IF NOT EXISTS \`$DB_NAME\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
          CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASSWORD';
          GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'%';
          FLUSH PRIVILEGES;
          EOF
          
          if [ $? -ne 0 ]; then
            echo "Failed to create database schema and user"
            exit 1
          fi
          
          echo "Database schema and user created successfully"
          
          # Import SQL file directly from S3 to RDS
          echo "Importing SQL file from S3 to RDS"
          echo "Using S3 bucket: $S3_BUCKET"
          echo "Using S3 key: $S3_KEY"
          
          # Check if the S3 object exists
          if ! aws s3 ls "s3://$S3_BUCKET/$S3_KEY"; then
            echo "SQL file s3://$S3_BUCKET/$S3_KEY does not exist"
            exit 1
          fi
          
          # Use the AWS CLI to get the SQL file content and pipe it directly to mysql
          echo "Starting SQL import from S3 to RDS (this may take a few minutes)..."
          aws s3 cp "s3://$S3_BUCKET/$S3_KEY" - | mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" "$DB_NAME"
          
          if [ $? -ne 0 ]; then
            echo "Failed to import SQL file from S3 to RDS"
            exit 1
          fi
          
          # Verify the import was successful by checking for tables
          echo "Verifying database import..."
          TABLE_COUNT=$(mysql -h "$RDS_HOST" -P "$RDS_PORT" -u "$RDS_ADMIN_USER" -p"$RDS_ADMIN_PASSWORD" -e "SELECT COUNT(table_name) FROM information_schema.tables WHERE table_schema='$DB_NAME'" | tail -n 1)
          
          if [ -z "$TABLE_COUNT" ] || [ "$TABLE_COUNT" -eq 0 ]; then
            echo "Database import verification failed: No tables found in the database"
            exit 1
          fi
          
          echo "SQL file imported successfully ($TABLE_COUNT tables created)"
          
          # Update the Kubernetes secret with the database credentials
          kubectl create secret generic db-credentials \
            --namespace=tenant-$TENANT_ID \
            --from-literal=host="$RDS_HOST" \
            --from-literal=port="$RDS_PORT" \
            --from-literal=database="$DB_NAME" \
            --from-literal=username="$DB_USER" \
            --from-literal=password="$DB_PASSWORD" \
            --dry-run=client -o yaml | kubectl apply -f -
          
          echo "Database credentials secret updated successfully"
          echo "Database import completed successfully for tenant $TENANT_ID"
        volumeMounts:
        - name: rds-secrets
          mountPath: /mnt/secrets/rds
          readOnly: true
      volumes:
      - name: rds-secrets
        secret:
          secretName: rds-admin-credentials
      restartPolicy: Never
  backoffLimit: 3
