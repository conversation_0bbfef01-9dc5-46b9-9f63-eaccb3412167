apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: test-aws-secrets-ingress
  namespace: tenant-test-aws-secrets
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:eu-central-1:545009857703:certificate/8d306c2f-62e2-42f4-a941-562ce65cda32
spec:
  rules:
    - host: test-aws-secrets.architrave-assets.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: test-aws-secrets-frontend-service
                port:
                  number: 80 