replicas: 3

audit:
  interval: 60
  logLevel: INFO
  constraintViolationsLimit: 20

validatingWebhook:
  timeoutSeconds: 3
  failurePolicy: Ignore

mutatingWebhook:
  timeoutSeconds: 3
  failurePolicy: Ignore

nodeSelector:
  kubernetes.io/os: linux

tolerations:
  - key: "node-role.kubernetes.io/master"
    operator: "Exists"
    effect: "NoSchedule"

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 500m
    memory: 256Mi

podSecurityPolicy:
  enabled: true

rbac:
  enabled: true 