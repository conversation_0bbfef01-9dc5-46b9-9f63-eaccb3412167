apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-app-cpu-scaler
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  scaleTargetRef:
    name: tenant-app
  minReplicaCount: 2
  maxReplicaCount: 10
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: cpu
    metadata:
      type: Utilization
      value: "70"

---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-app-memory-scaler
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  scaleTargetRef:
    name: tenant-app
  minReplicaCount: 2
  maxReplicaCount: 10
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: memory
    metadata:
      type: Utilization
      value: "70"

---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-queue-processor-scaler
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  scaleTargetRef:
    name: tenant-queue-processor
  minReplicaCount: 1
  maxReplicaCount: 20
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: rabbitmq_queue_messages
      query: sum(rabbitmq_queue_messages{queue="tenant_tasks"})
      threshold: "10"
  - type: rabbitmq
    metadata:
      protocol: http
      host: rabbitmq.tenant-system.svc.cluster.local
      port: "15672"
      queueName: tenant_tasks
      mode: QueueLength
      value: "50"
    authenticationRef:
      name: rabbitmq-trigger-auth

---
apiVersion: keda.sh/v1alpha1
kind: TriggerAuthentication
metadata:
  name: rabbitmq-trigger-auth
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  secretTargetRef:
  - parameter: username
    name: rabbitmq-auth
    key: username
  - parameter: password
    name: rabbitmq-auth
    key: password

---
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: tenant-api-scaler
  namespace: tenant-system
  labels:
    app.kubernetes.io/managed-by: terraform
spec:
  scaleTargetRef:
    name: tenant-api
  minReplicaCount: 2
  maxReplicaCount: 10
  pollingInterval: 15
  cooldownPeriod: 300
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus-server.monitoring.svc.cluster.local:9090
      metricName: http_requests_per_second
      query: sum(rate(istio_requests_total{destination_service_name="tenant-api"}[2m]))
      threshold: "100"
