apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: istio-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  mtls:
    mode: STRICT # Enforce mTLS for all services

---
# Create namespace-specific PeerAuthentication for tenant namespaces
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: tenant-mtls-policy
  namespace: tenant-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  selector:
    matchLabels:
      tenant.architrave.io/tenant-id: "*"
  mtls:
    mode: STRICT # Enforce mTLS for all tenant services

---
# Create default DestinationRule to configure client-side mTLS
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: default-mtls
  namespace: istio-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  host: "*.svc.cluster.local"
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL # Use Istio's mutual TLS

---
# Create DestinationRule for tenant services
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: tenant-services-mtls
  namespace: tenant-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  host: "*.tenant-*.svc.cluster.local"
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL # Use Istio's mutual TLS
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: 30ms
      http:
        http2MaxRequests: 1000
        maxRequestsPerConnection: 10
    outlierDetection:
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 100
