apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  namespace: istio-system
  name: advanced-istio-config
  labels:
    app: istio
    component: istio-operator
    part-of: service-mesh
    managed-by: terraform
spec:
  # Use the default profile as a base
  profile: default
  
  # Configure components with optimized resource requirements
  components:
    base:
      enabled: true
    pilot:
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 500m
            memory: 512Mi
        hpaSpec:
          minReplicas: 1
          maxReplicas: 3
          metrics:
          - type: Resource
            resource:
              name: cpu
              target:
                type: Utilization
                averageUtilization: 80
    
    # Configure ingress gateway with optimized resources
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 500m
            memory: 512Mi
        hpaSpec:
          minReplicas: 1
          maxReplicas: 3
          metrics:
          - type: Resource
            resource:
              name: cpu
              target:
                type: Utilization
                averageUtilization: 80
        service:
          ports:
          - name: status-port
            port: 15021
            targetPort: 15021
          - name: http2
            port: 80
            targetPort: 8080
          - name: https
            port: 443
            targetPort: 8443
          - name: tcp-istiod
            port: 15012
            targetPort: 15012
          - name: tls
            port: 15443
            targetPort: 15443
    
    # Configure egress gateway for outbound traffic control
    egressGateways:
    - name: istio-egressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 200m
            memory: 256Mi
        hpaSpec:
          minReplicas: 1
          maxReplicas: 2
          metrics:
          - type: Resource
            resource:
              name: cpu
              target:
                type: Utilization
                averageUtilization: 80
    
    # Disable CNI to simplify setup
    cni:
      enabled: false
  
  # Advanced mesh configuration
  meshConfig:
    # Enable access logs for debugging
    accessLogFile: /dev/stdout
    
    # Enable tracing with reasonable sampling rate
    enableTracing: true
    defaultConfig:
      tracing:
        sampling: 10 # 10% sampling rate
        zipkin:
          address: jaeger-collector.monitoring:9411
      
      # Configure concurrency for better performance
      concurrency: 2
      
      # Configure health checks
      proxyHealthCheck:
        enabled: true
      
      # Configure outlier detection
      outlierDetection:
        consecutiveErrors: 5
        interval: 30s
        baseEjectionTime: 30s
        maxEjectionPercent: 100
      
      # Configure circuit breaking
      circuitBreakers:
        thresholds:
          - maxConnections: 100
            maxPendingRequests: 100
            maxRequests: 100
            maxRetries: 3
    
    # Enable automatic mutual TLS
    enableAutoMtls: true
    
    # Configure default destination rule
    defaultDestinationRuleExportTo:
      - "*"
    
    # Configure default service export
    defaultServiceExportTo:
      - "*"
    
    # Configure default virtual service export
    defaultVirtualServiceExportTo:
      - "*"
    
    # Configure root namespace for shared resources
    rootNamespace: istio-system
    
    # Configure trust domain for security
    trustDomain: cluster.local
    
    # Configure DNS refresh rate
    dnsRefreshRate: 5s
  
  # Values to further optimize resource usage
  values:
    global:
      # Configure proxy resources
      proxy:
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 200m
            memory: 256Mi
        
        # Configure image pull policy
        imagePullPolicy: IfNotPresent
        
        # Configure logs
        logLevel: warning
        
        # Configure concurrency
        concurrency: 2
      
      # Configure default resources
      defaultResources:
        requests:
          cpu: 50m
          memory: 64Mi
      
      # Configure pod disruption budget
      defaultPodDisruptionBudget:
        enabled: true
        minAvailable: 1
      
      # Configure proxy init resources
      proxy_init:
        resources:
          limits:
            cpu: 100m
            memory: 50Mi
          requests:
            cpu: 10m
            memory: 10Mi
      
      # Configure multi-cluster settings
      multiCluster:
        enabled: false
      
      # Configure control plane security
      controlPlaneSecurityEnabled: true
      
      # Configure pod annotations
      podAnnotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "15020"
      
      # Configure default labels
      defaultLabels:
        app: istio
        part-of: service-mesh
        managed-by: terraform
    
    # Configure pilot (istiod) settings
    pilot:
      # Enable autoscaling
      autoscaleEnabled: true
      autoscaleMin: 1
      autoscaleMax: 3
      
      # Configure resources
      resources:
        requests:
          cpu: 200m
          memory: 256Mi
        limits:
          cpu: 500m
          memory: 512Mi
      
      # Configure environment variables
      env:
        PILOT_ENABLE_PROTOCOL_SNIFFING_FOR_OUTBOUND: "true"
        PILOT_ENABLE_PROTOCOL_SNIFFING_FOR_INBOUND: "true"
        PILOT_ENABLE_ANALYSIS: "true"
        PILOT_ENABLE_STATUS: "true"
        PILOT_TRACE_SAMPLING: "10"
    
    # Configure telemetry
    telemetry:
      enabled: true
      v2:
        enabled: true
        prometheus:
          enabled: true
        stackdriver:
          enabled: false
    
    # Configure sidecar injector
    sidecarInjectorWebhook:
      enableNamespacesByDefault: false
      rewriteAppHTTPProbe: true
    
    # Configure Grafana integration
    grafana:
      enabled: false # We'll use our existing Grafana
    
    # Configure Prometheus integration
    prometheus:
      enabled: false # We'll use our existing Prometheus
    
    # Configure Jaeger integration
    tracing:
      enabled: false # We'll use our existing Jaeger
    
    # Configure Kiali integration
    kiali:
      enabled: true
      dashboard:
        grafanaURL: http://grafana.monitoring:3000
        jaegerURL: http://jaeger-query.monitoring:16686
    
    # Configure gateway settings
    gateways:
      istio-ingressgateway:
        autoscaleEnabled: true
        autoscaleMin: 1
        autoscaleMax: 3
      istio-egressgateway:
        autoscaleEnabled: true
        autoscaleMin: 1
        autoscaleMax: 2
