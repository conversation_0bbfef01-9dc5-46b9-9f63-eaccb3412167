apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: istio-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  mtls:
    mode: STRICT # Enforce mTLS for all services

---
# Create namespace-specific PeerAuthentication for tenant namespaces
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: tenant-mtls-policy
  namespace: tenant-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  selector:
    matchLabels:
      tenant.architrave.io/tenant-id: "*"
  mtls:
    mode: STRICT # Enforce mTLS for all tenant services

---
# Create default DestinationRule to configure client-side mTLS
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: default-mtls
  namespace: istio-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  host: "*.svc.cluster.local"
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL # Use Isti<PERSON>'s mutual TLS

---
# Create namespace-specific mTLS policies for system namespaces
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: system-mtls-policy
  namespace: kube-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  mtls:
    mode: STRICT

---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: monitoring-mtls-policy
  namespace: monitoring
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  mtls:
    mode: STRICT
