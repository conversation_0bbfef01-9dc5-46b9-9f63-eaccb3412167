apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: ${SERVICE_NAME}
  namespace: ${NAMESPACE}
spec:
  hosts:
  - ${SERVICE_HOST}
  gateways:
  - ${GATEWAY_NAME}
  http:
  - match:
    - headers:
        x-canary:
          exact: "true"
    route:
    - destination:
        host: ${SERVICE_NAME}
        subset: canary
        port:
          number: ${SERVICE_PORT}
      weight: 100
  - route:
    - destination:
        host: ${SERVICE_NAME}
        subset: stable
        port:
          number: ${SERVICE_PORT}
      weight: ${STABLE_WEIGHT}
    - destination:
        host: ${SERVICE_NAME}
        subset: canary
        port:
          number: ${SERVICE_PORT}
      weight: ${CANARY_WEIGHT}
---
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: ${SERVICE_NAME}
  namespace: ${NAMESPACE}
spec:
  host: ${SERVICE_NAME}
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http1MaxPendingRequests: 100
        maxRequestsPerConnection: 10
    outlierDetection:
      consecutiveErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 100
  subsets:
  - name: stable
    labels:
      version: stable
  - name: canary
    labels:
      version: canary
