apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-namespace-isolation
  namespace: istio-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  selector:
    matchLabels:
      istio: ingressgateway
  action: ALLOW
  rules:
  - to:
    - operation:
        hosts: ["*.architrave-assets.com"]
---
# Default deny policy for tenant namespaces
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-default-deny
  namespace: tenant-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  selector:
    matchLabels:
      tenant.architrave.io/tenant-id: "*"
  action: DENY
  rules:
  - {}
---
# Allow policy for tenant namespaces - allows traffic from same namespace and istio-system
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-allow-same-namespace
  namespace: tenant-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  selector:
    matchLabels:
      tenant.architrave.io/tenant-id: "*"
  action: ALLOW
  rules:
  # Allow traffic from the same namespace
  - from:
    - source:
        namespaces: ["."]
  # Allow traffic from istio-system (for ingress gateway)
  - from:
    - source:
        namespaces: ["istio-system"]
  # Allow traffic from monitoring namespace
  - from:
    - source:
        namespaces: ["monitoring"]
---
# Allow policy for tenant API services
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-api-allow
  namespace: tenant-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  selector:
    matchLabels:
      app: tenant-api
  action: ALLOW
  rules:
  # Allow traffic from the same namespace
  - from:
    - source:
        namespaces: ["."]
  # Allow traffic from istio-system (for ingress gateway)
  - from:
    - source:
        namespaces: ["istio-system"]
  # Allow traffic to specific API paths
  - to:
    - operation:
        paths: ["/api/v1/*"]
        methods: ["GET", "POST", "PUT", "DELETE"]
---
# Allow policy for tenant frontend services
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-frontend-allow
  namespace: tenant-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  selector:
    matchLabels:
      app: tenant-frontend
  action: ALLOW
  rules:
  # Allow traffic from the same namespace
  - from:
    - source:
        namespaces: ["."]
  # Allow traffic from istio-system (for ingress gateway)
  - from:
    - source:
        namespaces: ["istio-system"]
  # Allow all HTTP methods for frontend
  - to:
    - operation:
        methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD"]
---
# Allow policy for health checks and metrics
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-health-metrics-allow
  namespace: tenant-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  selector:
    matchLabels:
      tenant.architrave.io/tenant-id: "*"
  action: ALLOW
  rules:
  # Allow traffic to health check and metrics endpoints
  - to:
    - operation:
        paths: ["/health", "/ready", "/metrics", "/status"]
        methods: ["GET"]
  # Allow traffic from monitoring namespace
  - from:
    - source:
        namespaces: ["monitoring"]
