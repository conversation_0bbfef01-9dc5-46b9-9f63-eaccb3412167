apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  namespace: istio-system
  name: minimal-istio-install
  labels:
    app: istio
    component: istio-operator
    part-of: security-suite
    managed-by: terraform
spec:
  profile: minimal
  # Use minimal components to reduce resource usage
  components:
    base:
      enabled: true
    pilot:
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
    # Disable all gateways to save resources
    ingressGateways:
    - name: istio-ingressgateway
      enabled: false
    egressGateways:
    - name: istio-egressgateway
      enabled: false
    cni:
      enabled: false
  # Minimal mesh configuration
  meshConfig:
    accessLogFile: /dev/stdout
    enableTracing: false
    defaultConfig:
      tracing:
        sampling: 0
      concurrency: 1
  # Values to further reduce resource usage
  values:
    global:
      proxy:
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
      defaultResources:
        requests:
          cpu: 50m
          memory: 64Mi
      defaultPodDisruptionBudget:
        enabled: false
      defaultLabels:
        app: istio
        part-of: security-suite
        managed-by: terraform
    pilot:
      autoscaleEnabled: false
      resources:
        requests:
          cpu: 100m
          memory: 128Mi
