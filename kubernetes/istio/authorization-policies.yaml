apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: global-deny-policy
  namespace: istio-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  action: DENY
  rules:
  - from:
    - source:
        notNamespaces: ["istio-system", "kube-system", "monitoring"]
    to:
    - operation:
        hosts: ["*.kube-system.svc.cluster.local"]
  - from:
    - source:
        notNamespaces: ["istio-system", "monitoring"]
    to:
    - operation:
        hosts: ["*.istio-system.svc.cluster.local"]
        notPaths: ["/status/*", "/health", "/metrics"]

---
# Default authorization policy for istio-system namespace
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: istio-system-policy
  namespace: istio-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  action: ALLOW
  rules:
  # Allow traffic from anywhere to ingress gateway
  - to:
    - operation:
        hosts: ["istio-ingressgateway.istio-system.svc.cluster.local"]
  # Allow traffic from istio-system
  - from:
    - source:
        namespaces: ["istio-system"]
  # Allow traffic from monitoring namespace
  - from:
    - source:
        namespaces: ["monitoring"]
  # Allow traffic to istiod from anywhere in the cluster
  - to:
    - operation:
        hosts: ["istiod.istio-system.svc.cluster.local"]

---
# Default authorization policy for tenant namespaces
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-namespace-policy
  namespace: tenant-system
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  selector:
    matchLabels:
      tenant.architrave.io/tenant-id: "*"
  action: ALLOW
  rules:
  # Allow traffic from the same tenant namespace
  - from:
    - source:
        namespaces: ["tenant-*"]
        principals: ["cluster.local/ns/tenant-*/sa/*"]
  # Allow traffic from istio-system
  - from:
    - source:
        namespaces: ["istio-system"]
  # Allow traffic from monitoring namespace
  - from:
    - source:
        namespaces: ["monitoring"]
  # Allow traffic to specific paths without authentication (public APIs)
  - to:
    - operation:
        paths: ["/api/public/*", "/health", "/metrics"]

---
# Default authorization policy for monitoring namespace
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: monitoring-namespace-policy
  namespace: monitoring
  labels:
    app: istio
    component: security
    part-of: service-mesh
    managed-by: terraform
spec:
  action: ALLOW
  rules:
  # Allow traffic from istio-system
  - from:
    - source:
        namespaces: ["istio-system"]
  # Allow traffic from monitoring namespace
  - from:
    - source:
        namespaces: ["monitoring"]
  # Allow traffic from tenant namespaces to specific monitoring endpoints
  - from:
    - source:
        namespaces: ["tenant-*"]
    to:
    - operation:
        paths: ["/api/v1/query", "/api/v1/query_range", "/metrics"]
