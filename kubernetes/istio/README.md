# Istio Service Mesh

This directory contains Istio service mesh configurations and resources.

## Overview

Istio is a service mesh that provides traffic management, security, and observability features for microservices. It is used in this infrastructure to provide the following features:

- Traffic management
- Security (mTLS)
- Observability
- Gateway management

## Directory Structure

```
.
└── istio-1.20.0/           # Istio 1.20.0 installation files
    ├── manifest.yaml       # Istio manifest
    ├── manifests/          # Istio manifests
    │   ├── charts/         # Istio Helm charts
    │   ├── examples/       # Example configurations
    │   └── profiles/       # Istio profiles
    └── samples/            # Sample applications and configurations
```

## Installation

Istio is installed using the Istio Operator with the following command:

```bash
kubectl apply -f kubernetes/istio/istio-1.20.0/manifests/charts/istio-operator/templates/crds.yaml
kubectl apply -f kubernetes/istio/istio-1.20.0/manifests/charts/istio-operator/templates/clusterrole.yaml
kubectl apply -f kubernetes/istio/istio-1.20.0/manifests/charts/istio-operator/templates/clusterrole_binding.yaml
kubectl apply -f kubernetes/istio/istio-1.20.0/manifests/charts/istio-operator/templates/service_account.yaml
kubectl apply -f kubernetes/istio/istio-1.20.0/manifests/charts/istio-operator/templates/deployment.yaml
kubectl apply -f kubernetes/istio/istio-1.20.0/manifests/charts/istio-operator/templates/service.yaml
```

## Configuration

Istio is configured using the following resources:

### Gateway

Gateways define entry points into the service mesh. They are configured using the following example:

```yaml
apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  name: tenant-gateway
  namespace: tenant-namespace
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "tenant.example.com"
```

### Virtual Service

Virtual Services define routing rules for traffic. They are configured using the following example:

```yaml
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: tenant-service
  namespace: tenant-namespace
spec:
  hosts:
  - "tenant.example.com"
  gateways:
  - tenant-gateway
  http:
  - route:
    - destination:
        host: tenant-service
        port:
          number: 80
```

### Destination Rule

Destination Rules define policies that apply to traffic intended for a service. They are configured using the following example:

```yaml
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: tenant-service
  namespace: tenant-namespace
spec:
  host: tenant-service
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL
```

### Authorization Policy

Authorization Policies define access control for services. They are configured using the following example:

```yaml
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: tenant-policy
  namespace: tenant-namespace
spec:
  selector:
    matchLabels:
      app: tenant-service
  rules:
  - from:
    - source:
        namespaces: ["tenant-namespace"]
    to:
    - operation:
        methods: ["GET", "POST"]
```

## mTLS

Mutual TLS (mTLS) is enabled for all services in the mesh using the following configuration:

```yaml
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: istio-system
spec:
  mtls:
    mode: STRICT
```

## Observability

Istio provides observability features through the following components:

- Prometheus for metrics collection
- Grafana for visualization
- Kiali for service mesh visualization
- Jaeger for distributed tracing

These components are installed using the following commands:

```bash
kubectl apply -f kubernetes/istio/istio-1.20.0/samples/addons/prometheus.yaml
kubectl apply -f kubernetes/istio/istio-1.20.0/samples/addons/grafana.yaml
kubectl apply -f kubernetes/istio/istio-1.20.0/samples/addons/kiali.yaml
kubectl apply -f kubernetes/istio/istio-1.20.0/samples/addons/jaeger.yaml
```

## Tenant Integration

Each tenant is integrated with Istio using the following resources:

- Gateway for tenant-specific ingress
- Virtual Service for tenant-specific routing
- Destination Rule for tenant-specific traffic policy
- Authorization Policy for tenant-specific access control

These resources are created during tenant onboarding and removed during tenant offboarding.
