apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tenant-gateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    managed-by: terraform
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*.architrave-assets.com"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
    - "*.architrave-assets.com"
    tls:
      mode: SIMPLE
      credentialName: architrave-assets-wildcard-cert

---
# Default VirtualService for tenant routing
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-routing
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    managed-by: terraform
spec:
  hosts:
  - "*.architrave-assets.com"
  gateways:
  - tenant-gateway
  http:
  - match:
    - uri:
        prefix: "/api"
      authority:
        regex: "([a-z0-9-]+)\\.architrave-assets\\.com"
    route:
    - destination:
        host: "tenant-$1-api.tenant-$1.svc.cluster.local"
        port:
          number: 80
      timeout: 30s
      retries:
        attempts: 3
        perTryTimeout: 2s
        retryOn: "gateway-error,connect-failure,refused-stream"
  - match:
    - authority:
        regex: "([a-z0-9-]+)\\.architrave-assets\\.com"
    route:
    - destination:
        host: "tenant-$1-app.tenant-$1.svc.cluster.local"
        port:
          number: 80
      timeout: 30s
      retries:
        attempts: 3
        perTryTimeout: 2s
        retryOn: "gateway-error,connect-failure,refused-stream"

---
# Create a script to fetch and store the ACM certificate
apiVersion: v1
kind: ConfigMap
metadata:
  name: certificate-sync-script
  namespace: istio-system
data:
  sync-cert.sh: |
    #!/bin/bash
    set -e
    
    # Get the ARN of the wildcard certificate for architrave-assets.com
    CERT_ARN=$(aws acm list-certificates --query "CertificateSummaryList[?DomainName=='*.architrave-assets.com'].CertificateArn" --output text)
    
    if [ -z "$CERT_ARN" ]; then
      echo "No certificate found for *.architrave-assets.com"
      exit 1
    fi
    
    echo "Found certificate ARN: $CERT_ARN"
    
    # Get the certificate data
    CERT_DATA=$(aws acm get-certificate --certificate-arn $CERT_ARN)
    CERTIFICATE=$(echo $CERT_DATA | jq -r '.Certificate')
    CHAIN=$(echo $CERT_DATA | jq -r '.CertificateChain')
    
    # Create a temporary directory
    TEMP_DIR=$(mktemp -d)
    
    # Write certificate and chain to files
    echo "$CERTIFICATE" > $TEMP_DIR/tls.crt
    echo "$CHAIN" > $TEMP_DIR/ca.crt
    
    # Create or update the Kubernetes secret
    kubectl create secret tls architrave-assets-wildcard-cert \
      --cert=$TEMP_DIR/tls.crt \
      --key=/etc/ssl/private/tls.key \
      -n istio-system \
      --dry-run=client -o yaml | kubectl apply -f -
    
    # Clean up
    rm -rf $TEMP_DIR
    
    echo "Certificate synced successfully"

---
# Create a CronJob to sync the certificate periodically
apiVersion: batch/v1
kind: CronJob
metadata:
  name: certificate-sync
  namespace: istio-system
spec:
  schedule: "0 0 * * *"  # Run daily at midnight
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: certificate-sync-sa
          containers:
          - name: certificate-sync
            image: amazon/aws-cli:latest
            command:
            - /bin/bash
            - -c
            - /scripts/sync-cert.sh
            volumeMounts:
            - name: scripts
              mountPath: /scripts
            - name: ssl-key
              mountPath: /etc/ssl/private
          volumes:
          - name: scripts
            configMap:
              name: certificate-sync-script
              defaultMode: 0755
          - name: ssl-key
            secret:
              secretName: ssl-private-key
          restartPolicy: OnFailure
