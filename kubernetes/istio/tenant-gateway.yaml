apiVersion: networking.istio.io/v1beta1
kind: Gateway
metadata:
  name: tenant-gateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    managed-by: terraform
spec:
  selector:
    istio: ingressgateway
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "*.architrave.com"
    - "*.architrave-assets.com"
    tls:
      httpsRedirect: true
  - port:
      number: 443
      name: https
      protocol: HTTPS
    hosts:
    - "*.architrave.com"
    tls:
      mode: SIMPLE
      credentialName: wildcard-cert
  - port:
      number: 443
      name: https-assets
      protocol: HTTPS
    hosts:
    - "*.architrave-assets.com"
    tls:
      mode: SIMPLE
      credentialName: architrave-assets-wildcard-cert
---
# Default VirtualService for tenant routing
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-routing
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    managed-by: terraform
spec:
  hosts:
  - "*.architrave.com"
  gateways:
  - tenant-gateway
  http:
  - match:
    - uri:
        prefix: "/api"
      authority:
        regex: "([a-z0-9-]+)\\.architrave\\.com"
    route:
    - destination:
        host: "tenant-$1-api.tenant-$1.svc.cluster.local"
        port:
          number: 80
    retries:
      attempts: 3
      perTryTimeout: 2s
      retryOn: "gateway-error,connect-failure,refused-stream"
    timeout: 30s
  - match:
    - authority:
        regex: "([a-z0-9-]+)\\.architrave\\.com"
    route:
    - destination:
        host: "tenant-$1-app.tenant-$1.svc.cluster.local"
        port:
          number: 80
    retries:
      attempts: 3
      perTryTimeout: 2s
      retryOn: "gateway-error,connect-failure,refused-stream"
    timeout: 30s
---
# VirtualService for architrave-assets.com domain
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: tenant-routing-assets
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    managed-by: terraform
spec:
  hosts:
  - "*.architrave-assets.com"
  gateways:
  - tenant-gateway
  http:
  - match:
    - uri:
        prefix: "/api"
      authority:
        regex: "([a-z0-9-]+)\\.architrave-assets\\.com"
    route:
    - destination:
        host: "tenant-$1-api.tenant-$1.svc.cluster.local"
        port:
          number: 80
    retries:
      attempts: 3
      perTryTimeout: 2s
      retryOn: "gateway-error,connect-failure,refused-stream"
    timeout: 30s
  - match:
    - authority:
        regex: "([a-z0-9-]+)\\.architrave-assets\\.com"
    route:
    - destination:
        host: "tenant-$1-app.tenant-$1.svc.cluster.local"
        port:
          number: 80
    retries:
      attempts: 3
      perTryTimeout: 2s
      retryOn: "gateway-error,connect-failure,refused-stream"
    timeout: 30s
---
# Service Entry for external services
apiVersion: networking.istio.io/v1beta1
kind: ServiceEntry
metadata:
  name: external-apis
  namespace: istio-system
  labels:
    managed-by: terraform
spec:
  hosts:
  - "api.external-service.com"
  - "auth.external-service.com"
  - "storage.external-service.com"
  location: MESH_EXTERNAL
  ports:
  - number: 443
    name: https
    protocol: HTTPS
  resolution: DNS
  exportTo:
  - "."
---
# Circuit Breaker configuration
apiVersion: networking.istio.io/v1beta1
kind: DestinationRule
metadata:
  name: circuit-breaker
  namespace: istio-system
  labels:
    managed-by: terraform
spec:
  host: "*.svc.cluster.local"
  trafficPolicy:
    connectionPool:
      tcp:
        maxConnections: 100
        connectTimeout: 30ms
      http:
        http2MaxRequests: 1000
        maxRequestsPerConnection: 10
        maxPendingRequests: 1000
        maxRetries: 3
    outlierDetection:
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
      maxEjectionPercent: 100
    loadBalancer:
      simple: ROUND_ROBIN
    tls:
      mode: ISTIO_MUTUAL
---
# Global mTLS policy
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: istio-system
  labels:
    managed-by: terraform
spec:
  mtls:
    mode: PERMISSIVE
