apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  namespace: istio-system
  name: minimal-istio-config
  labels:
    app: istio
    component: istio-operator
    part-of: security-suite
    managed-by: terraform
spec:
  # Use the minimal profile to reduce resource usage
  profile: minimal
  
  # Configure components with minimal resource requirements
  components:
    base:
      enabled: true
    pilot:
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
    
    # Configure gateways with minimal resources
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
      k8s:
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 200m
            memory: 256Mi
        hpaSpec:
          minReplicas: 1
          maxReplicas: 2
          metrics:
          - type: Resource
            resource:
              name: cpu
              target:
                type: Utilization
                averageUtilization: 80
    
    # Disable egress gateway to save resources
    egressGateways:
    - name: istio-egressgateway
      enabled: false
    
    # Disable CNI to simplify setup
    cni:
      enabled: false
  
  # Minimal mesh configuration
  meshConfig:
    accessLogFile: /dev/stdout
    enableTracing: false
    defaultConfig:
      tracing:
        sampling: 0
      concurrency: 1
  
  # Values to further reduce resource usage
  values:
    global:
      proxy:
        resources:
          requests:
            cpu: 50m
            memory: 64Mi
          limits:
            cpu: 100m
            memory: 128Mi
      defaultResources:
        requests:
          cpu: 50m
          memory: 64Mi
      defaultPodDisruptionBudget:
        enabled: true
      defaultLabels:
        app: istio
        part-of: security-suite
        managed-by: terraform
    
    pilot:
      autoscaleEnabled: true
      autoscaleMin: 1
      autoscaleMax: 2
      resources:
        requests:
          cpu: 100m
          memory: 128Mi
        limits:
          cpu: 200m
          memory: 256Mi
