# Istio plugin CA sample certificates

This directory contains sample pre-generated certificate and keys to demonstrate how an operator could configure Citadel with an existing root certificate, signing certificates and keys. In such
a deployment, Citadel acts as an intermediate certificate authority (CA), under the given root CA.
Instructions are available [here](https://istio.io/docs/tasks/security/cert-management/plugin-ca-cert/).

The included sample files are:

- `root-cert.pem`: root CA certificate.
- `root-cert-alt.pem`: alternative CA certificate.
- `ca-[cert|key].pem`: Citadel intermediate certificate and corresponding private key.
- `ca-[cert-alt|key-alt].pem`: alternative intermediate certificate and corresponding private key.
- `cert-chain.pem`: certificate trust chain.
- `cert-chain-alt.pem`: alternative certificate chain.
- `workload-foo-[cert|key].pem`: workload certificate and key for URI SAN `spiffe://trust-domain-foo/ns/foo/sa/foo` signed by `ca-cert.key`.
- `workload-bar-[cert|key].pem`: workload certificate and key for URI SAN `spiffe://trust-domain-bar/ns/bar/sa/bar` signed by `ca-cert.key`.
- `workload-foo-root-certs.pem`: root and intermediate CA certificates for foo workload certificate.
- `workload-bar-root-certs.pem`: root and intermediate CA certificates for bar workload certificate.
- `leaf-workload-foo-cert.pem`: leaf workload certificate for URI SAN `spiffe://trust-domain-foo/ns/foo/sa/foo`.
- `leaf-workload-bar-cert.pem`: leaf workload certificate for URI SAN `spiffe://trust-domain-bar/ns/bar/sa/bar`.

The workload cert and key are generated by:

```shell script
 ./generate-workload.sh foo
 ./generate-workload.sh bar
```

To generate certs signed by the alternative root `root-cert-alt.pem`

```shell script
./generate-workload.sh name namespace serviceAccount tmpDir use-alternative-root
./generate-workload.sh name namespace serviceAccount tmpDir use-alternative-root
```
