{{- if .Values.global.remotePilotAddress }}
apiVersion: v1
kind: Service
metadata:
  {{- if .Values.pilot.enabled }}
  # when local istiod is enabled, we can't use istiod service name to reach the remote control plane
  name: istiod-remote
  {{- else }}
  # when local istiod isn't enabled, we can use istiod service name to reach the remote control plane
  name: istiod
  {{- end }}
  namespace: {{ .Release.Namespace }}
spec:
  ports:
  - port: 15012
    name: tcp-istiod
    protocol: TCP
  - port: 443
    targetPort: 15017
    name: tcp-webhook
    protocol: TCP
  {{- if not (regexMatch "^([0-9]*\\.){3}[0-9]*$" .Values.global.remotePilotAddress) }}
  # if the remotePilotAddress is not an IP addr, we use ExternalName
  type: ExternalName
  externalName: {{ .Values.global.remotePilotAddress }}
  {{- end }}
{{- if .Values.global.ipFamilyPolicy }}
  ipFamilyPolicy: {{ .Values.global.ipFamilyPolicy }}
{{- end }}
{{- if .Values.global.ipFamilies }}
  ipFamilies:
{{- range .Values.global.ipFamilies }}
  - {{ . }}
{{- end }}
{{- end }}
---
{{- end }}
