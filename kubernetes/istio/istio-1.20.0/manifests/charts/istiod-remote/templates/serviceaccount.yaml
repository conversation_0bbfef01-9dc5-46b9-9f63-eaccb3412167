{{- if .Values.global.configCluster }}
apiVersion: v1
kind: ServiceAccount
  {{- if .Values.global.imagePullSecrets }}
imagePullSecrets:
  {{- range .Values.global.imagePullSecrets }}
  - name: {{ . }}
  {{- end }}
  {{- end }}
metadata:
  name: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  namespace: {{ .Values.global.istioNamespace }}
  labels:
    app: istiod
    release: {{ .Release.Name }}
---
{{- end }}
