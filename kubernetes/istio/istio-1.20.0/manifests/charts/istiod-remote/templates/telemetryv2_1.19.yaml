{{- if and .Values.telemetry.enabled .Values.telemetry.v2.enabled }}
{{ $prom := not (include "prometheus" . | eq "true") }}
{{ $sdMetrics := not (include "sd-metrics" . | eq "true") }}
{{ $sdLogs := not (include "sd-logs" . | eq "true") }}
---
# Note: http stats filter is wasm enabled only in sidecars.
{{- if and .Values.telemetry.v2.prometheus.enabled $prom }}
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: stats-filter-1.19{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  {{- if .Values.meshConfig.rootNamespace }}
  namespace: {{ .Values.meshConfig.rootNamespace }}
  {{- else }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels:
    istio.io/rev: {{ .Values.revision | default "default" }}
spec:
  priority: -1
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.19.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/stats.PluginConfig
            value:
              {{- if not .Values.telemetry.v2.prometheus.configOverride.outboundSidecar }}
              {}
              {{- else }}
              {{ toJson .Values.telemetry.v2.prometheus.configOverride.outboundSidecar | indent 18 }}
              {{- end }}
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.19.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/stats.PluginConfig
            value:
              {{- if not .Values.telemetry.v2.prometheus.configOverride.inboundSidecar }}
              {
                "disable_host_header_fallback": true
              }
              {{- else }}
              {{ toJson .Values.telemetry.v2.prometheus.configOverride.inboundSidecar | indent 18 }}
              {{- end }}
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.19.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/stats.PluginConfig
            value:
              {{- if not .Values.telemetry.v2.prometheus.configOverride.gateway }}
              {
                "disable_host_header_fallback": true
              }
              {{- else }}
              {{ toJson .Values.telemetry.v2.prometheus.configOverride.gateway | indent 18 }}
              {{- end }}
---
# Note: tcp stats filter is wasm enabled only in sidecars.
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-stats-filter-1.19{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  {{- if .Values.meshConfig.rootNamespace }}
  namespace: {{ .Values.meshConfig.rootNamespace }}
  {{- else }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels:
    istio.io/rev: {{ .Values.revision | default "default" }}
spec:
  priority: -1
  configPatches:
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.19.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/stats.PluginConfig
            value:
              {{- if not .Values.telemetry.v2.prometheus.configOverride.inboundSidecar }}
              {}
              {{- else }}
              {{ toJson .Values.telemetry.v2.prometheus.configOverride.inboundSidecar | indent 18 }}
              {{- end }}
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.19.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/stats.PluginConfig
            value:
              {{- if not .Values.telemetry.v2.prometheus.configOverride.outboundSidecar }}
              {}
              {{- else }}
              {{ toJson .Values.telemetry.v2.prometheus.configOverride.outboundSidecar | indent 18 }}
              {{- end }}
    - applyTo: NETWORK_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.19.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/stats.PluginConfig
            value:
              {{- if not .Values.telemetry.v2.prometheus.configOverride.gateway }}
              {}
              {{- else }}
              {{ toJson .Values.telemetry.v2.prometheus.configOverride.gateway | indent 18 }}
              {{- end }}
---
{{- end }}
{{/*TODO: this is broken, we do not handle the split quite right! */}}
{{- if and .Values.telemetry.v2.stackdriver.enabled $sdLogs $sdMetrics }}
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: stackdriver-filter-1.19{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  {{- if .Values.meshConfig.rootNamespace }}
  namespace: {{ .Values.meshConfig.rootNamespace }}
  {{- else }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels:
    istio.io/rev: {{ .Values.revision | default "default" }}
spec:
  priority: -1
  configPatches:
{{- if not .Values.telemetry.v2.stackdriver.disableOutbound }}
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.19.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stackdriver
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stackdriver_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {{- if not .Values.telemetry.v2.stackdriver.configOverride }}
                    {
                      "access_logging": "{{ .Values.telemetry.v2.stackdriver.outboundAccessLogging }}",
                      "metric_expiry_duration": "3600s"
                    }
                    {{- else }}
                    {{ toJson .Values.telemetry.v2.stackdriver.configOverride | indent 18 }}
                    {{- end }}
                vm_config:
                  vm_id: stackdriver_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local: { inline_string: envoy.wasm.null.stackdriver }
{{- end }}
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.19.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stackdriver
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stackdriver_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {{- if not .Values.telemetry.v2.stackdriver.configOverride }}
                    {
                      "disable_server_access_logging": {{ not .Values.telemetry.v2.stackdriver.logging }},
                      "access_logging": "{{ .Values.telemetry.v2.stackdriver.inboundAccessLogging }}",
                      "disable_host_header_fallback": true,
                      "metric_expiry_duration": "3600s"
                    }
                    {{- else }}
                    {{ toJson .Values.telemetry.v2.stackdriver.configOverride | indent 18 }}
                    {{- end }}
                vm_config:
                  vm_id: stackdriver_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local: { inline_string: envoy.wasm.null.stackdriver }
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.19.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stackdriver
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stackdriver_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {{- if not .Values.telemetry.v2.stackdriver.configOverride }}
                    {
                      "access_logging": "{{ .Values.telemetry.v2.stackdriver.outboundAccessLogging }}",
                      "disable_host_header_fallback": true,
                      "metric_expiry_duration": "3600s"
                    }
                    {{- else }}
                    {{ toJson .Values.telemetry.v2.stackdriver.configOverride | indent 18 }}
                    {{- end }}
                vm_config:
                  vm_id: stackdriver_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local: { inline_string: envoy.wasm.null.stackdriver }
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-stackdriver-filter-1.19{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  {{- if .Values.meshConfig.rootNamespace }}
  namespace: {{ .Values.meshConfig.rootNamespace }}
  {{- else }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels:
    istio.io/rev: {{ .Values.revision | default "default" }}
spec:
  priority: -1
  configPatches:
  {{- if not .Values.telemetry.v2.stackdriver.disableOutbound }}
  - applyTo: NETWORK_FILTER
    match:
      context: SIDECAR_OUTBOUND
      proxy:
        proxyVersion: '^1\.19.*'
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.tcp_proxy"
    patch:
      operation: INSERT_BEFORE
      value:
        name: istio.stackdriver
        typed_config:
          "@type": type.googleapis.com/udpa.type.v1.TypedStruct
          type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
          value:
            config:
              root_id: stackdriver_outbound
              configuration:
                "@type": "type.googleapis.com/google.protobuf.StringValue"
                value: |
                  {{- if not .Values.telemetry.v2.stackdriver.configOverride }}
                  {
                    "access_logging": "{{ .Values.telemetry.v2.stackdriver.outboundAccessLogging }}",
                    "metric_expiry_duration": "3600s"
                  }
                  {{- else }}
                  {{ toJson .Values.telemetry.v2.stackdriver.configOverride | indent 18 }}
                  {{- end }}
              vm_config:
                vm_id: stackdriver_outbound
                runtime: envoy.wasm.runtime.null
                code:
                  local: { inline_string: envoy.wasm.null.stackdriver }
    {{- end }}
  - applyTo: NETWORK_FILTER
    match:
      context: SIDECAR_INBOUND
      proxy:
        proxyVersion: '^1\.19.*'
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.tcp_proxy"
    patch:
      operation: INSERT_BEFORE
      value:
        name: istio.stackdriver
        typed_config:
          "@type": type.googleapis.com/udpa.type.v1.TypedStruct
          type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
          value:
            config:
              root_id: stackdriver_inbound
              configuration:
                "@type": "type.googleapis.com/google.protobuf.StringValue"
                value: |
                  {{- if not .Values.telemetry.v2.stackdriver.configOverride }}
                  {
                    "disable_server_access_logging": {{ not .Values.telemetry.v2.stackdriver.logging }},
                    "access_logging": "{{ .Values.telemetry.v2.stackdriver.inboundAccessLogging }}",
                    "metric_expiry_duration": "3600s"
                  }
                  {{- else }}
                  {{ toJson .Values.telemetry.v2.stackdriver.configOverride | indent 18 }}
                  {{- end }}
              vm_config:
                vm_id: stackdriver_inbound
                runtime: envoy.wasm.runtime.null
                code:
                  local: { inline_string: envoy.wasm.null.stackdriver }
  - applyTo: NETWORK_FILTER
    match:
      context: GATEWAY
      proxy:
        proxyVersion: '^1\.19.*'
      listener:
        filterChain:
          filter:
            name: "envoy.filters.network.tcp_proxy"
    patch:
      operation: INSERT_BEFORE
      value:
        name: istio.stackdriver
        typed_config:
          "@type": type.googleapis.com/udpa.type.v1.TypedStruct
          type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
          value:
            config:
              root_id: stackdriver_outbound
              configuration:
                "@type": "type.googleapis.com/google.protobuf.StringValue"
                value: |
                  {{- if not .Values.telemetry.v2.stackdriver.configOverride }}
                  {
                    "access_logging": "{{ .Values.telemetry.v2.stackdriver.outboundAccessLogging }}",
                    "metric_expiry_duration": "3600s"
                  }
                  {{- else }}
                  {{ toJson .Values.telemetry.v2.stackdriver.configOverride | indent 18 }}
                  {{- end }}
              vm_config:
                vm_id: stackdriver_outbound
                runtime: envoy.wasm.runtime.null
                code:
                  local: { inline_string: envoy.wasm.null.stackdriver }
---
{{- if .Values.telemetry.v2.accessLogPolicy.enabled }}
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: stackdriver-sampling-accesslog-filter-1.19{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  {{- if .Values.meshConfig.rootNamespace }}
  namespace: {{ .Values.meshConfig.rootNamespace }}
  {{- else }}
  namespace: {{ .Release.Namespace }}
  {{- end }}
  labels:
    istio.io/rev: {{ .Values.revision | default "default" }}
spec:
  priority: -1
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '1\.19.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "istio.stackdriver"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.access_log
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "log_window_duration": "{{ .Values.telemetry.v2.accessLogPolicy.logWindowDuration }}"
                    }
                vm_config:
                  runtime: envoy.wasm.runtime.null
                  code:
                    local: { inline_string: "envoy.wasm.access_log_policy" }
---
{{- end }}
{{- end }}
{{- end }}
