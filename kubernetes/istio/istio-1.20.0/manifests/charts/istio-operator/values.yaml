hub: docker.io/istio
tag: 1.20.0

# ImagePullSecrets for operator ServiceAccount, list of secrets in the same namespace
# used to pull operator image. Must be set for any cluster configured with private docker registry.
imagePullSecrets: []

# Specify image pull policy if default behavior isn't desired.
# Default behavior: latest images will be Always else IfNotPresent.
imagePullPolicy: ""

# Used to replace istioNamespace to support operator watch multiple namespaces.
watchedNamespaces: istio-system
waitForResourcesTimeout: 300s

# Used for helm2 to add the CRDs to templates.
enableCRDTemplates: false

# revision for the operator resources
revision: ""

# The number of old ReplicaSets to retain in operator deployment
deploymentHistory: 10

# Operator resource defaults
operator:
  monitoring:
    host: 127.0.0.1
    port: 15014
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 128Mi
  # Set to `type: RuntimeDefault` to use the default profile if available.
  seccompProfile: {}

# Node labels for pod assignment
nodeSelector: {}

# Tolerations for pod assignment
tolerations: []

# Affinity for pod assignment
affinity: {}

# Additional labels and annotations to apply on the pod level for monitoring and logging configuration.
podLabels: {}
podAnnotations: {}
