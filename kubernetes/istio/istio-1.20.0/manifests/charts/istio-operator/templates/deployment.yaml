apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{.Release.Namespace}}
  name: istio-operator{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
spec:
  replicas: 1
  revisionHistoryLimit: {{ .Values.deploymentHistory }}
  selector:
    matchLabels:
      name: istio-operator
  template:
    metadata:
      labels:
        name: istio-operator
        {{- range $key, $val := .Values.podLabels }}
        {{ $key }}: "{{ $val }}"
        {{- end }}
      annotations:
        prometheus.io/port: "{{ .Values.operator.monitoring.port }}"
        prometheus.io/scrape: "true"
    {{- if .Values.podAnnotations }}
{{ toYaml .Values.podAnnotations | indent 8 }}
    {{- end }}
    spec:
      serviceAccountName: istio-operator{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
      containers:
        - name: istio-operator
          image: {{.Values.hub}}/operator:{{.Values.tag}}
          command:
          - operator
          - server
          - --monitoring-host={{ .Values.operator.monitoring.host }}
          - --monitoring-port={{ .Values.operator.monitoring.port }}
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
{{- if .Values.operator.seccompProfile }}
            seccompProfile:
{{ toYaml .Values.operator.seccompProfile | trim | indent 14 }}
{{- end }}
{{- if .Values.imagePullPolicy }}
          imagePullPolicy: {{ .Values.imagePullPolicy }}
{{- end }}
          resources:
{{ toYaml .Values.operator.resources | trim | indent 12 }}
          env:
            - name: WATCH_NAMESPACE
              value: {{.Values.watchedNamespaces | quote}}
            - name: LEADER_ELECTION_NAMESPACE
              value: {{.Release.Namespace | quote}}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: OPERATOR_NAME
              value: {{.Release.Namespace | quote}}
            - name: WAIT_FOR_RESOURCES_TIMEOUT
              value: {{.Values.waitForResourcesTimeout | quote}}
            - name: REVISION
              value: {{.Values.revision | quote}}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
---
