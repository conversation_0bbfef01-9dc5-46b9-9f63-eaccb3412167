apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: ztunnel
  namespace: {{ .Release.Namespace }}
  labels:
    {{- .Values.labels | toYaml | nindent 4}}
  annotations:
    {{- .Values.annotations | toYaml | nindent 4 }}
spec:
  updateStrategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: ztunnel
  template:
    metadata:
      labels:
        sidecar.istio.io/inject: "false"
        app: ztunnel
{{ with .Values.podLabels -}}{{ toYaml . | indent 8 }}{{ end }}
      annotations:
        cni.projectcalico.org/allowedSourcePrefixes: "[\"0.0.0.0/0\"]"
        ambient.istio.io/redirection: disabled
        sidecar.istio.io/inject: "false"
{{ with .Values.podAnnotations -}}{{ toYaml . | indent 8 }}{{ end }}
    spec:
      nodeSelector:
        kubernetes.io/os: linux
      serviceAccountName: ztunnel
      tolerations:
        - effect: NoSchedule
          operator: Exists
        - key: CriticalAddonsOnly
          operator: Exists
        - effect: NoExecute
          operator: Exists
      containers:
      - name: istio-proxy
{{- if contains "/" .Values.image }}
        image: "{{ .Values.image }}"
{{- else }}
        image: "{{ .Values.hub }}/{{ .Values.image | default "ztunnel" }}:{{ .Values.tag }}{{with (.Values.variant )}}-{{.}}{{end}}"
{{- end }}
        ports:
        - containerPort: 15020
          name: ztunnel-stats
          protocol: TCP
        resources:
{{- if .Values.resources }}
{{ toYaml .Values.resources | trim | indent 10 }}
{{- end }}
{{- with .Values.imagePullPolicy }}
        imagePullPolicy: {{ . }}
{{- end }}
        securityContext:
          allowPrivilegeEscalation: false
          privileged: false
          capabilities:
            drop:
            - ALL
            add:
            - NET_ADMIN
          readOnlyRootFilesystem: true
          runAsGroup: 1337
          runAsNonRoot: false
          runAsUser: 0
        readinessProbe:
          httpGet:
            port: 15021
            path: /healthz/ready
        args:
        - proxy
        - ztunnel
        env:
        - name: CA_ADDRESS
        {{- if .Values.caAddress }}
          value: {{ .Values.caAddress }}
        {{- else }}
          value: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}.{{ .Values.istioNamespace }}.svc:15012
        {{- end }}
        - name: XDS_ADDRESS
          value: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}.{{ .Values.istioNamespace }}.svc:15012
        - name: CLUSTER_ID
          value: {{ .Values.multiCluster.clusterName | default "Kubernetes" }}
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              fieldPath: spec.nodeName
        - name: INSTANCE_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: SERVICE_ACCOUNT
          valueFrom:
            fieldRef:
              fieldPath: spec.serviceAccountName
        {{- if .Values.meshConfig.defaultConfig.proxyMetadata }}
        {{- range $key, $value := .Values.meshConfig.defaultConfig.proxyMetadata}}
        - name: {{ $key }}
          value: "{{ $value }}"
        {{- end }}
        {{- end }}
        {{- with .Values.env }}
        {{- range $key, $val := . }}
        - name: {{ $key }}
          value: "{{ $val }}"
        {{- end }}
        {{- end }}
        volumeMounts:
        - mountPath: /var/run/secrets/istio
          name: istiod-ca-cert
        - mountPath: /var/run/secrets/tokens
          name: istio-token
        {{- with .Values.volumeMounts }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      volumes:
      - name: istio-token
        projected:
          sources:
          - serviceAccountToken:
              path: istio-token
              expirationSeconds: 43200
              audience: istio-ca
      - name: istiod-ca-cert
        configMap:
          name: istio-ca-root-cert
      {{- with .Values.volumes }}
        {{- toYaml . | nindent 6}}
      {{- end }}