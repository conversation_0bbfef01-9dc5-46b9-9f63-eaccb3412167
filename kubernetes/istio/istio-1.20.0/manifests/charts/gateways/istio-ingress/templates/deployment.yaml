{{- $gateway := index .Values "gateways" "istio-ingressgateway" }}
{{- if eq $gateway.injectionTemplate "" }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $gateway.name }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ $gateway.labels | toYaml | indent 4 }}
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "IngressGateways"
spec:
{{- if not $gateway.autoscaleEnabled }}
{{- if $gateway.replicaCount }}
  replicas: {{ $gateway.replicaCount }}
{{- end }}
{{- end }}
  selector:
    matchLabels:
{{ $gateway.labels | toYaml | indent 6 }}
  strategy:
    rollingUpdate:
      maxSurge: {{ $gateway.rollingMaxSurge }}
      maxUnavailable: {{ $gateway.rollingMaxUnavailable }}
  template:
    metadata:
      labels:
{{ $gateway.labels | toYaml | indent 8 }}
{{- if eq .Release.Namespace "istio-system"}}
        heritage: Tiller
        release: istio
        chart: gateways
{{- end }}
        service.istio.io/canonical-name: {{ $gateway.name }}
        service.istio.io/canonical-revision: {{ index $gateway.labels "app.kubernetes.io/version" | default (index $gateway.labels "version") | default .Values.revision | default "latest" }}
        istio.io/rev: {{ .Values.revision | default "default" }}
        install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
        operator.istio.io/component: "IngressGateways"
        sidecar.istio.io/inject: "false"
      annotations:
        istio.io/rev: {{ .Values.revision | default "default" }}
        {{- if .Values.meshConfig.enablePrometheusMerge }}
        prometheus.io/port: "15020"
        prometheus.io/scrape: "true"
        prometheus.io/path: "/stats/prometheus"
        {{- end }}
        sidecar.istio.io/inject: "false"
{{- if $gateway.podAnnotations }}
{{ toYaml $gateway.podAnnotations | indent 8 }}
{{ end }}
    spec:
{{- if not $gateway.runAsRoot }}
      securityContext:
{{- if not (eq .Values.global.platform "openshift") }}
        runAsUser: 1337
        runAsGroup: 1337
{{- end }}
        runAsNonRoot: true
{{- end }}
      serviceAccountName: {{ $gateway.name }}-service-account
{{- if .Values.global.priorityClassName }}
      priorityClassName: "{{ .Values.global.priorityClassName }}"
{{- end }}
{{- if .Values.global.proxy.enableCoreDump }}
      initContainers:
        - name: enable-core-dump
{{- if contains "/" .Values.global.proxy.image }}
          image: "{{ .Values.global.proxy.image }}"
{{- else }}
          image: "{{ .Values.global.hub }}/{{ .Values.global.proxy.image | default "proxyv2" }}:{{ .Values.global.tag }}{{with (.Values.global.proxy.variant | default .Values.global.variant)}}-{{.}}{{end}}"
{{- end }}
{{- if .Values.global.imagePullPolicy }}
          imagePullPolicy: {{ .Values.global.imagePullPolicy }}
{{- end }}
          command:
            - /bin/sh
          args:
            - -c
            - sysctl -w kernel.core_pattern=/var/lib/istio/data/core.proxy && ulimit -c unlimited
          securityContext:
            runAsUser: 0
            runAsGroup: 0
            runAsNonRoot: false
            privileged: true
{{- end }}
      containers:
        - name: istio-proxy
{{- if contains "/" .Values.global.proxy.image }}
          image: "{{ .Values.global.proxy.image }}"
{{- else }}
          image: "{{ .Values.global.hub }}/{{ .Values.global.proxy.image | default "proxyv2" }}:{{ .Values.global.tag }}{{with (.Values.global.proxy.variant | default .Values.global.variant)}}-{{.}}{{end}}"
{{- end }}
{{- if .Values.global.imagePullPolicy }}
          imagePullPolicy: {{ .Values.global.imagePullPolicy }}
{{- end }}
          ports:
            {{- range $key, $val := $gateway.ports }}
            - containerPort: {{ $val.targetPort | default $val.port }}
              protocol: {{ $val.protocol | default "TCP" }}
            {{- end }}
            - containerPort: 15090
              protocol: TCP
              name: http-envoy-prom
          args:
          - proxy
          - router
          - --domain
          - $(POD_NAMESPACE).svc.{{ .Values.global.proxy.clusterDomain }}
        {{- if .Values.global.proxy.logLevel }}
          - --proxyLogLevel={{ .Values.global.proxy.logLevel }}
        {{- end}}
        {{- if .Values.global.proxy.componentLogLevel }}
          - --proxyComponentLogLevel={{ .Values.global.proxy.componentLogLevel }}
        {{- end}}
        {{- if .Values.global.logging.level }}
          - --log_output_level={{ .Values.global.logging.level }}
        {{- end}}
        {{- if .Values.global.logAsJson }}
          - --log_as_json
        {{- end }}
        {{- if .Values.global.sts.servicePort }}
          - --stsPort={{ .Values.global.sts.servicePort }}
        {{- end }}
        {{- if not $gateway.runAsRoot }}
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: true
        {{- end }}
          readinessProbe:
            failureThreshold: 30
            httpGet:
              path: /healthz/ready
              port: 15021
              scheme: HTTP
            initialDelaySeconds: 1
            periodSeconds: 2
            successThreshold: 1
            timeoutSeconds: 1
          resources:
{{- if $gateway.resources }}
{{ toYaml $gateway.resources | indent 12 }}
{{- else }}
{{ toYaml .Values.global.defaultResources | indent 12 }}
{{- end }}
          env:
          - name: JWT_POLICY
            value: {{ .Values.global.jwtPolicy }}
          - name: PILOT_CERT_PROVIDER
            value: {{ .Values.global.pilotCertProvider }}
          - name: CA_ADDR
          {{- if .Values.global.caAddress }}
            value: {{ .Values.global.caAddress }}
          {{- else }}
            value: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}.{{ .Values.global.istioNamespace }}.svc:15012
          {{- end }}
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          - name: POD_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.name
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: INSTANCE_IP
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: status.podIP
          - name: HOST_IP
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: status.hostIP
          - name: ISTIO_CPU_LIMIT
            valueFrom:
              resourceFieldRef:
                resource: limits.cpu
          - name: SERVICE_ACCOUNT
            valueFrom:
              fieldRef:
                fieldPath: spec.serviceAccountName
          - name: ISTIO_META_WORKLOAD_NAME
            value: {{ $gateway.name }}
          - name: ISTIO_META_OWNER
            value: kubernetes://apis/apps/v1/namespaces/{{ .Release.Namespace }}/deployments/{{ $gateway.name }}
          {{- if $.Values.global.meshID }}
          - name: ISTIO_META_MESH_ID
            value: "{{ $.Values.global.meshID }}"
          {{- else if .Values.meshConfig.trustDomain }}
          - name: ISTIO_META_MESH_ID
            value: "{{ .Values.meshConfig.trustDomain }}"
          {{- end }}
          {{- if .Values.meshConfig.trustDomain }}
          - name: TRUST_DOMAIN
            value: "{{ .Values.meshConfig.trustDomain }}"
          {{- end }}
          {{- if not $gateway.runAsRoot }}
          - name: ISTIO_META_UNPRIVILEGED_POD
            value: "true"
          {{- end }}
          {{- range $key, $val := $gateway.env }}
          - name: {{ $key }}
            value: "{{ $val }}"
          {{- end }}
          {{- range $key, $value := .Values.meshConfig.defaultConfig.proxyMetadata }}
          - name: {{ $key }}
            value: "{{ $value }}"
          {{- end }}
          {{- $network_set := index $gateway.env "ISTIO_META_NETWORK" }}
          {{- if and (not $network_set) .Values.global.network }}
          - name: ISTIO_META_NETWORK
            value: "{{ .Values.global.network }}"
          {{- end }}
          - name: ISTIO_META_CLUSTER_ID
            value: "{{ $.Values.global.multiCluster.clusterName | default `Kubernetes` }}"
          - name: ISTIO_META_NODE_NAME
            valueFrom:
              fieldRef:
                fieldPath: spec.nodeName
          volumeMounts:
          - name: workload-socket
            mountPath: /var/run/secrets/workload-spiffe-uds
          - name: credential-socket
            mountPath: /var/run/secrets/credential-uds
          - name: workload-certs
            mountPath: /var/run/secrets/workload-spiffe-credentials
          - name: istio-envoy
            mountPath: /etc/istio/proxy
          - name: config-volume
            mountPath: /etc/istio/config
{{- if eq .Values.global.pilotCertProvider "istiod" }}
          - mountPath: /var/run/secrets/istio
            name: istiod-ca-cert
{{- end }}
{{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
          - name: istio-token
            mountPath: /var/run/secrets/tokens
            readOnly: true
{{- end }}
          {{- if .Values.global.mountMtlsCerts }}
          # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
          - name: istio-certs
            mountPath: /etc/certs
            readOnly: true
          {{- end }}
          - mountPath: /var/lib/istio/data
            name: istio-data
          - name: podinfo
            mountPath: /etc/istio/pod
          {{- range $gateway.secretVolumes }}
          - name: {{ .name }}
            mountPath: {{ .mountPath | quote }}
            readOnly: true
          {{- end }}
          {{- range $gateway.configVolumes }}
          {{- if .mountPath }}
          - name: {{ .name }}
            mountPath: {{ .mountPath | quote }}
            readOnly: true
          {{- end }}
          {{- end }}
{{- if $gateway.additionalContainers }}
{{ toYaml $gateway.additionalContainers | indent 8 }}
{{- end }}
      volumes:
      - emptyDir: {}
        name: workload-socket
      - emptyDir: {}
        name: credential-socket
      - emptyDir: {}
        name: workload-certs
{{- if eq .Values.global.pilotCertProvider "istiod" }}
      - name: istiod-ca-cert
        configMap:
          name: istio-ca-root-cert
{{- end }}
      - name: podinfo
        downwardAPI:
          items:
            - path: "labels"
              fieldRef:
                fieldPath: metadata.labels
            - path: "annotations"
              fieldRef:
                fieldPath: metadata.annotations
      - name: istio-envoy
        emptyDir: {}
      - name: istio-data
        emptyDir: {}
{{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
      - name: istio-token
        projected:
          sources:
          - serviceAccountToken:
              path: istio-token
              expirationSeconds: 43200
              audience: {{ .Values.global.sds.token.aud }}
{{- end }}
      {{- if .Values.global.mountMtlsCerts }}
      # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
      - name: istio-certs
        secret:
          secretName: istio.istio-ingressgateway-service-account
          optional: true
      {{- end }}
      - name: config-volume
        configMap:
          name: istio{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
          optional: true
      {{- range $gateway.secretVolumes }}
      - name: {{ .name }}
        secret:
          secretName: {{ .secretName | quote }}
          optional: true
      {{- end }}
      {{- range $gateway.configVolumes }}
      - name: {{ .name }}
        configMap:
          name: {{ .configMapName | quote }}
          optional: true
      {{- end }}
      affinity:
{{ include "nodeaffinity" (dict "global" .Values.global "nodeSelector" $gateway.nodeSelector) | trim | indent 8 }}
      {{- include "podAntiAffinity" $gateway | indent 6 }}
{{- if $gateway.tolerations }}
      tolerations:
{{ toYaml $gateway.tolerations | indent 6 }}
{{- else if .Values.global.defaultTolerations }}
      tolerations:
{{ toYaml .Values.global.defaultTolerations | indent 6 }}
{{- end }}
{{- end }}
