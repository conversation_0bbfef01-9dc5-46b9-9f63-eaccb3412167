{{- if .Values.global.defaultPodDisruptionBudget.enabled }}
{{ $gateway := index .Values "gateways" "istio-ingressgateway" }}
{{- if (semverCompare ">=1.21-0" .Capabilities.KubeVersion.GitVersion) }}
apiVersion: policy/v1
{{- else }}
apiVersion: policy/v1beta1
{{- end }}
kind: PodDisruptionBudget
metadata:
  name: {{ $gateway.name }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ $gateway.labels | toYaml | trim | indent 4 }}
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "IngressGateways"
spec:
  minAvailable: 1
  selector:
    matchLabels:
{{ $gateway.labels | toYaml | trim | indent 6 }}
{{- end }}
