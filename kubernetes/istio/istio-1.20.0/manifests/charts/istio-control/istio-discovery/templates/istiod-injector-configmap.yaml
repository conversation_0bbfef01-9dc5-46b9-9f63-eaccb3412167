{{- if not .Values.global.omitSidecarInjectorConfigMap }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: istio-sidecar-injector{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  namespace: {{ .Release.Namespace }}
  labels:
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Pilot"
    release: {{ .Release.Name }}
data:
{{/* Scope the values to just top level fields used in the template, to reduce the size. */}}
  values: |-
{{ pick .Values "global" "istio_cni" "sidecarInjectorWebhook" "revision" | toPrettyJson | indent 4 }}

  # To disable injection: use omitSidecarInjectorConfigMap, which disables the webhook patching
  # and istiod webhook functionality.
  #
  # New fields should not use Values - it is a 'primary' config object, users should be able
  # to fine tune it or use it with kube-inject.
  config: |-
    # defaultTemplates defines the default template to use for pods that do not explicitly specify a template
    {{- if .Values.sidecarInjectorWebhook.defaultTemplates }}
    defaultTemplates:
{{- range .Values.sidecarInjectorWebhook.defaultTemplates}}
    - {{ . }}
{{- end }}
    {{- else }}
    defaultTemplates: [sidecar]
    {{- end }}
    policy: {{ .Values.global.proxy.autoInject }}
    alwaysInjectSelector:
{{ toYaml .Values.sidecarInjectorWebhook.alwaysInjectSelector | trim | indent 6 }}
    neverInjectSelector:
{{ toYaml .Values.sidecarInjectorWebhook.neverInjectSelector | trim | indent 6 }}
    injectedAnnotations:
      {{- range $key, $val := .Values.sidecarInjectorWebhook.injectedAnnotations }}
      "{{ $key }}": {{ $val | quote }}
      {{- end }}
    {{- /* If someone ends up with this new template, but an older Istiod image, they will attempt to render this template
         which will fail with "Pod injection failed: template: inject:1: function "Istio_1_9_Required_Template_And_Version_Mismatched" not defined".
         This should make it obvious that their installation is broken.
     */}}
    template: {{ `{{ Template_Version_And_Istio_Version_Mismatched_Check_Installation }}` | quote }}
    templates:
{{- if not (hasKey .Values.sidecarInjectorWebhook.templates "sidecar") }}
      sidecar: |
{{ .Files.Get "files/injection-template.yaml" | trim | indent 8 }}
{{- end }}
{{- if not (hasKey .Values.sidecarInjectorWebhook.templates "gateway") }}
      gateway: |
{{ .Files.Get "files/gateway-injection-template.yaml" | trim | indent 8 }}
{{- end }}
{{- if not (hasKey .Values.sidecarInjectorWebhook.templates "grpc-simple") }}
      grpc-simple: |
{{ .Files.Get "files/grpc-simple.yaml" | trim | indent 8 }}
{{- end }}
{{- if not (hasKey .Values.sidecarInjectorWebhook.templates "grpc-agent") }}
      grpc-agent: |
{{ .Files.Get "files/grpc-agent.yaml" | trim | indent 8 }}
{{- end }}
{{- if not (hasKey .Values.sidecarInjectorWebhook.templates "waypoint") }}
      waypoint: |
{{ .Files.Get "files/waypoint.yaml" | trim | indent 8 }}
{{- end }}
{{- if not (hasKey .Values.sidecarInjectorWebhook.templates "kube-gateway") }}
      kube-gateway: |
{{ .Files.Get "files/kube-gateway.yaml" | trim | indent 8 }}
{{- end }}
{{- with .Values.sidecarInjectorWebhook.templates }}
{{ toYaml . | trim | indent 6 }}
{{- end }}

{{- end }}
