{{- if .Values.pilot.jwksResolverExtraRootCA }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: pilot-jwks-extra-cacerts{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  namespace: {{ .Release.Namespace }}
  labels:
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Pilot"
data:
  extra.pem: {{ .Values.pilot.jwksResolverExtraRootCA | quote }}
{{- end }}
