apiVersion: v1
kind: Service
metadata:
  name: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  namespace: {{ .Release.Namespace }}
  {{- if .Values.pilot.serviceAnnotations }}
  annotations:
{{ toYaml .Values.pilot.serviceAnnotations | indent 4 }}
  {{- end }}
  labels:
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Pilot"
    app: istiod
    istio: pilot
    release: {{ .Release.Name }}
spec:
  ports:
    - port: 15010
      name: grpc-xds # plaintext
      protocol: TCP
    - port: 15012
      name: https-dns # mTLS with k8s-signed cert
      protocol: TCP
    - port: 443
      name: https-webhook # validation and injection
      targetPort: 15017
      protocol: TCP
    - port: 15014
      name: http-monitoring # prometheus stats
      protocol: TCP
  selector:
    app: istiod
    {{- if ne .Values.revision "" }}
    istio.io/rev: {{ .Values.revision }}
    {{- else }}
    # Label used by the 'default' service. For versioned deployments we match with app and version.
    # This avoids default deployment picking the canary
    istio: pilot
    {{- end }}
  {{- if .Values.pilot.ipFamilyPolicy }}
  ipFamilyPolicy: {{ .Values.pilot.ipFamilyPolicy }}
  {{- end }}
  {{- if .Values.pilot.ipFamilies }}
  ipFamilies:
  {{- range .Values.pilot.ipFamilies }}
  - {{ . }}
  {{- end }}
  {{- end }}
---
