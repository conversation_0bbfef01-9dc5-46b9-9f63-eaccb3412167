{{- if .Values.global.defaultPodDisruptionBudget.enabled }}
{{- if (semverCompare ">=1.21-0" .Capabilities.KubeVersion.GitVersion) }}
apiVersion: policy/v1
{{- else }}
apiVersion: policy/v1beta1
{{- end }}
kind: PodDisruptionBudget
metadata:
  name: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: istiod
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Pilot"
    release: {{ .Release.Name }}
    istio: pilot
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: istiod
      {{- if ne .Values.revision "" }}
      istio.io/rev: {{ .Values.revision }}
      {{- else }}
      istio: pilot
      {{- end }}
---
{{- end }}
