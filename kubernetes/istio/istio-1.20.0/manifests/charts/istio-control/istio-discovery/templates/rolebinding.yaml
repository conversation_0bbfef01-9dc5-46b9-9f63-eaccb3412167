apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istiod{{- if not (eq .Values.revision "")}}-{{ .Values.revision }}{{- end }}
  namespace: {{ .Values.global.istioNamespace }}
  labels:
    app: istiod
    release: {{ .Release.Name }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istiod{{- if not (eq .Values.revision "")}}-{{ .Values.revision }}{{- end }}
subjects:
  - kind: ServiceAccount
    name: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
    namespace: {{ .Values.global.istioNamespace }}
