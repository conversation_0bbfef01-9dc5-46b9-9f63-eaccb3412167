{{- $containers := list }}
{{- range $index, $container := .Spec.Containers }}{{ if not (eq $container.Name "istio-proxy") }}{{ $containers = append $containers $container.Name }}{{end}}{{- end}}
metadata:
  labels:
    service.istio.io/canonical-name: {{ index .ObjectMeta.Labels `service.istio.io/canonical-name` | default (index .ObjectMeta.Labels `app.kubernetes.io/name`) | default (index .ObjectMeta.Labels `app`) | default .DeploymentMeta.Name  | quote }}
    service.istio.io/canonical-revision: {{ index .ObjectMeta.Labels `service.istio.io/canonical-revision` | default (index .ObjectMeta.Labels `app.kubernetes.io/version`) | default (index .ObjectMeta.Labels `version`) | default "latest"  | quote }}
  annotations: {
    istio.io/rev: {{ .Revision | default "default" | quote }},
    {{- if eq (len $containers) 1 }}
    kubectl.kubernetes.io/default-logs-container: "{{ index $containers 0 }}",
    kubectl.kubernetes.io/default-container: "{{ index $containers 0 }}",
    {{ end }}
  }
spec:
  containers:
  - name: istio-proxy
  {{- if contains "/" .Values.global.proxy.image }}
    image: "{{ annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy.image }}"
  {{- else }}
    image: "{{ .ProxyImage }}"
  {{- end }}
    ports:
    - containerPort: 15090
      protocol: TCP
      name: http-envoy-prom
    args:
    - proxy
    - router
    - --domain
    - $(POD_NAMESPACE).svc.{{ .Values.global.proxy.clusterDomain }}
    - --proxyLogLevel={{ annotation .ObjectMeta `sidecar.istio.io/logLevel` .Values.global.proxy.logLevel }}
    - --proxyComponentLogLevel={{ annotation .ObjectMeta `sidecar.istio.io/componentLogLevel` .Values.global.proxy.componentLogLevel }}
    - --log_output_level={{ annotation .ObjectMeta `sidecar.istio.io/agentLogLevel` .Values.global.logging.level }}
  {{- if .Values.global.sts.servicePort }}
    - --stsPort={{ .Values.global.sts.servicePort }}
  {{- end }}
  {{- if .Values.global.logAsJson }}
    - --log_as_json
  {{- end }}
  {{- if .Values.global.proxy.lifecycle }}
    lifecycle:
      {{ toYaml .Values.global.proxy.lifecycle | indent 6 }}
  {{- end }}
    securityContext:
      runAsUser: {{ .ProxyUID | default "1337" }}
      runAsGroup: {{ .ProxyGID | default "1337" }}
    env:
    - name: JWT_POLICY
      value: {{ .Values.global.jwtPolicy }}
    - name: PILOT_CERT_PROVIDER
      value: {{ .Values.global.pilotCertProvider }}
    - name: CA_ADDR
    {{- if .Values.global.caAddress }}
      value: {{ .Values.global.caAddress }}
    {{- else }}
      value: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}.{{ .Values.global.istioNamespace }}.svc:15012
    {{- end }}
    - name: POD_NAME
      valueFrom:
        fieldRef:
          fieldPath: metadata.name
    - name: POD_NAMESPACE
      valueFrom:
        fieldRef:
          fieldPath: metadata.namespace
    - name: INSTANCE_IP
      valueFrom:
        fieldRef:
          fieldPath: status.podIP
    - name: SERVICE_ACCOUNT
      valueFrom:
        fieldRef:
          fieldPath: spec.serviceAccountName
    - name: HOST_IP
      valueFrom:
        fieldRef:
          fieldPath: status.hostIP
    - name: ISTIO_CPU_LIMIT
      valueFrom:
        resourceFieldRef:
          resource: limits.cpu
    - name: PROXY_CONFIG
      value: |
             {{ protoToJSON .ProxyConfig }}
    - name: ISTIO_META_POD_PORTS
      value: |-
        [
        {{- $first := true }}
        {{- range $index1, $c := .Spec.Containers }}
          {{- range $index2, $p := $c.Ports }}
            {{- if (structToJSON $p) }}
            {{if not $first}},{{end}}{{ structToJSON $p }}
            {{- $first = false }}
            {{- end }}
          {{- end}}
        {{- end}}
        ]
    - name: GOMEMLIMIT
      valueFrom:
        resourceFieldRef:
          resource: limits.memory
    - name: GOMAXPROCS
      valueFrom:
        resourceFieldRef:
          resource: limits.cpu
    - name: ISTIO_META_APP_CONTAINERS
      value: "{{ $containers | join "," }}"
    - name: ISTIO_META_CLUSTER_ID
      value: "{{ valueOrDefault .Values.global.multiCluster.clusterName `Kubernetes` }}"
    - name: ISTIO_META_NODE_NAME
      valueFrom:
        fieldRef:
          fieldPath: spec.nodeName
    - name: ISTIO_META_INTERCEPTION_MODE
      value: "{{ .ProxyConfig.InterceptionMode.String }}"
    {{- if .Values.global.network }}
    - name: ISTIO_META_NETWORK
      value: "{{ .Values.global.network }}"
    {{- end }}
    {{- if .DeploymentMeta.Name }}
    - name: ISTIO_META_WORKLOAD_NAME
      value: "{{ .DeploymentMeta.Name }}"
    {{ end }}
    {{- if and .TypeMeta.APIVersion .DeploymentMeta.Name }}
    - name: ISTIO_META_OWNER
      value: kubernetes://apis/{{ .TypeMeta.APIVersion }}/namespaces/{{ valueOrDefault .DeploymentMeta.Namespace `default` }}/{{ toLower .TypeMeta.Kind}}s/{{ .DeploymentMeta.Name }}
    {{- end}}
    {{- if .Values.global.meshID }}
    - name: ISTIO_META_MESH_ID
      value: "{{ .Values.global.meshID }}"
    {{- else if (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}
    - name: ISTIO_META_MESH_ID
      value: "{{ (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}"
    {{- end }}
    {{- with (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain)  }}
    - name: TRUST_DOMAIN
      value: "{{ . }}"
    {{- end }}
    {{- range $key, $value := .ProxyConfig.ProxyMetadata }}
    - name: {{ $key }}
      value: "{{ $value }}"
    {{- end }}
    {{with .Values.global.imagePullPolicy }}imagePullPolicy: "{{.}}"{{end}}
    readinessProbe:
      httpGet:
        path: /healthz/ready
        port: 15021
      initialDelaySeconds: {{.Values.global.proxy.readinessInitialDelaySeconds }}
      periodSeconds: {{ .Values.global.proxy.readinessPeriodSeconds }}
      timeoutSeconds: 3
      failureThreshold: {{ .Values.global.proxy.readinessFailureThreshold }}
    volumeMounts:
    - name: workload-socket
      mountPath: /var/run/secrets/workload-spiffe-uds
    - name: credential-socket
      mountPath: /var/run/secrets/credential-uds
    {{- if eq .Values.global.caName "GkeWorkloadCertificate" }}
    - name: gke-workload-certificate
      mountPath: /var/run/secrets/workload-spiffe-credentials
      readOnly: true
    {{- else }}
    - name: workload-certs
      mountPath: /var/run/secrets/workload-spiffe-credentials
    {{- end }}
    {{- if eq .Values.global.pilotCertProvider "istiod" }}
    - mountPath: /var/run/secrets/istio
      name: istiod-ca-cert
    {{- end }}
    - mountPath: /var/lib/istio/data
      name: istio-data
    # SDS channel between istioagent and Envoy
    - mountPath: /etc/istio/proxy
      name: istio-envoy
    {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
    - mountPath: /var/run/secrets/tokens
      name: istio-token
    {{- end }}
    {{- if .Values.global.mountMtlsCerts }}
    # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
    - mountPath: /etc/certs/
      name: istio-certs
      readOnly: true
    {{- end }}
    - name: istio-podinfo
      mountPath: /etc/istio/pod
  volumes:
  - emptyDir: {}
    name: workload-socket
  - emptyDir: {}
    name: credential-socket
  {{- if eq .Values.global.caName "GkeWorkloadCertificate" }}
  - name: gke-workload-certificate
    csi:
      driver: workloadcertificates.security.cloud.google.com
  {{- else}}
  - emptyDir: {}
    name: workload-certs
  {{- end }}
  # SDS channel between istioagent and Envoy
  - emptyDir:
      medium: Memory
    name: istio-envoy
  - name: istio-data
    emptyDir: {}
  - name: istio-podinfo
    downwardAPI:
      items:
        - path: "labels"
          fieldRef:
            fieldPath: metadata.labels
        - path: "annotations"
          fieldRef:
            fieldPath: metadata.annotations
  {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
  - name: istio-token
    projected:
      sources:
      - serviceAccountToken:
          path: istio-token
          expirationSeconds: 43200
          audience: {{ .Values.global.sds.token.aud }}
  {{- end }}
  {{- if eq .Values.global.pilotCertProvider "istiod" }}
  - name: istiod-ca-cert
    configMap:
      name: istio-ca-root-cert
  {{- end }}
  {{- if .Values.global.mountMtlsCerts }}
  # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
  - name: istio-certs
    secret:
      optional: true
      {{ if eq .Spec.ServiceAccountName "" }}
      secretName: istio.default
      {{ else -}}
      secretName: {{  printf "istio.%s" .Spec.ServiceAccountName }}
      {{  end -}}
  {{- end }}
  {{- if .Values.global.imagePullSecrets }}
  imagePullSecrets:
    {{- range .Values.global.imagePullSecrets }}
    - name: {{ . }}
    {{- end }}
  {{- end }}
