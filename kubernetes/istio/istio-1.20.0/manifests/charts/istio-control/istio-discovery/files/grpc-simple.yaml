metadata:
  annotations:
    sidecar.istio.io/rewriteAppHTTPProbers: "false"
spec:
  initContainers:
    - name: grpc-bootstrap-init
      image: busybox:1.28
      volumeMounts:
        - mountPath: /var/lib/grpc/data/
          name: grpc-io-proxyless-bootstrap
      env:
        - name: INSTANCE_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: ISTIO_NAMESPACE
          value: |
             {{ .Values.global.istioNamespace }}
      command:
        - sh
        - "-c"
        - |-
          NODE_ID="sidecar~${INSTANCE_IP}~${POD_NAME}.${POD_NAMESPACE}~cluster.local"
          SERVER_URI="dns:///istiod.${ISTIO_NAMESPACE}.svc:15010"
          echo '
          {
            "xds_servers": [
              {
                "server_uri": "'${SERVER_URI}'",
                "channel_creds": [{"type": "insecure"}],
                "server_features" : ["xds_v3"]
              }
            ],
            "node": {
              "id": "'${NODE_ID}'",
              "metadata": {
                "GENERATOR": "grpc"
              }
            }
          }' > /var/lib/grpc/data/bootstrap.json
  containers:
  {{- range $index, $container := .Spec.Containers }}
  - name: {{ $container.Name }}
    env:
      - name: GRPC_XDS_BOOTSTRAP
        value: /var/lib/grpc/data/bootstrap.json
      - name: GRPC_GO_LOG_VERBOSITY_LEVEL
        value: "99"
      - name: GRPC_GO_LOG_SEVERITY_LEVEL
        value: info
    volumeMounts:
      - mountPath: /var/lib/grpc/data/
        name: grpc-io-proxyless-bootstrap
  {{- end }}
  volumes:
    - name: grpc-io-proxyless-bootstrap
      emptyDir: {}
