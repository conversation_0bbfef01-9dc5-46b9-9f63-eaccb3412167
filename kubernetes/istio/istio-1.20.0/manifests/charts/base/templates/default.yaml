{{- if not (eq .Values.defaultRevision "") }}
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: istiod-default-validator
  labels:
    app: istiod
    release: {{ .Release.Name }}
    istio: istiod
    istio.io/rev: {{ .Values.defaultRevision }}
webhooks:
  - name: validation.istio.io
    clientConfig:
      {{- if .Values.base.validationURL }}
      url: {{ .Values.base.validationURL }}
      {{- else }}
      service:
        {{- if (eq .Values.defaultRevision "default") }}
        name: istiod
        {{- else }}
        name: istiod-{{ .Values.defaultRevision }}
        {{- end }}
        namespace: {{ .Values.global.istioNamespace }}
        path: "/validate"
      {{- end }}
    rules:
      - operations:
          - CREATE
          - UPDATE
        apiGroups:
          - security.istio.io
          - networking.istio.io
          - telemetry.istio.io
          - extensions.istio.io
          {{- if .Values.base.validateGateway }}
          - gateway.networking.k8s.io
          {{- end }}
        apiVersions:
          - "*"
        resources:
          - "*"
    # Fail open until the validation webhook is ready. The webhook controller
    # will update this to `Fail` and patch in the `caBundle` when the webhook
    # endpoint is ready.
    failurePolicy: Ignore
    sideEffects: None
    admissionReviewVersions: ["v1beta1", "v1"]
{{- end }}
