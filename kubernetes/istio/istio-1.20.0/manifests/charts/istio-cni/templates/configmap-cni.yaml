{{- $defaultBinDir :=
    (.Capabilities.KubeVersion.GitVersion | contains "-gke") | ternary
      "/home/<USER>/bin"
      "/opt/cni/bin"
}}
kind: ConfigMap
apiVersion: v1
metadata:
  name: istio-cni-config
  namespace: {{ .Release.Namespace }}
  labels:
    app: istio-cni
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Cni"
data:
  # The CNI network configuration to add to the plugin chain on each node.  The special
  # values in this config will be automatically populated.
  cni_network_config: |-
        {
          "cniVersion": "0.3.1",
          "name": "istio-cni",
          "type": "istio-cni",
          "log_level": {{ quote .Values.cni.logLevel }},
          "log_uds_address": "__LOG_UDS_ADDRESS__",
          {{if .Values.cni.ambient.enabled}}"ambient_enabled": true,{{end}}
          "kubernetes": {
              "kubeconfig": "__KUBECONFIG_FILEPATH__",
              "cni_bin_dir": {{ .Values.cni.cniBinDir | default $defaultBinDir | quote }},
              "exclude_namespaces": [ {{ range $idx, $ns := .Values.cni.excludeNamespaces }}{{ if $idx }}, {{ end }}{{ quote $ns }}{{ end }} ]
          }
        }
