apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: istio-cni
  labels:
    app: istio-cni
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Cni"
rules:
- apiGroups: [""]
  resources: ["pods","nodes","namespaces"]
  verbs: ["get", "list", "watch"]
---
{{- if .Values.cni.repair.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: istio-cni-repair-role
  labels:
    app: istio-cni
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Cni"
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch", "delete", "patch", "update" ]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch", "delete", "patch", "update", "create" ]
{{- end }}
