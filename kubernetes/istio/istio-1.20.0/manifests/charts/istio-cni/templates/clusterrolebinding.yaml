apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: istio-cni
  labels:
    app: istio-cni
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Cni"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: istio-cni
subjects:
- kind: ServiceAccount
  name: istio-cni
  namespace: {{ .Release.Namespace }}
---
{{- if .Values.cni.repair.enabled }}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: istio-cni-repair-rolebinding
  labels:
    k8s-app: istio-cni-repair
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Cni"
subjects:
- kind: ServiceAccount
  name: istio-cni
  namespace: {{ .Release.Namespace}}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: istio-cni-repair-role
{{- end }}
---
{{- if ne .Values.cni.psp_cluster_role "" }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istio-cni-psp
  namespace: {{ .Release.Namespace }}
  labels:
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Cni"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ .Values.cni.psp_cluster_role }}
subjects:
- kind: ServiceAccount
  name: istio-cni
  namespace: {{ .Release.Namespace }}
{{- end }}
