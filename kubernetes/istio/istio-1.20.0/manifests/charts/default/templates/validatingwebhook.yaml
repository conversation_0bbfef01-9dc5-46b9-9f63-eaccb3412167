apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: istiod-default-validator
  labels:
    app: istiod
    istio: istiod
    istio.io/rev: {{ .Values.revision | default "default" }}
    istio.io/tag: "default"
    # Required to make sure this resource is removed
    # when purging Istio resources
    operator.istio.io/component: Pilot
webhooks:
  - name: validation.istio.io
    clientConfig:
      {{- if .Values.base.validationURL }}
      url: {{ .Values.base.validationURL }}
      {{- else }}
      service:
        name: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
        namespace: {{ .Values.global.istioNamespace }}
        path: "/validate"
      {{- end }}
    rules:
      - operations:
          - CREATE
          - UPDATE
        apiGroups:
          - security.istio.io
          - networking.istio.io
          - telemetry.istio.io
          - extensions.istio.io
          {{- if .Values.base.validateGateway }}
          - gateway.networking.k8s.io
          {{- end }}
        apiVersions:
          - "*"
        resources:
          - "*"
    failurePolicy: Ignore
    sideEffects: None
    admissionReviewVersions: ["v1beta1", "v1"]
    objectSelector:
      matchExpressions:
        - key: istio.io/rev
          operator: DoesNotExist
---
