{{- if not (eq .Values.service.type "None") }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "gateway.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "gateway.labels" . | nindent 4 }}
    {{- with .Values.networkGateway }}
    topology.istio.io/network: "{{.}}"
    {{- end }}
  annotations:
    {{- merge (deepCopy .Values.service.annotations) .Values.annotations | toYaml | nindent 4 }}
spec:
{{- with .Values.service.loadBalancerIP }}
  loadBalancerIP: "{{ . }}"
{{- end }}
{{- if .Values.service.ipFamilyPolicy }}
  ipFamilyPolicy: {{ .Values.service.ipFamilyPolicy }}
{{- end }}
{{- if .Values.service.ipFamilies }}
  ipFamilies:
{{- range .Values.service.ipFamilies }}
  - {{ . }}
{{- end }}
{{- end }}
{{- with .Values.service.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
{{ toYaml . | indent 4 }}
{{- end }}
{{- with .Values.service.externalTrafficPolicy }}
  externalTrafficPolicy: "{{ . }}"
{{- end }}
  type: {{ .Values.service.type }}
  ports:
{{- if .Values.networkGateway }}
  - name: status-port
    port: 15021
    targetPort: 15021
  - name: tls
    port: 15443
    targetPort: 15443
  - name: tls-istiod
    port: 15012
    targetPort: 15012
  - name: tls-webhook
    port: 15017
    targetPort: 15017
{{- else }}
{{ .Values.service.ports | toYaml | indent 4 }}
{{- end }}
{{- if .Values.service.externalIPs }}
  externalIPs: {{- range .Values.service.externalIPs }}
    - {{.}}
  {{- end }}
{{- end }}
  selector:
    {{- include "gateway.selectorLabels" . | nindent 4 }}
{{- end }}
