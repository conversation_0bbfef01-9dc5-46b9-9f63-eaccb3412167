{{- if .Values.ingress.enabled -}}
{{- $fullName := printf "%s-app" .Values.customer.id -}}
{{- $svcPort := .Values.service.port -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ $fullName }}
  namespace: customer-{{ .Values.customer.id }}
  labels:
    app: {{ .Values.customer.id }}-app
    customer: {{ .Values.customer.id }}
    environment: {{ .Values.customer.environment | lower }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ .host }}
      http:
        paths:
          {{- range .paths }}
          - path: {{ .path }}
            pathType: {{ .pathType }}
            backend:
              service:
                name: {{ $fullName }}
                port:
                  number: {{ $svcPort }}
          {{- end }}
    {{- end }}
{{- end }}
