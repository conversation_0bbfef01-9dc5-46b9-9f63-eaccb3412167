apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.customer.id }}-app
  namespace: customer-{{ .Values.customer.id }}
  labels:
    app: {{ .Values.customer.id }}-app
    customer: {{ .Values.customer.id }}
    environment: {{ .Values.customer.environment | lower }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.customer.id }}-app
  template:
    metadata:
      labels:
        app: {{ .Values.customer.id }}-app
        customer: {{ .Values.customer.id }}
        environment: {{ .Values.customer.environment | lower }}
    spec:
      serviceAccountName: {{ .Values.serviceAccount.name }}
      containers:
        - name: app
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: CUSTOMER_ID
              value: {{ .Values.customer.id | quote }}
            - name: CUSTOMER_NAME
              value: {{ .Values.customer.name | quote }}
            - name: ENVIRONMENT
              value: {{ .Values.customer.environment | quote }}
            - name: SUBDOMAIN
              value: {{ .Values.customer.subdomain | quote }}
            - name: DATABASE_HOST
              value: {{ .Values.database.host | quote }}
            - name: DATABASE_NAME
              value: {{ .Values.database.name | quote }}
            - name: DATABASE_USER
              value: {{ .Values.database.username | quote }}
            - name: DATABASE_PASSWORD
              value: {{ .Values.database.password | quote }}
            - name: S3_BUCKET
              value: {{ .Values.s3.bucket | quote }}
            - name: S3_REGION
              value: {{ .Values.s3.region | quote }}
            - name: DMS
              value: {{ .Values.customer.dms | quote }}
            - name: DELPHI_ENABLED
              value: {{ .Values.customer.delphi | quote }}
            - name: EXTERNAL_API_ENABLED
              value: {{ .Values.customer.externalApi | quote }}
            - name: DOCUMENT_CLASS_SET
              value: {{ .Values.customer.documentClassSet | quote }}
            - name: NAMING_SUGGESTION_LANGUAGE
              value: {{ .Values.customer.language | quote }}
            - name: REFERENCE_DATA_MATCHING
              value: {{ .Values.customer.referenceDataMatching | quote }}
            - name: HEAP_TRACKING
              value: {{ .Values.customer.heapTracking | quote }}
          volumeMounts:
            - name: data
              mountPath: /data
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 5
            periodSeconds: 5
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: {{ .Values.customer.id }}-data
      securityContext:
        {{- toYaml .Values.securityContext | nindent 8 }}
