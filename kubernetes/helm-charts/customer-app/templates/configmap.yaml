apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .Values.customer.id }}-migrations
  namespace: customer-{{ .Values.customer.id }}
  labels:
    app: {{ .Values.customer.id }}-app
    customer: {{ .Values.customer.id }}
    environment: {{ .Values.customer.environment | lower }}
data:
  V1__initial_schema.sql: |
    -- Create users table
    CREATE TABLE IF NOT EXISTS users (
      id INT AUTO_INCREMENT PRIMARY KEY,
      username VARCHAR(255) NOT NULL,
      email VARCHAR(255) NOT NULL,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    );
    
    -- Create documents table
    CREATE TABLE IF NOT EXISTS documents (
      id INT AUTO_INCREMENT PRIMARY KEY,
      title VARCHAR(255) NOT NULL,
      description TEXT,
      s3_key VARCHAR(512) NOT NULL,
      created_by INT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      FOREIGN KEY (created_by) REFERENCES users(id)
    );
    
    -- Create settings table
    CREATE TABLE IF NOT EXISTS settings (
      id INT AUTO_INCREMENT PRIMARY KEY,
      setting_key VARCHAR(255) NOT NULL,
      setting_value TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      UNIQUE KEY unique_setting_key (setting_key)
    );
    
    -- Insert initial settings
    INSERT INTO settings (setting_key, setting_value) VALUES
      ('customer_id', '{{ .Values.customer.id }}'),
      ('customer_name', '{{ .Values.customer.name }}'),
      ('subdomain', '{{ .Values.customer.subdomain }}'),
      ('environment', '{{ .Values.customer.environment }}'),
      ('dms', '{{ .Values.customer.dms }}'),
      ('delphi_enabled', '{{ .Values.customer.delphi }}'),
      ('external_api_enabled', '{{ .Values.customer.externalApi }}'),
      ('document_class_set', '{{ .Values.customer.documentClassSet }}'),
      ('naming_suggestion_language', '{{ .Values.customer.language }}'),
      ('reference_data_matching', '{{ .Values.customer.referenceDataMatching }}'),
      ('heap_tracking', '{{ .Values.customer.heapTracking }}');
    
    -- Insert a sample user
    INSERT INTO users (username, email) VALUES
      ('admin', 'admin@{{ .Values.customer.subdomain }}');
    
    -- Insert a sample document
    INSERT INTO documents (title, description, s3_key, created_by) VALUES
      ('Welcome Document', 'Welcome to {{ .Values.customer.name }}', 'README.txt', 1);
