# Default values for customer-app
# This is a YAML-formatted file.

customer:
  id: ""
  name: ""
  subdomain: ""
  environment: "Production"
  dms: "internal"
  delphi: false
  externalApi: false
  documentClassSet: "ArchitraveInternal"
  language: "German"
  referenceDataMatching: false
  heapTracking: false

database:
  host: ""
  name: ""
  username: ""
  password: ""

s3:
  bucket: ""
  region: "eu-central-1"

image:
  repository: nginx
  tag: latest
  pullPolicy: IfNotPresent

service:
  type: ClusterIP
  port: 80

resources:
  requests:
    cpu: 100m
    memory: 128Mi
  limits:
    cpu: 200m
    memory: 256Mi

persistence:
  enabled: true
  size: 10Gi
  storageClass: "gp2"

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
  hosts:
    - host: ""
      paths:
        - path: /*
          pathType: ImplementationSpecific

securityContext:
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000

serviceAccount:
  create: false
  name: "customer-app-sa"
