apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Values.tenant.name }}-flyway
  namespace: tenant-{{ .Values.tenant.id }}
spec:
  template:
    spec:
      containers:
      - name: flyway
        image: flyway/flyway
        command: ["flyway", "migrate"]
        env:
        - name: FLYWAY_URL
          value: "jdbc:{{ .Values.database.url }}"
        - name: FLYWAY_USER
          value: {{ .Values.database.username }}
        - name: FLYWAY_PASSWORD
          value: {{ .Values.database.password }}
        volumeMounts:
        - name: migrations
          mountPath: /flyway/sql
      volumes:
      - name: migrations
        configMap:
          name: {{ .Values.tenant.name }}-migrations
      restartPolicy: OnFailure
  backoffLimit: 4
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.tenant.name }}-app
  namespace: tenant-{{ .Values.tenant.id }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.tenant.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.tenant.name }}
    spec:
      containers:
      - name: app
        image: {{ .Values.image.repository }}:{{ .Values.image.tag }}
        env:
        - name: DB_URL
          value: {{ .Values.database.url }}
        - name: DB_USERNAME
          value: {{ .Values.database.username }}
        - name: DB_PASSWORD
          value: {{ .Values.database.password }}
        - name: ELASTICSEARCH_URL
          value: {{ .Values.elasticsearch.url }}
        resources:
          requests:
            cpu: {{ .Values.resources.requests.cpu }}
            memory: {{ .Values.resources.requests.memory }}
          limits:
            cpu: {{ .Values.resources.limits.cpu }}
            memory: {{ .Values.resources.limits.memory }}